{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\PieChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\PieChart.vue", "mtime": 1753943508732}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\PieChart.vue"], "names": [], "mappings": ";AAKA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC1D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAA<PERSON>;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,EAAE;MACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACvB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3D,CAAC,CAAC,CAAC,CAAC,CAAC;QACP;QACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC;;QAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACb,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACvD,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACT,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cAC5D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACnD;cACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACZ;UACF,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACN;cACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;kBAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;gBACxD,CAAC;gBACD,CAAC,CAAC,CAAC,CAAC,EAAE;kBACJ,CAAC,EAAE;oBACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBACb;gBACF,CAAC;gBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACf,CAAC;cACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACd,CAAC;cACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;gBACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;kBACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClB;cACF,CAAC,CAAC;YACJ;UACF;QACF;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC;IACH;;IAEA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MACrB;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR;EACF;AACF", "file": "D:/zy/xm/h5/qdzx_h5/product-app/src/views/SmartBrainLargeScreen/component/echartsComponent/PieChart.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div :id=\"chartId\" class=\"chart\"></div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, onMounted, onUnmounted, nextTick } from 'vue'\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: 'Pie<PERSON><PERSON>',\r\n  props: {\r\n    id: {\r\n      type: String,\r\n      default: () => ''\r\n    },\r\n    chartData: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    radius: {\r\n      type: Array,\r\n      default: () => ['26%', '42%']\r\n    },\r\n    center: {\r\n      type: Array,\r\n      default: () => ['50%', '35%']\r\n    },\r\n    startAngle: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    showLabel: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    showLegend: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    legendPosition: {\r\n      type: String,\r\n      default: 'bottom'\r\n    }\r\n  },\r\n  setup (props) {\r\n    const chartId = ref(props.id)\r\n    let chartInstance = null\r\n    // 初始化图表\r\n    const initChart = () => {\r\n      nextTick(() => {\r\n        const dom = document.getElementById(chartId.value)\r\n        if (!dom) {\r\n          console.error('Chart DOM element not found:', chartId.value)\r\n          return\r\n        }\r\n        if (!chartInstance) {\r\n          chartInstance = echarts.init(dom)\r\n        }\r\n\r\n        const option = {\r\n          tooltip: {\r\n            trigger: 'item',\r\n            formatter: '{b}: {c} ({d}%)',\r\n            confine: true,\r\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\r\n            borderColor: 'transparent',\r\n            textStyle: {\r\n              color: '#fff',\r\n              fontSize: 12\r\n            },\r\n            extraCssText: 'border-radius: 4px; padding: 8px 12px;'\r\n          },\r\n          legend: {\r\n            show: props.showLegend,\r\n            orient: 'horizontal',\r\n            bottom: 8,\r\n            top: props.legendPosition === 'top' ? 10 : 'auto',\r\n            // left: 30,\r\n            // right: 30,\r\n            textStyle: {\r\n              fontSize: 12,\r\n              color: '#999'\r\n            },\r\n            itemWidth: 18,\r\n            itemHeight: 8,\r\n            itemGap: 22,\r\n            formatter: function (name) {\r\n              const item = props.chartData.find(data => data.name === name)\r\n              if (item) {\r\n                return `${name} ${item.value}人 ${item.percentage}`\r\n              }\r\n              return name\r\n            }\r\n          },\r\n          series: [\r\n            {\r\n              type: 'pie',\r\n              // minAngle: 20,\r\n              radius: props.radius,\r\n              center: props.center,\r\n              startAngle: props.startAngle,\r\n              avoidLabelOverlap: true,\r\n              label: {\r\n                show: props.showLabel,\r\n                position: 'outside',\r\n                formatter: function (params) {\r\n                  return params.name + '\\n' + '{c|' + params.value + '人}'\r\n                },\r\n                rich: {\r\n                  c: {\r\n                    color: '#666',\r\n                    fontSize: 14\r\n                  }\r\n                },\r\n                fontSize: 11,\r\n                color: '#999',\r\n                lineHeight: 18\r\n              },\r\n              labelLine: {\r\n                show: props.showLabel,\r\n                length: 10,\r\n                length2: 20,\r\n                smooth: false\r\n              },\r\n              data: props.chartData.map(item => ({\r\n                value: item.value,\r\n                name: item.name,\r\n                itemStyle: {\r\n                  color: item.color\r\n                }\r\n              }))\r\n            }\r\n          ]\r\n        }\r\n        chartInstance.setOption(option)\r\n      })\r\n    }\r\n\r\n    // 监听窗口大小变化\r\n    const handleResize = () => {\r\n      if (chartInstance) {\r\n        chartInstance.resize()\r\n      }\r\n    }\r\n\r\n    onMounted(() => {\r\n      initChart()\r\n      window.addEventListener('resize', handleResize)\r\n    })\r\n\r\n    onUnmounted(() => {\r\n      if (chartInstance) {\r\n        chartInstance.dispose()\r\n        chartInstance = null\r\n      }\r\n      window.removeEventListener('resize', handleResize)\r\n    })\r\n\r\n    return {\r\n      chartId\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.chart {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n</style>\r\n"]}]}