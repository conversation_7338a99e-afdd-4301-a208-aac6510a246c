{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\Home.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\Home.vue", "mtime": 1753932846834}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\Home.vue"], "names": [], "mappings": ";AAoMA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAErE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAClE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAC3B,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACzB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACzB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAClC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChD,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChD,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/D,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACjE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/D,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjE,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7D,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACpG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrG,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpB;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClC,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClC;MACF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf;UACE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACT,CAAC;QACD;UACE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACT,CAAC;QACD;UACE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACT;MACF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACzB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/B,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC5B,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACvD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACtD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACvD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACtD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACrD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACtD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACtD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACxD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACxD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACvD;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC3B;AACF", "file": "D:/zy/xm/h5/qdzx_h5/product-app/src/views/SmartBrainLargeScreen/component/Home.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"home_page\">\n    <div class=\"map_section\">\n      <MapQingdao :mapData=\"mapData\" />\n    </div>\n    <!-- 委员统计 -->\n    <div class=\"home_committee_statistics\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">委员统计</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">{{ circles }}</span>\n        </div>\n      </div>\n      <div class=\"committee_statistics_box\">\n        <div class=\"statistics_card card_blue\">\n          <div class=\"card_content\">\n            <div class=\"card_number\">{{ cppccMemberNum }}</div>\n            <div class=\"card_label\">政协委员(人)</div>\n          </div>\n        </div>\n        <div class=\"statistics_card card_yellow\">\n          <div class=\"card_content\">\n            <div class=\"card_number\">{{ standingCommitteeNum }}</div>\n            <div class=\"card_label\">政协常委(人)</div>\n          </div>\n        </div>\n      </div>\n      <div class=\"circles_box\">\n        <div class=\"circles_top_header\">\n          <span class=\"circles_top_header_title\">界别分布</span>\n          <span class=\"circles_top_header_more\" style=\"\">查看全部</span>\n        </div>\n        <horizontalBarEcharts id=\"circles\" :barList=\"barList\" colorStart=\"#FFFFFF\" colorEnd=\"#EF817C\"\n          style=\"height: 260px;\" />\n      </div>\n    </div>\n    <!-- 提案统计 -->\n    <div class=\"home_proposal_statistics\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">提案统计</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">{{ circles }}</span>\n        </div>\n      </div>\n      <div class=\"proposal_statistics_box\">\n        <div class=\"proposal_card\" v-for=\"(item, index) in proposalStatsNum\" :key=\"index\">\n          <div class=\"proposal_number\" :style=\"{ color: item.color }\">{{ item.value }}</div>\n          <div class=\"proposal_label\">{{ item.label }}</div>\n        </div>\n      </div>\n      <div class=\"proposal_type_analysis\">\n        <pieEchartsLegend id=\"typeAnalysisPie\" :dataList=\"typeAnalysisList\" title=\"类型分析\" />\n      </div>\n    </div>\n    <!-- 工作动态 -->\n    <div class=\"home_work_dynamics\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">工作动态</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">本年</span>\n        </div>\n      </div>\n      <div class=\"work_dynamics_list\">\n        <div class=\"work_dynamics_item\" v-for=\"(item, idx) in workDynamicsList\" :key=\"idx\">\n          <div class=\"work_dynamics_title\">{{ item.title }}</div>\n          <div class=\"work_dynamics_date\">{{ item.date }}</div>\n        </div>\n      </div>\n    </div>\n    <!-- 社情民意 -->\n    <div class=\"home_social\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">社情民意</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">本年</span>\n        </div>\n      </div>\n      <div class=\"social_box\">\n        <!-- 总数卡片 -->\n        <div class=\"social_card total_card\" :style=\"{ background: socialStats[0].bg }\">\n          <div class=\"total_card_number\" :style=\"{ color: socialStats[0].color }\">{{ socialStats[0].total }}</div>\n          <div class=\"total_card_label\">{{ socialStats[0].label }}</div>\n        </div>\n        <!-- 报送卡片 -->\n        <div class=\"social_card report_card\" v-for=\"(item, idx) in socialStats.slice(1)\" :key=\"idx\"\n          :style=\"{ background: item.bg }\">\n          <div class=\"report_row\">\n            <span class=\"report_label\">总数</span>\n            <span class=\"report_total\" :style=\"{ color: item.color }\">{{ item.total }}</span>\n          </div>\n          <div class=\"report_row\">\n            <span class=\"report_label\">采用</span>\n            <span class=\"report_adopted\" :style=\"{ color: item.adoptedColor }\">{{ item.adopted }}</span>\n          </div>\n          <div class=\"report_card_label\">{{ item.label }}</div>\n        </div>\n      </div>\n    </div>\n    <!-- 会议活动 -->\n    <div class=\"home_meetting_activity\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">会议活动</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">本年</span>\n        </div>\n      </div>\n      <div class=\"meetting_activity_box\">\n        <div v-for=\"(item, idx) in meettingActivityList\" :key=\"idx\" class=\"meetting_activity_card\"\n          :class=\"item.cardClass\">\n          <img :src=\"item.icon\" class=\"activity_card_iconimg\" />\n          <div class=\"meetting_activity_card_content\">\n            <div class=\"meetting_activity_card_label\">{{ item.label1 }}</div>\n            <div class=\"meetting_activity_card_value\" :style=\"item.value1Style\">{{ item.value1 }}</div>\n            <div class=\"meetting_activity_card_label\">{{ item.label2 }}</div>\n            <div class=\"meetting_activity_card_value\" :style=\"item.value2Style\">{{ item.value2 }}</div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <!-- 网络议政 -->\n    <div class=\"home_discussions\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">网络议政</span>\n        </div>\n      </div>\n      <div class=\"discussions_box\">\n        <div class=\"discussion_card\" v-for=\"(item, idx) in discussionsList\" :key=\"idx\"\n          :style=\"{ backgroundImage: `url(${item.bg})` }\">\n          <div class=\"discussion_card_number\">{{ item.number }}<span class=\"discussion_card_unit\">{{ item.unit }}</span>\n          </div>\n          <div class=\"discussion_card_label\">{{ item.label }}</div>\n        </div>\n      </div>\n      <div class=\"hot_topics\">\n        <div class=\"hot_topics_title\">最热话题</div>\n        <div class=\"hot_topics_list\">\n          <div class=\"hot_topic_item\" v-for=\"(topic, idx) in hotTopics\" :key=\"idx\">\n            <span class=\"hot_topic_index\" :class=\"'hot_topic_index_' + (idx + 1)\">{{ idx + 1 }}</span>\n            <span class=\"hot_topic_text\">{{ topic.title }}</span>\n            <span class=\"hot_topic_tag\" :class=\"'hot_topic_tag_' + (idx + 1)\">热</span>\n          </div>\n        </div>\n      </div>\n    </div>\n    <!-- 履职统计 -->\n    <div class=\"home_performance_statistics\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">履职统计</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">{{ circles }}</span>\n        </div>\n      </div>\n      <div class=\"performance_statistics_box\">\n        <div class=\"performance_table\">\n          <div class=\"performance_table_header\">\n            <span>姓名</span>\n            <span>会议活动</span>\n            <span>政协提案</span>\n            <span>社情民意</span>\n          </div>\n          <div class=\"performance_table_row\" v-for=\"(item, idx) in performanceStatistics\" :key=\"item.name\"\n            :class=\"{ 'row-alt': idx % 2 === 1 }\">\n            <span>{{ item.name }}</span>\n            <span>{{ item.meeting }}</span>\n            <span>{{ item.proposal }}</span>\n            <span>{{ item.opinion }}</span>\n          </div>\n          <div class=\"performance_table_footer\">\n            <button class=\"view-all-btn\">查看全部</button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n<script>\nimport { toRefs, reactive } from 'vue'\nimport MapQingdao from './echartsComponent/MapQingdao.vue'\nimport horizontalBarEcharts from './echartsComponent/horizontalBarEcharts.vue'\nimport pieEchartsLegend from './echartsComponent/pieEchartsLegend.vue'\n\nexport default {\n  components: { MapQingdao, horizontalBarEcharts, pieEchartsLegend },\n  setup () {\n    const data = reactive({\n      mapData: [\n        { name: '市南区', value: 80 },\n        { name: '市北区', value: 60 },\n        { name: '李沧区', value: 50 },\n        { name: '崂山区', value: 40 },\n        { name: '城阳区', value: 70 },\n        { name: '黄岛区', value: 90 },\n        { name: '即墨区', value: 30 },\n        { name: '胶州市', value: 55 },\n        { name: '平度市', value: 20 },\n        { name: '莱西市', value: 10 }\n      ],\n      circles: '十一届二次',\n      cppccMemberNum: '10095',\n      standingCommitteeNum: '8742',\n      barList: [\n        { name: '教育界', value: 35 },\n        { name: '医药卫生界', value: 15 },\n        { name: '经济界', value: 14 },\n        { name: '工商联界', value: 21 },\n        { name: '民革界', value: 15 },\n        { name: '特邀界', value: 21 },\n        { name: '妇联界', value: 8 },\n        { name: '工会界', value: 8 },\n        { name: '社会福利与社会保障界', value: 14 }\n      ],\n      proposalStatsNum: [\n        { value: 873, label: '提案总数', color: '#2386F9' },\n        { value: 456, label: '委员提案', color: '#2CA6F9' },\n        { value: 354, label: '界别提案', color: '#3AC86B' },\n        { value: 221, label: '组织提案', color: '#F96C9C' }\n      ],\n      typeAnalysisList: [\n        { name: '发改财政', value: 22.52, color: '#3DC3F0' },\n        { name: '民政市场', value: 18.33, color: '#4AC6A8' },\n        { name: '公安司法', value: 12.5, color: '#F9C846' },\n        { name: '区市政府', value: 11.34, color: '#6DD3A0' },\n        { name: '科技工信', value: 9.56, color: '#7B8DF9' },\n        { name: '教育文化', value: 8.09, color: '#F97C9C' },\n        { name: '派出机构', value: 4.21, color: '#F9A846' },\n        { name: '驻青单位', value: 3.71, color: '#F97C46' },\n        { name: '住建交通', value: 3.65, color: '#A97CF9' },\n        { name: '农村卫生', value: 3.21, color: '#4A9CF9' },\n        { name: '其他机构', value: 1.86, color: '#BFBFBF' },\n        { name: '党群其他', value: 1.02, color: '#F9C8C8' }\n      ],\n      workDynamicsList: [\n        { title: '市政协社会和法制工作办公室围绕市政协社会和法制工作办公室围绕', date: '2025-06-03' },\n        { title: '“与民同行 共创共赢”新格局下民市政协社会和法制工作办公室围绕', date: '2025-05-30' },\n        { title: '“惠民生·基层行”义诊活动温暖人心市政协社会和法制工作办公室围绕', date: '2025-05-30' },\n        { title: '市科技局面复市政协科技界别提案市政协社会和法制工作办公室围绕', date: '2025-05-30' },\n        { title: '孟庆斌到胶州市、崂山区调研项目时市政协社会和法制工作办公室围绕', date: '2025-05-29' }\n      ],\n      socialStats: [\n        { total: 1057, label: '总数', bg: '#EFF6FF', color: '#2386F9' },\n        { total: 345, adopted: 21, label: '委员报送', bg: '#FDF8F0', color: '#2386F9', adoptedColor: '#F9C846' },\n        { total: 547, adopted: 79, label: '单位报送', bg: '#F0FDF4', color: '#3AC86B', adoptedColor: '#F9C846' }\n      ],\n      meettingActivityList: [\n        {\n          cardClass: 'card_meeting',\n          icon: require('../../../assets/img/largeScreen/icon_meetting.png'),\n          label1: '会议次数',\n          value1: 201,\n          value1Style: { color: '#308FFF' },\n          label2: '会议人数',\n          value2: 2412,\n          value2Style: { color: '#308FFF' }\n        },\n        {\n          cardClass: 'card_activity',\n          icon: require('../../../assets/img/largeScreen/icon_acticity.png'),\n          label1: '活动次数',\n          value1: 310,\n          value1Style: { color: '#1FC6FF' },\n          label2: '活动人数',\n          value2: 4015,\n          value2Style: { color: '#1FC6FF' }\n        }\n      ],\n      discussionsList: [\n        {\n          bg: require('../../../assets/img/largeScreen/icon_release_bg.png'),\n          number: '72',\n          unit: '个',\n          label: '发布议题',\n          desc: ''\n        },\n        {\n          bg: require('../../../assets/img/largeScreen/icon_participate_bg.png'),\n          number: '39301',\n          unit: '次',\n          label: '累计参与人次',\n          desc: ''\n        },\n        {\n          bg: require('../../../assets/img/largeScreen/icon_seek_bg.png'),\n          number: '12308',\n          unit: '条',\n          label: '累计征求意见',\n          desc: ''\n        }\n      ],\n      hotTopics: [\n        { title: '推进黄河国家文化公园建设' },\n        { title: '持续推进黄河流域生态保护修复，助力…' },\n        { title: '全面加强新时代中小学劳动教育' }\n      ],\n      performanceStatistics: [\n        { name: '马平安', meeting: 515, proposal: 15, opinion: 0 },\n        { name: '马波', meeting: 400, proposal: 0, opinion: 12 },\n        { name: '王玉民', meeting: 490, proposal: 15, opinion: 0 },\n        { name: '王洋宝', meeting: 500, proposal: 0, opinion: 1 },\n        { name: '王忠', meeting: 420, proposal: 0, opinion: 2 },\n        { name: '刘彩霞', meeting: 512, proposal: 0, opinion: 1 },\n        { name: '刘军', meeting: 500, proposal: 20, opinion: 0 },\n        { name: '吴雪玲', meeting: 315, proposal: 15, opinion: 38 },\n        { name: '杨文比', meeting: 310, proposal: 60, opinion: 28 },\n        { name: '贾谊', meeting: 540, proposal: 9, opinion: 13 }\n      ]\n    })\n    return { ...toRefs(data) }\n  }\n}\n</script>\n<style lang=\"less\" scoped>\n.home_page {\n  width: 100%;\n  height: 100%;\n\n  .header_box {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 15px 15px 0 15px;\n\n    .header_left {\n      display: flex;\n      align-items: center;\n\n      .header_left_line {\n        width: 3px;\n        height: 14px;\n        background: #007AFF;\n      }\n\n      .header_left_title {\n        font-weight: bold;\n        font-size: 15px;\n        color: #222222;\n        margin-left: 8px;\n        font-family: Source Han Serif SC, Source Han Serif SC;\n      }\n    }\n\n    .header_right {\n      display: flex;\n      align-items: center;\n\n      .header_right_text {\n        font-weight: 400;\n        font-size: 12px;\n        color: #999999;\n      }\n    }\n  }\n\n  .map_section {\n    background: #fff;\n    border-radius: 8px;\n    padding: 10px;\n  }\n\n  .home_committee_statistics {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .committee_statistics_box {\n      display: flex;\n      gap: 15px;\n      padding: 20px 15px 10px 15px;\n\n      .statistics_card {\n        flex: 1;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: #f5faff;\n        position: relative;\n        height: 86px;\n\n        .card_content {\n          display: flex;\n          flex-direction: column;\n          align-items: flex-start;\n          justify-content: center;\n          margin-left: 55px;\n          margin-top: 5px;\n\n          .card_number {\n            font-size: 20px;\n            color: #4AA3FF;\n          }\n\n          .card_label {\n            font-size: 14px;\n            color: #666;\n            margin-top: 2px;\n          }\n        }\n      }\n\n      .card_blue {\n        background-image: url('../../../assets/img/largeScreen/icon_member_bg.png');\n        background-size: 100% 100%;\n        background-position: center;\n      }\n\n      .card_yellow {\n        background-image: url('../../../assets/img/largeScreen/icon_committee_bg.png');\n        background-size: 100% 100%;\n        background-position: center;\n\n        .card_number {\n          color: #E6B800 !important;\n        }\n      }\n    }\n\n    .circles_box {\n      border-radius: 6px;\n      margin: 10px 12px;\n\n      .circles_top_header {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n\n        .circles_top_header_title {\n          font-size: 14px;\n          color: #000;\n          font-family: Source Han Serif SC, Source Han Serif SC;\n        }\n\n        .circles_top_header_more {\n          font-size: 14px;\n          color: #0271E3;\n          border-radius: 14px;\n          border: 1px solid #0271E3;\n          padding: 3px 10px;\n        }\n      }\n    }\n  }\n\n  .home_proposal_statistics {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .proposal_statistics_box {\n      display: flex;\n      justify-content: space-between;\n      padding: 20px 15px 10px 15px;\n\n      .proposal_card {\n        flex: 1;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n\n        .proposal_number {\n          font-size: 24px;\n        }\n\n        .proposal_label {\n          font-size: 14px;\n          color: #999;\n          margin-top: 4px;\n        }\n      }\n    }\n\n    .proposal_type_analysis {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n    }\n  }\n\n  .home_work_dynamics {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .work_dynamics_list {\n      padding: 8px 15px;\n      background: #fff;\n\n      .work_dynamics_item {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        padding: 12px 0;\n        border-bottom: 1px solid #f0f0f0;\n\n        &:last-child {\n          border-bottom: none;\n        }\n\n        .work_dynamics_title {\n          flex: 1;\n          font-size: 14px;\n          color: #666666;\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n        }\n\n        .work_dynamics_date {\n          font-size: 14px;\n          color: #bdbdbd;\n          flex-shrink: 0;\n        }\n      }\n    }\n  }\n\n  .home_social {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .social_box {\n      display: flex;\n      gap: 16px;\n      padding: 20px 15px;\n\n      .social_card {\n        flex: 1;\n        width: 97px;\n        height: 94px;\n        border-radius: 10px;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        box-sizing: border-box;\n        box-shadow: none;\n        padding: 15px;\n        background-clip: padding-box;\n      }\n\n      .total_card {\n        justify-content: center;\n\n        .total_card_number {\n          font-size: 20px;\n          color: #3B91FB;\n          margin-bottom: 5px;\n        }\n\n        .total_card_label {\n          font-size: 14px;\n          color: #666666;\n        }\n      }\n\n      .report_card {\n        justify-content: flex-start;\n\n        .report_row {\n          width: 100%;\n          display: flex;\n          justify-content: space-between;\n          align-items: baseline;\n          margin-bottom: 2px;\n\n          .report_label {\n            font-size: 14px;\n            color: #999;\n            margin-right: 2px;\n          }\n\n          .report_total {\n            font-size: 15px;\n          }\n\n          .report_adopted {\n            font-size: 15px;\n          }\n        }\n\n        .report_card_label {\n          margin-top: 5px;\n          font-size: 15px;\n          color: #666;\n          font-family: Source Han Serif SC, Source Han Serif SC;\n        }\n      }\n    }\n  }\n\n  .home_meetting_activity {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .meetting_activity_box {\n      display: flex;\n      gap: 16px;\n      padding: 20px 15px;\n\n      .meetting_activity_card {\n        flex: 1;\n        display: flex;\n        align-items: flex-start;\n        box-sizing: border-box;\n        width: 157px;\n        height: 140px;\n        padding: 14px 20px;\n\n        .activity_card_iconimg {\n          width: 32px;\n          height: 32px;\n          margin-right: 15px;\n          margin-top: 4px;\n        }\n\n        .meetting_activity_card_content {\n          display: flex;\n          flex-direction: column;\n          justify-content: center;\n          flex: 1;\n\n          .meetting_activity_card_label {\n            font-size: 14px;\n            color: #999;\n            margin-bottom: 5px;\n          }\n\n          .meetting_activity_card_value {\n            font-size: 20px;\n            color: #308FFF;\n            margin-bottom: 8px;\n          }\n        }\n      }\n\n      .card_meeting {\n        background-image: url('../../../assets/img/largeScreen/icon_meetting_bg.png');\n        background-size: 100% 100%;\n        background-position: center;\n      }\n\n      .card_activity {\n        background-image: url('../../../assets/img/largeScreen/icon_activity_bg.png');\n        background-size: 100% 100%;\n        background-position: center;\n      }\n    }\n  }\n\n  .home_discussions {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .discussions_box {\n      display: flex;\n      gap: 10px;\n      padding: 20px 15px;\n      justify-content: flex-start;\n\n      .discussion_card {\n        flex: 1;\n        width: 103px;\n        height: 77px;\n        background-size: 100% 100%;\n        background-position: center;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        color: #fff;\n\n        .discussion_card_number {\n          font-size: 20px;\n          margin-bottom: 2px;\n\n          .discussion_card_unit {\n            font-size: 12px;\n            font-weight: normal;\n            margin-left: 2px;\n          }\n        }\n\n        .discussion_card_label {\n          font-size: 14px;\n          font-weight: 400;\n          margin-bottom: 2px;\n        }\n      }\n    }\n\n    .hot_topics {\n      padding: 0 15px 15px 15px;\n\n      .hot_topics_title {\n        font-size: 14px;\n        color: #000;\n        margin-bottom: 10px;\n        font-family: Source Han Serif SC, Source Han Serif SC;\n      }\n\n      .hot_topics_list {\n        .hot_topic_item {\n          display: flex;\n          align-items: center;\n          border-bottom: 1px solid #f0f0f0;\n          padding: 12px 0;\n\n          .hot_topic_index {\n            font-size: 14px;\n            margin-right: 10px;\n          }\n\n          .hot_topic_index_1 {\n            color: #FF4D4F;\n          }\n\n          .hot_topic_index_2 {\n            color: #FF9900;\n          }\n\n          .hot_topic_index_3 {\n            color: #FFD600;\n          }\n\n          .hot_topic_text {\n            flex: 1;\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            font-size: 14px;\n            color: #666;\n          }\n\n          .hot_topic_tag {\n            font-size: 14px;\n            color: #fff;\n            border-radius: 2px;\n            width: 22px;\n            height: 22px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n          }\n\n          .hot_topic_tag_1 {\n            background: #FB3030;\n          }\n\n          .hot_topic_tag_2 {\n            background: #FF833E;\n          }\n\n          .hot_topic_tag_3 {\n            background: #FFD978;\n          }\n        }\n      }\n    }\n  }\n\n  .home_performance_statistics {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .performance_statistics_box {\n      margin-top: 15px;\n\n      .performance_table {\n        width: 100%;\n        background: #fff;\n\n        .performance_table_header,\n        .performance_table_row {\n          display: flex;\n          align-items: center;\n          padding: 8px 0;\n\n          span {\n            flex: 1;\n            text-align: center;\n          }\n        }\n\n        .performance_table_header {\n          background: #F1F8FF;\n          font-weight: bold;\n          color: #222;\n          font-size: 14px;\n        }\n\n        .performance_table_row {\n          background: #fff;\n          color: #222;\n          font-size: 14px;\n\n          &.row-alt {\n            background: #F1F8FF;\n          }\n        }\n\n        .performance_table_footer {\n          display: flex;\n          justify-content: center;\n          padding: 10px 0;\n          background: #fff;\n\n          .view-all-btn {\n            border: 1px solid #0271e3;\n            color: #0271e3;\n            background: #fff;\n            border-radius: 16px;\n            padding: 4px 14px;\n            font-size: 14px;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}