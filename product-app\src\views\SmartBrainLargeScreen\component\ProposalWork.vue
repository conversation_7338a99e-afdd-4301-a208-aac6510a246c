<template>
  <div class="ProposalWork">
    <!-- 提案整体情况 -->
    <div class="proposal_overall_situation_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">提案整体情况</span>
        </div>
      </div>
      <div class="proposal_overall_situation">
        <div class="statistics_row">
          <div class="statistics_card" style="background: #E8F7FF;">
            <img src="../../../assets/img/largeScreen/icon_total.png" alt="提案总件数" class="card_icon" />
            <div class="card_label">提案总件数</div>
            <div class="card_value proposal_total_text">{{ proposalTotal }}</div>
          </div>
          <div class="statistics_card" style="background: #F1F5FF;">
            <img src="../../../assets/img/largeScreen/icon_register_num.png" alt="立案总件数" class="card_icon" />
            <div class="card_label">立案总件数</div>
            <div class="card_value register_text">{{ registerTotal }}</div>
          </div>
          <div class="statistics_card" style="background: #DAF6F2;">
            <img src="../../../assets/img/largeScreen/icon_reply_num.png" alt="答复总件数" class="card_icon" />
            <div class="card_label">答复总件数</div>
            <div class="card_value reply_text">{{ replyTotal }}</div>
          </div>
        </div>
        <div class="progress_row">
          <div class="progress_card">
            <div class="progress_content">
              <div class="progress_circle blue_progress">
                <svg width="50" height="50" viewBox="0 0 80 80">
                  <circle cx="40" cy="40" r="30" fill="none" stroke="rgba(58,147,255,0.2)" stroke-width="8" />
                  <circle cx="40" cy="40" r="30" fill="none" stroke="#3A61CD" stroke-width="8" stroke-dasharray="188.4"
                    :stroke-dashoffset="registerCircleOffset" stroke-linecap="round" transform="rotate(-90 40 40)" />
                </svg>
              </div>
              <div class="progress_info">
                <div class="progress_label">立案率</div>
                <span class="progress_value" style="color: #3A61CD;">{{ registerRate }}</span>
              </div>
            </div>
          </div>
          <div class="progress_card">
            <div class="progress_content">
              <div class="progress_circle green_progress">
                <svg width="50" height="50" viewBox="0 0 80 80">
                  <circle cx="40" cy="40" r="30" fill="none" stroke="rgba(58,147,255,0.2)" stroke-width="8" />
                  <circle cx="40" cy="40" r="30" fill="none" stroke="#57BCAA" stroke-width="8" stroke-dasharray="188.4"
                    :stroke-dashoffset="replyCircleOffset" stroke-linecap="round" transform="rotate(-90 40 40)" />
                </svg>
              </div>
              <div class="progress_info">
                <div class="progress_label">答复率</div>
                <span class="progress_value" style="color: #57BCAA;">{{ replyRate }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 提交情况 -->
    <div class="submit_status_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">提交情况</span>
        </div>
      </div>
      <div class="submit_status">
        <div class="submit_statistics_row">
          <div class="submit_card" style="background: #E8F4FF;">
            <div class="submit_value committee_text">{{ committeeProposal }}</div>
            <div class="submit_label">委员提案</div>
          </div>
          <div class="submit_card" style="background: #FFF8E1;">
            <div class="submit_value boundary_text">{{ boundaryProposal }}</div>
            <div class="submit_label">界别提案</div>
          </div>
          <div class="submit_card" style="background: #E8F5E8;">
            <div class="submit_value organization_text">{{ organizationProposal }}</div>
            <div class="submit_label">组织提案</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 类型分布 -->
    <div class="proposal_type_analysis_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">类型分布</span>
        </div>
      </div>
      <div class="proposal_type_analysis">
        <pieEchartsLegend id="typeAnalysisPie" :dataList="typeAnalysisList" title="类型分析" />
      </div>
    </div>
    <!-- 答复类型 -->
    <div class="reply_type_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">答复类型</span>
        </div>
      </div>
      <div class="reply_type">
        <ProgressBarChart title="面复" :desc="`占总件数${replyType.face}%`" :percent="replyType.face"
          :value="replyType.faceNum" color="linear-gradient(90deg, rgba(58,147,255,0.3) 0%, #3A93FF 100%)" />
        <ProgressBarChart title="函复" :desc="`占提交数${replyType.letter}%`" :percent="replyType.letter"
          :value="replyType.letterNum" color="linear-gradient(90deg, rgba(255,115,141,0.3) 0%, #FF738D 100%)" />
      </div>
    </div>
    <!-- 办理单位统计（前十） -->
    <div class="handle_unit_statistics_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">办理单位统计（前十）</span>
        </div>
      </div>
      <div class="handle_unit_statistics">
        <!-- 表头 -->
        <div class="ranking_header">
          <div class="header_rank">排名</div>
          <div class="header_unit">单位名称</div>
          <div class="header_count">办理数量</div>
        </div>

        <!-- 排名列表 -->
        <div class="ranking_list">
          <div v-for="(item, index) in unitRankingList" :key="index" class="ranking_item"
            :class="{ 'top_three': index < 3 }">
            <div class="rank_number">
              <!-- 前三名显示图片，其他显示数字 -->
              <img v-if="index < 3" :src="getRankIcon(index + 1)" :alt="`第${index + 1}名`" class="rank_icon" />
              <span v-else class="rank_text">{{ index + 1 }}</span>
            </div>
            <div class="unit_name">{{ item.name }}</div>
            <div class="handle_count">{{ item.count }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { toRefs, reactive, computed } from 'vue'
import pieEchartsLegend from './echartsComponent/pieEchartsLegend.vue'
import ProgressBarChart from './echartsComponent/ProgressBarChart.vue'
export default {
  components: { pieEchartsLegend, ProgressBarChart },
  name: 'ProposalWork',
  setup () {
    const data = reactive({
      // 提案统计数据
      proposalTotal: 1500, // 提案总件数
      registerTotal: 600, // 立案总件数
      replyTotal: 600, // 答复总件数

      // 提交情况数据
      committeeProposal: 456, // 委员提案
      boundaryProposal: 354, // 界别提案
      organizationProposal: 221, // 组织提案

      // 计算属性相关数据
      circleRadius: 30, // 圆形进度条半径
      circleStrokeWidth: 8, // 圆形进度条线宽
      typeAnalysisList: [
        { name: '发改财政', value: 22.52, color: '#3DC3F0' },
        { name: '民政市场', value: 18.33, color: '#4AC6A8' },
        { name: '公安司法', value: 12.5, color: '#F9C846' },
        { name: '区市政府', value: 11.34, color: '#6DD3A0' },
        { name: '科技工信', value: 9.56, color: '#7B8DF9' },
        { name: '教育文化', value: 8.09, color: '#F97C9C' },
        { name: '派出机构', value: 4.21, color: '#F9A846' },
        { name: '驻青单位', value: 3.71, color: '#F97C46' },
        { name: '住建交通', value: 3.65, color: '#A97CF9' },
        { name: '农村卫生', value: 3.21, color: '#4A9CF9' },
        { name: '其他机构', value: 1.86, color: '#BFBFBF' },
        { name: '党群其他', value: 1.02, color: '#F9C8C8' }
      ],
      replyType: { face: 60, faceNum: 360, letter: 40, letterNum: 240 },

      // 办理单位统计数据（前十）
      unitRankingList: [
        { name: '市教育局', count: 89 },
        { name: '市民政局', count: 75 },
        { name: '市劳动局', count: 70 },
        { name: '市农业农村委', count: 63 },
        { name: '市交通运输管理局', count: 60 },
        { name: '市经济信息委', count: 50 },
        { name: '市发改委', count: 46 },
        { name: '市教委', count: 46 },
        { name: '市林业局', count: 44 },
        { name: '市规划自然资源局', count: 41 }
      ]
    })

    // 计算立案率
    const registerRate = computed(() => {
      if (data.proposalTotal === 0) return '0%'
      return Math.round((data.registerTotal / data.proposalTotal) * 100) + '%'
    })

    // 计算答复率
    const replyRate = computed(() => {
      if (data.registerTotal === 0) return '0%'
      return Math.round((data.replyTotal / data.registerTotal) * 100) + '%'
    })

    // 计算立案率圆形进度条偏移量
    const registerCircleOffset = computed(() => {
      const circumference = 2 * Math.PI * data.circleRadius
      const percentage = data.proposalTotal === 0 ? 0 : (data.registerTotal / data.proposalTotal)
      return circumference - (circumference * percentage)
    })

    // 计算答复率圆形进度条偏移量
    const replyCircleOffset = computed(() => {
      const circumference = 2 * Math.PI * data.circleRadius
      const percentage = data.registerTotal === 0 ? 0 : (data.replyTotal / data.registerTotal)
      return circumference - (circumference * percentage)
    })

    // 获取排名图标
    const getRankIcon = (rank) => {
      // 这里先返回占位图片路径，您可以后续替换为实际的图片路径
      const iconMap = {
        1: require('../../../assets/img/largeScreen/icon_rank_one.png'), // 第一名图标
        2: require('../../../assets/img/largeScreen/icon_rank_two.png'), // 第二名图标
        3: require('../../../assets/img/largeScreen/icon_rank_three.png') // 第三名图标
      }
      return iconMap[rank] || ''
    }

    return {
      ...toRefs(data),
      registerRate,
      replyRate,
      registerCircleOffset,
      replyCircleOffset,
      getRankIcon
    }
  }
}
</script>
<style lang="less" scoped>
.ProposalWork {
  width: 100%;
  height: 100%;

  .header_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 15px 0 15px;

    .header_left {
      display: flex;
      align-items: center;

      .header_left_line {
        width: 3px;
        height: 14px;
        background: #007AFF;
      }

      .header_left_title {
        font-weight: bold;
        font-size: 15px;
        color: #222222;
        margin-left: 8px;
        font-family: Source Han Serif SC, Source Han Serif SC;
      }
    }

    .header_right {
      display: flex;
      align-items: center;

      .header_right_text {
        font-weight: 400;
        font-size: 12px;
        color: #999999;
      }

      .header_right_more {
        font-size: 14px;
        color: #0271E3;
        border-radius: 14px;
        border: 1px solid #0271E3;
        padding: 3px 10px;
      }
    }
  }

  .proposal_overall_situation_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .proposal_overall_situation {
      padding: 12px;

      .statistics_row {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;

        .statistics_card {
          flex: 1;
          border-radius: 4px;
          padding: 17px 8px;
          text-align: center;

          .card_icon {
            width: 32px;
            height: 32px;
            margin-bottom: 8px;
          }

          .card_label {
            font-size: 12px;
            color: #999;
            margin-bottom: 8px;
          }

          .card_value {
            font-size: 20px;

            &.proposal_total_text {
              color: #308FFF;
            }

            &.register_text {
              color: #3A61CD;
            }

            &.reply_text {
              color: #57BCAA;
            }
          }
        }
      }

      .progress_row {
        display: flex;
        gap: 10px;
        justify-content: space-between;

        .progress_card {
          flex: 1;
          border-radius: 4px;
          padding: 10px 16px;
          background: #E8F7FF;

          .progress_content {
            display: flex;
            align-items: center;
          }

          .progress_circle {
            margin-right: 15px;
            margin-top: 5px;
          }

          .progress_info {

            .progress_label {
              font-size: 12px;
              color: #999999;
              margin-bottom: 5px;
            }

            .progress_value {
              font-size: 20px;
            }
          }
        }
      }
    }
  }

  .submit_status_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .submit_status {
      padding: 12px;

      .submit_statistics_row {
        display: flex;
        gap: 10px;

        .submit_card {
          flex: 1;
          border-radius: 4px;
          padding: 24px 16px;
          text-align: center;

          .submit_value {
            font-size: 20px;
            margin-bottom: 10px;

            &.committee_text {
              color: #3B91FB;
            }

            &.boundary_text {
              color: #EAB308;
            }

            &.organization_text {
              color: #43DDBB;
            }
          }

          .submit_label {
            font-size: 14px;
            color: #666666;
          }
        }
      }
    }
  }

  .proposal_type_analysis_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .proposal_type_analysis {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }

  .reply_type_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .reply_type {
      padding: 5px 18px 18px 18px;
    }
  }

  .handle_unit_statistics_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .handle_unit_statistics {
      padding: 5px 18px 18px 18px;

      .ranking_header {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #F0F0F0;
        margin-bottom: 8px;

        .header_rank {
          width: 60px;
          font-size: 14px;
          color: #999999;
          text-align: center;
        }

        .header_unit {
          flex: 1;
          font-size: 14px;
          color: #999999;
          text-align: left;
          padding-left: 10px;
        }

        .header_count {
          width: 80px;
          font-size: 14px;
          color: #999999;
          text-align: right;
        }
      }

      .ranking_list {
        .ranking_item {
          display: flex;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid #F8F8F8;

          &:last-child {
            border-bottom: none;
          }

          .rank_number {
            width: 60px;
            display: flex;
            justify-content: center;
            align-items: center;

            .rank_icon {
              width: 22px;
              height: 27px;
            }

            .rank_text {
              font-size: 14px;
              color: #999999;
            }
          }

          .unit_name {
            flex: 1;
            font-size: 14px;
            color: #333333;
            padding-left: 10px;
            font-weight: 400;
          }

          .handle_count {
            width: 80px;
            font-size: 14px;
            color: #333333;
            text-align: right;
            font-weight: 500;
          }

          // 前三名特殊样式
          &.top_three {
            .unit_name {
              font-weight: 500;
            }

            .handle_count {
              font-weight: 600;
            }
          }
        }
      }
    }
  }
}
</style>
