<template>
  <div class="ProposalWork">
    <!-- 提案整体情况 -->
    <div class="proposal_overall_situation_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">提案整体情况</span>
        </div>
      </div>
      <div class="proposal_overall_situation">
        <div class="statistics_row">
          <div class="statistics_card" style="background: #E8F7FF;">
            <img src="../../../assets/img/largeScreen/icon_total.png" alt="提案总件数" class="card_icon" />
            <div class="card_label">提案总件数</div>
            <div class="card_value proposal_total_text">1500</div>
          </div>
          <div class="statistics_card" style="background: #F1F5FF;">
            <img src="../../../assets/img/largeScreen/icon_register_num.png" alt="立案总件数" class="card_icon" />
            <div class="card_label">立案总件数</div>
            <div class="card_value register_text">600</div>
          </div>
          <div class="statistics_card" style="background: #DAF6F2;">
            <img src="../../../assets/img/largeScreen/icon_reply_num.png" alt="答复总件数" class="card_icon" />
            <div class="card_label">答复总件数</div>
            <div class="card_value reply_text">600</div>
          </div>
        </div>
        <div class="progress_row">
          <div class="progress_card">
            <div class="progress_content">
              <div class="progress_circle blue_progress">
                <svg width="60" height="60" viewBox="0 0 80 80">
                  <circle cx="40" cy="40" r="30" fill="none" stroke="rgba(58,147,255,0.2)" stroke-width="8" />
                  <circle cx="40" cy="40" r="30" fill="none" stroke="#3A61CD" stroke-width="8" stroke-dasharray="188.4"
                    stroke-dashoffset="58.4" stroke-linecap="round" transform="rotate(-90 40 40)" />
                </svg>
              </div>
              <div class="progress_info">
                <div class="progress_label">立案率</div>
                <span class="progress_value" style="color: #3A61CD;">69%</span>
              </div>
            </div>
          </div>
          <div class="progress_card">
            <div class="progress_content">
              <div class="progress_circle green_progress">
                <svg width="60" height="60" viewBox="0 0 80 80">
                  <circle cx="40" cy="40" r="30" fill="none" stroke="rgba(58,147,255,0.2)" stroke-width="8" />
                  <circle cx="40" cy="40" r="30" fill="none" stroke="#57BCAA" stroke-width="8" stroke-dasharray="188.4"
                    stroke-dashoffset="58.4" stroke-linecap="round" transform="rotate(-90 40 40)" />
                </svg>
              </div>
              <div class="progress_info">
                <div class="progress_label">答复率</div>
                <span class="progress_value" style="color: #57BCAA;">69%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ProposalWork',
  setup () {
    const data = reactive({

    })
    return { ...toRefs(data) }
  }
}
</script>
<style lang="less" scoped>
.ProposalWork {
  width: 100%;
  height: 100%;

  .header_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 15px 0 15px;

    .header_left {
      display: flex;
      align-items: center;

      .header_left_line {
        width: 3px;
        height: 14px;
        background: #007AFF;
      }

      .header_left_title {
        font-weight: bold;
        font-size: 15px;
        color: #222222;
        margin-left: 8px;
        font-family: Source Han Serif SC, Source Han Serif SC;
      }
    }

    .header_right {
      display: flex;
      align-items: center;

      .header_right_text {
        font-weight: 400;
        font-size: 12px;
        color: #999999;
      }

      .header_right_more {
        font-size: 14px;
        color: #0271E3;
        border-radius: 14px;
        border: 1px solid #0271E3;
        padding: 3px 10px;
      }
    }
  }

  .proposal_overall_situation_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .proposal_overall_situation {
      padding: 12px;

      .statistics_row {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;

        .statistics_card {
          flex: 1;
          border-radius: 4px;
          padding: 17px 8px;
          text-align: center;

          .card_icon {
            width: 32px;
            height: 32px;
            margin-bottom: 8px;
          }

          .card_label {
            font-size: 12px;
            color: #999;
            margin-bottom: 8px;
          }

          .card_value {
            font-size: 20px;

            &.proposal_total_text {
              color: #308FFF;
            }

            &.register_text {
              color: #3A61CD;
            }

            &.reply_text {
              color: #57BCAA;
            }
          }
        }
      }

      .progress_row {
        display: flex;
        gap: 10px;
        justify-content: space-between;

        .progress_card {
          flex: 1;
          border-radius: 4px;
          padding: 10px 16px;
          background: #E8F7FF;

          .progress_content {
            display: flex;
            align-items: center;
          }

          .progress_circle {
            margin-right: 15px;
            margin-top: 5px;
          }

          .progress_info {

            .progress_label {
              font-size: 12px;
              color: #999999;
              margin-bottom: 5px;
            }

            .progress_value {
              font-size: 20px;
            }
          }
        }
      }
    }
  }
}
</style>
