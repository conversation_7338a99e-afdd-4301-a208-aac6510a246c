<template>
  <div class="PublicOpinion">
    <!-- 社情民意整体情况 -->
    <div class="overall_situation_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">社情民意整体情况</span>
        </div>
      </div>
      <div class="overall_situation_list">
        <PublicOpinionOverallSituationChart id="publicOpinionOverall" :total-count="totalCount"
          :adopted-count="adoptedCount" />
      </div>
    </div>
    <!-- 采用情况 -->
    <div class="adopt_situation_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">采用情况</span>
        </div>
      </div>
      <div class="adopt_situation_list">
        <ProgressBarChart title="委员提交" desc="占总件数60%" :percent="committeeMember.submitPercent"
          :value="committeeMember.submitNum" color="linear-gradient(90deg, rgba(58,147,255,0.3) 0%, #3A93FF 100%)" />
        <ProgressBarChart title="采用情况" desc="占提交数42%" :percent="committeeMember.adoptSituationPercent"
          :value="committeeMember.adoptSituationNum"
          color="linear-gradient(90deg, rgba(255,115,141,0.3) 0%, #FF738D 100%)" />
        <ProgressBarChart title="单位提交" desc="占总件数60%" :percent="unit.submitPercent" :value="unit.submitNum"
          color="linear-gradient(90deg, rgba(58,147,255,0.3) 0%, #3A93FF 100%)" />
        <ProgressBarChart title="采用情况" desc="占提交数42%" :percent="unit.adoptSituationPercent"
          :value="unit.adoptSituationNum" color="linear-gradient(90deg, rgba(255,115,141,0.3) 0%, #FF738D 100%)" />
      </div>
      <div class="adopt_situation_distribution_text">采用情况分布</div>
      <div class="adopt_situation_distribution_charts">
        <PieChart id="adoptSituationDistribution" :chart-data="adoptSituationDistribution" :radius="['35%', '60%']" />
      </div>
    </div>
    <!-- 批示情况 -->
    <div class="instructions_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">批示情况</span>
        </div>
      </div>
      <div class="instructions_list">
        <div class="instructions_item" v-for="(item, index) in instructions" :key="index"
          :style="`background: ${item.bg}`">
          <div class="instructions_item_value" :style="`color: ${item.color}`">{{ item.value }}</div>
          <div class="instructions_item_label">{{ item.label }}</div>
        </div>
      </div>
    </div>
    <!-- 各单位上报与采用情况 -->
    <div class="report_adopt_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">各单位上报与采用情况</span>
        </div>
      </div>
      <div class="report_adopt_list">
        <DoubleBarChart id="party_double_line" :data="partyData" :legend="['报送篇数', '采用篇数']" :color="[
          ['#56A0FF', 'rgba(86,160,255,0.1)'],
          ['#FF738D', 'rgba(255,115,141,0.1)']
        ]" title="八大党派及民主工商联" style="height: 260px;" />
        <DoubleBarChart id="district_double_line" :data="districtData" :legend="['报送篇数', '采用篇数']" :color="[
          ['#56A0FF', 'rgba(86,160,255,0.1)'],
          ['#FF738D', 'rgba(255,115,141,0.1)']
        ]" title="各区市" style="height: 260px;" />
        <DoubleBarChart id="office_double_line" :data="officeData" :legend="['报送篇数', '采用篇数']" :color="[
          ['#56A0FF', 'rgba(86,160,255,0.1)'],
          ['#FF738D', 'rgba(255,115,141,0.1)']
        ]" title="各单位办公室" style="height: 260px;" />
      </div>
    </div>
    <!-- 个人报送与采用情况 -->
    <div class="submit_adopt_situation_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">个人报送与采用情况</span>
        </div>
        <div class="header_right" @click="openMore('notice')">
          <span class="header_right_more">查看全部</span>
        </div>
      </div>
      <div class="submit_adopt_situation_list">
        <div class="submit_adopt_table">
          <div class="submit_adopt_table_header">
            <span class="party-header-column">姓名</span>
            <span class="count-header-column">报送件数</span>
            <span class="count-header-column">采用件数</span>
          </div>
          <div class="submit_adopt_table_row" v-for="(item, idx) in submitAdoptSituationData" :key="item.name"
            :class="{ 'row-alt': idx % 2 === 1 }">
            <span class="party-column name-cell">
              <img :src="item.avatar" class="avatar" />
              <span class="name">{{ item.name }}</span>
            </span>
            <span class="count-column">{{ item.submit }}</span>
            <span class="count-column">{{ item.adopt }}</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 类别分析 -->
    <div class="category_analysis_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">类别分析</span>
        </div>
      </div>
      <div class="category_analysis_list">
        <barChart id="categoryAnalysis" :data="categoryData" :color="['#559FFF', 'rgba(85,159,255,0.3)']"
          style="height:200px" />
      </div>
    </div>
    <!-- 热词分析 -->
    <div class="hot_words_analysis_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">热词分析</span>
        </div>
      </div>
      <div class="hot_words_analysis_list">
        <wordCloudEcharts id="wordcloud" :wordList="wordCloudData"
          :colorList="['#1890FF', '#FF6B35', '#52C41A', '#722ED1', '#1890FF', '#FF69B4', '#52C41A', '#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']"
          :sizeRange="[2, 10]" />
      </div>
    </div>
  </div>
</template>
<script>
import { toRefs, reactive } from 'vue'
import PublicOpinionOverallSituationChart from './echartsComponent/PublicOpinionOverallSituationChart.vue'
import ProgressBarChart from './echartsComponent/ProgressBarChart.vue'
import PieChart from './echartsComponent/PieChart.vue'
import DoubleBarChart from './echartsComponent/DoubleBarChart.vue'
import barChart from './echartsComponent/barChart.vue'
import wordCloudEcharts from './echartsComponent/wordCloudEcharts.vue'
export default {
  name: 'NetworkPolitics',
  components: { PublicOpinionOverallSituationChart, ProgressBarChart, PieChart, DoubleBarChart, barChart, wordCloudEcharts },
  setup () {
    const data = reactive({
      totalCount: 50,
      adoptedCount: 20,
      committeeMember: { submitPercent: 60, submitNum: 345, adoptSituationPercent: 42, adoptSituationNum: 102 },
      unit: { submitPercent: 60, submitNum: 345, adoptSituationPercent: 42, adoptSituationNum: 102 },
      adoptSituationDistribution: [
        { name: '市政协采用', value: 135, percentage: '40%', color: '#4A90E2' },
        { name: '省政协采用', value: 131, percentage: '30%', color: '#4CD9C0' },
        { name: '全国政协采用', value: 126, percentage: '20%', color: '#F56A6A' }
      ],
      instructions: [
        { label: '市政协批示', value: '135', bg: '#EFF6FF', color: '#3B91FB' },
        { label: '省政协批示', value: '131', bg: '#FDF8F0', color: '#EAB308' },
        { label: '全国政协批示', value: '126', bg: '#F0FDF4', color: '#43DDBB' }
      ],
      partyData: [
        { name: '民革', value1: 102, value2: 80 },
        { name: '民盟', value1: 56, value2: 30 },
        { name: '民建', value1: 120, value2: 75 },
        { name: '民进', value1: 34, value2: 20 },
        { name: '农工', value1: 89, value2: 60 },
        { name: '致公', value1: 95, value2: 70 },
        { name: '九三', value1: 80, value2: 55 },
        { name: '工商联', value1: 45, value2: 25 }
      ],
      districtData: [
        { name: '市南', value1: 55, value2: 13 },
        { name: '市北', value1: 20, value2: 6 },
        { name: '李沧', value1: 35, value2: 10 },
        { name: '崂山', value1: 18, value2: 4 },
        { name: '黄岛', value1: 52, value2: 12 },
        { name: '城阳', value1: 10, value2: 2 },
        { name: '即墨', value1: 25, value2: 5 },
        { name: '胶州', value1: 30, value2: 7 },
        { name: '平度', value1: 12, value2: 3 },
        { name: '莱西', value1: 8, value2: 1 }
      ],
      officeData: [
        { name: '委员', value1: 8, value2: 2 },
        { name: '提案', value1: 2, value2: 1 },
        { name: '经济', value1: 5, value2: 1 },
        { name: '农业', value1: 18, value2: 6 },
        { name: '人口环', value1: 50, value2: 15 },
        { name: '教科', value1: 3, value2: 1 },
        { name: '社法', value1: 2, value2: 1 },
        { name: '港澳', value1: 1, value2: 0 },
        { name: '文史', value1: 2, value2: 1 },
        { name: '民宗', value1: 1, value2: 0 },
        { name: '财经', value1: 50, value2: 15 }
      ],
      submitAdoptSituationData: [
        { avatar: require('@/assets/img/largeScreen/man.png'), name: '张伟', submit: 12, adopt: 3 },
        { avatar: require('@/assets/img/largeScreen/woman.png'), name: '刘芳', submit: 11, adopt: 2 },
        { avatar: require('@/assets/img/largeScreen/man.png'), name: '陈明', submit: 11, adopt: 2 },
        { avatar: require('@/assets/img/largeScreen/man.png'), name: '林小华', submit: 11, adopt: 2 },
        { avatar: require('@/assets/img/largeScreen/man.png'), name: '赵天宇', submit: 10, adopt: 1 },
        { avatar: require('@/assets/img/largeScreen/woman.png'), name: '吴静怡', submit: 10, adopt: 1 },
        { avatar: require('@/assets/img/largeScreen/man.png'), name: '黄浩然', submit: 8, adopt: 1 },
        { avatar: require('@/assets/img/largeScreen/woman.png'), name: '周梦琪', submit: 7, adopt: 1 }
      ],
      categoryData: [
        { name: '社会', value: 115 },
        { name: '政治', value: 140 },
        { name: '经济', value: 60 },
        { name: '文化', value: 115 },
        { name: '生态文明', value: 125 }
      ],
      wordCloudData: [
        { name: '乡村振兴', value: 180 },
        { name: '就业优先', value: 165 },
        { name: '科技创新', value: 150 },
        { name: '改革开放', value: 135 },
        { name: '依法治国', value: 120 },
        { name: '教育人才', value: 105 },
        { name: '社会保障', value: 90 },
        { name: '热词', value: 75 },
        { name: '绿色发展', value: 60 },
        { name: '数字中国', value: 45 },
        { name: '共同富裕', value: 40 },
        { name: '文化自信', value: 35 },
        { name: '国家安全', value: 30 },
        { name: '人民至上', value: 25 },
        { name: '中国式现代化', value: 20 }
      ]
    })

    return { ...toRefs(data) }
  }
}
</script>
<style lang="less" scoped>
.PublicOpinion {
  width: 100%;
  height: 100%;

  .header_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 15px 0 15px;

    .header_left {
      display: flex;
      align-items: center;

      .header_left_line {
        width: 3px;
        height: 14px;
        background: #007AFF;
      }

      .header_left_title {
        font-weight: bold;
        font-size: 15px;
        color: #222222;
        margin-left: 8px;
        font-family: Source Han Serif SC, Source Han Serif SC;
      }
    }

    .header_right {
      display: flex;
      align-items: center;

      .header_right_text {
        font-weight: 400;
        font-size: 12px;
        color: #999999;
      }

      .header_right_more {
        font-size: 14px;
        color: #0271E3;
        border-radius: 14px;
        border: 1px solid #0271E3;
        padding: 3px 10px;
      }
    }
  }

  .overall_situation_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .overall_situation_list {
      height: 160px;
    }
  }

  .adopt_situation_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .adopt_situation_list {
      padding: 5px 18px 18px 18px;
    }

    .adopt_situation_distribution_text {
      font-size: 15px;
      color: #222222;
      font-family: Source Han Serif SC, Source Han Serif SC;
      padding-left: 18px;
    }

    .adopt_situation_distribution_charts {
      width: 100%;
      height: 240px;
      margin-top: 10px;
    }
  }

  .instructions_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .instructions_list {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 18px;

      .instructions_item {
        width: 93px;
        height: 90px;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .instructions_item_value {
          font-size: 19px;
        }

        .instructions_item_label {
          font-size: 13px;
          color: #666666;
          margin-top: 14px;
        }
      }
    }
  }

  .report_adopt_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;
  }

  .submit_adopt_situation_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .submit_adopt_situation_list {
      margin-top: 15px;

      .submit_adopt_table {
        width: 100%;
        background: #fff;

        .submit_adopt_table_header,
        .submit_adopt_table_row {
          display: flex;
          align-items: center;
          padding: 12px 15px;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .party-header-column {
            flex: 2;
            text-align: left;
            font-size: 14px;
            color: #999;
          }

          .count-header-column {
            flex: 1;
            text-align: center;
            font-size: 14px;
            color: #999;
          }

          .party-column {
            flex: 2;
            text-align: left;
            font-size: 14px;
            color: #333;
          }

          .count-column {
            flex: 1;
            text-align: center;
            font-size: 14px;
            color: #333;
          }

          .name-cell {
            display: flex;
            align-items: center;

            .avatar {
              width: 28px;
              height: 28px;
              border-radius: 50%;
              margin-right: 8px;
              object-fit: cover;
              border: 1px solid #e0e0e0;
            }

            .name {
              font-size: 14px;
              color: #333;
            }
          }
        }

        .submit_adopt_table_header {
          background: #F1F8FF;
          font-weight: 600;
          color: #222;
          font-size: 14px;
        }

        .submit_adopt_table_row {
          background: #fff;
          color: #333;
          font-size: 14px;

          &.row-alt {
            background: #F1F8FF;
          }
        }
      }
    }
  }

  .category_analysis_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .category_analysis_list {
      padding: 18px;
    }
  }

  .hot_words_analysis_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .hot_words_analysis_list {}
  }
}
</style>
