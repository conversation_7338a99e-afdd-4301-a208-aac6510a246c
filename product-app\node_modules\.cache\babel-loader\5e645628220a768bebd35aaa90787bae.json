{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue?vue&type=template&id=6b17b738&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue", "mtime": 1753932951993}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_imports_0", "_imports_1", "_imports_2", "class", "_createElementBlock", "_hoisted_1", "_createCommentVNode"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ProposalWork\">\r\n    <!-- 提案整体情况 -->\r\n    <div class=\"proposal_overall_situation_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">提案整体情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"proposal_overall_situation\">\r\n        <div class=\"statistics_row\">\r\n          <div class=\"statistics_card\" style=\"background: #E8F7FF;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_total.png\" alt=\"提案总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">提案总件数</div>\r\n            <div class=\"card_value proposal_total_text\">1500</div>\r\n          </div>\r\n          <div class=\"statistics_card\" style=\"background: #F1F5FF;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_register_num.png\" alt=\"立案总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">立案总件数</div>\r\n            <div class=\"card_value register_text\">600</div>\r\n          </div>\r\n          <div class=\"statistics_card\" style=\"background: #DAF6F2;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_reply_num.png\" alt=\"答复总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">答复总件数</div>\r\n            <div class=\"card_value reply_text\">600</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"progress_row\">\r\n          <div class=\"progress_card\">\r\n            <div class=\"progress_content\">\r\n              <div class=\"progress_circle blue_progress\">\r\n                <svg width=\"60\" height=\"60\" viewBox=\"0 0 80 80\">\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"rgba(58,147,255,0.2)\" stroke-width=\"8\" />\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"#3A61CD\" stroke-width=\"8\" stroke-dasharray=\"188.4\"\r\n                    stroke-dashoffset=\"90\" stroke-linecap=\"round\" transform=\"rotate(-90 40 40)\" />\r\n                </svg>\r\n              </div>\r\n              <div class=\"progress_info\">\r\n                <div class=\"progress_label\">立案率</div>\r\n                <span class=\"progress_value\" style=\"color: #3A61CD;\">69%</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"progress_card\">\r\n            <div class=\"progress_content\">\r\n              <div class=\"progress_circle green_progress\">\r\n                <svg width=\"60\" height=\"60\" viewBox=\"0 0 80 80\">\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"rgba(58,147,255,0.2)\" stroke-width=\"8\" />\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"#57BCAA\" stroke-width=\"8\" stroke-dasharray=\"188.4\"\r\n                    stroke-dashoffset=\"58.4\" stroke-linecap=\"round\" transform=\"rotate(-90 40 40)\" />\r\n                </svg>\r\n              </div>\r\n              <div class=\"progress_info\">\r\n                <div class=\"progress_label\">答复率</div>\r\n                <span class=\"progress_value\" style=\"color: #57BCAA;\">69%</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { toRefs, reactive } from 'vue'\r\nexport default {\r\n  name: 'ProposalWork',\r\n  setup () {\r\n    const data = reactive({\r\n\r\n    })\r\n    return { ...toRefs(data) }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.ProposalWork {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .header_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 15px 0 15px;\r\n\r\n    .header_left {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_left_line {\r\n        width: 3px;\r\n        height: 14px;\r\n        background: #007AFF;\r\n      }\r\n\r\n      .header_left_title {\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #222222;\r\n        margin-left: 8px;\r\n        font-family: Source Han Serif SC, Source Han Serif SC;\r\n      }\r\n    }\r\n\r\n    .header_right {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_right_text {\r\n        font-weight: 400;\r\n        font-size: 12px;\r\n        color: #999999;\r\n      }\r\n\r\n      .header_right_more {\r\n        font-size: 14px;\r\n        color: #0271E3;\r\n        border-radius: 14px;\r\n        border: 1px solid #0271E3;\r\n        padding: 3px 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .proposal_overall_situation_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .proposal_overall_situation {\r\n      padding: 12px;\r\n\r\n      .statistics_row {\r\n        display: flex;\r\n        gap: 10px;\r\n        margin-bottom: 10px;\r\n\r\n        .statistics_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 17px 8px;\r\n          text-align: center;\r\n\r\n          .card_icon {\r\n            width: 32px;\r\n            height: 32px;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .card_label {\r\n            font-size: 12px;\r\n            color: #999;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .card_value {\r\n            font-size: 20px;\r\n\r\n            &.proposal_total_text {\r\n              color: #308FFF;\r\n            }\r\n\r\n            &.register_text {\r\n              color: #3A61CD;\r\n            }\r\n\r\n            &.reply_text {\r\n              color: #57BCAA;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .progress_row {\r\n        display: flex;\r\n        gap: 10px;\r\n        justify-content: space-between;\r\n\r\n        .progress_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 10px 16px;\r\n          background: #E8F7FF;\r\n\r\n          .progress_content {\r\n            display: flex;\r\n            align-items: center;\r\n          }\r\n\r\n          .progress_circle {\r\n            margin-right: 15px;\r\n            margin-top: 5px;\r\n          }\r\n\r\n          .progress_info {\r\n\r\n            .progress_label {\r\n              font-size: 12px;\r\n              color: #999999;\r\n              margin-bottom: 5px;\r\n            }\r\n\r\n            .progress_value {\r\n              font-size: 20px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";OAaiBA,UAAoD;OAKpDC,UAA2D;OAK3DC,UAAwD;;EAtBlEC,KAAK,EAAC;AAAc;;uBAAzBC,mBAAA,CA6DM,OA7DNC,UA6DM,GA5DJC,mBAAA,YAAe,E", "ignoreList": []}]}