{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue?vue&type=template&id=6b17b738&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue", "mtime": 1753936042886}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_imports_0", "_imports_1", "_imports_2", "class", "style", "width", "height", "viewBox", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "src", "alt", "_hoisted_6", "_toDisplayString", "_ctx", "proposalTotal", "_hoisted_7", "_hoisted_8", "registerTotal", "_hoisted_9", "_hoisted_10", "replyTotal", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "cx", "cy", "r", "fill", "stroke", "$setup", "registerCircleOffset", "transform", "_hoisted_17", "_hoisted_18", "registerRate", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "replyCircleOffset", "_hoisted_24", "_hoisted_25", "replyRate"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ProposalWork\">\r\n    <!-- 提案整体情况 -->\r\n    <div class=\"proposal_overall_situation_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">提案整体情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"proposal_overall_situation\">\r\n        <div class=\"statistics_row\">\r\n          <div class=\"statistics_card\" style=\"background: #E8F7FF;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_total.png\" alt=\"提案总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">提案总件数</div>\r\n            <div class=\"card_value proposal_total_text\">{{ proposalTotal }}</div>\r\n          </div>\r\n          <div class=\"statistics_card\" style=\"background: #F1F5FF;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_register_num.png\" alt=\"立案总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">立案总件数</div>\r\n            <div class=\"card_value register_text\">{{ registerTotal }}</div>\r\n          </div>\r\n          <div class=\"statistics_card\" style=\"background: #DAF6F2;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_reply_num.png\" alt=\"答复总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">答复总件数</div>\r\n            <div class=\"card_value reply_text\">{{ replyTotal }}</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"progress_row\">\r\n          <div class=\"progress_card\">\r\n            <div class=\"progress_content\">\r\n              <div class=\"progress_circle blue_progress\">\r\n                <svg width=\"50\" height=\"50\" viewBox=\"0 0 80 80\">\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"rgba(58,147,255,0.2)\" stroke-width=\"8\" />\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"#3A61CD\" stroke-width=\"8\" stroke-dasharray=\"188.4\"\r\n                    :stroke-dashoffset=\"registerCircleOffset\" stroke-linecap=\"round\" transform=\"rotate(-90 40 40)\" />\r\n                </svg>\r\n              </div>\r\n              <div class=\"progress_info\">\r\n                <div class=\"progress_label\">立案率</div>\r\n                <span class=\"progress_value\" style=\"color: #3A61CD;\">{{ registerRate }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"progress_card\">\r\n            <div class=\"progress_content\">\r\n              <div class=\"progress_circle green_progress\">\r\n                <svg width=\"50\" height=\"50\" viewBox=\"0 0 80 80\">\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"rgba(58,147,255,0.2)\" stroke-width=\"8\" />\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"#57BCAA\" stroke-width=\"8\" stroke-dasharray=\"188.4\"\r\n                    :stroke-dashoffset=\"replyCircleOffset\" stroke-linecap=\"round\" transform=\"rotate(-90 40 40)\" />\r\n                </svg>\r\n              </div>\r\n              <div class=\"progress_info\">\r\n                <div class=\"progress_label\">答复率</div>\r\n                <span class=\"progress_value\" style=\"color: #57BCAA;\">{{ replyRate }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 提交情况 -->\r\n    <div class=\"submit_status_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">提交情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"submit_status\"></div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { toRefs, reactive, computed } from 'vue'\r\nexport default {\r\n  name: 'ProposalWork',\r\n  setup () {\r\n    const data = reactive({\r\n      // 提案统计数据\r\n      proposalTotal: 1500, // 提案总件数\r\n      registerTotal: 600, // 立案总件数\r\n      replyTotal: 600, // 答复总件数\r\n      // 计算属性相关数据\r\n      circleRadius: 30, // 圆形进度条半径\r\n      circleStrokeWidth: 8 // 圆形进度条线宽\r\n    })\r\n\r\n    // 计算立案率\r\n    const registerRate = computed(() => {\r\n      if (data.proposalTotal === 0) return '0%'\r\n      return Math.round((data.registerTotal / data.proposalTotal) * 100) + '%'\r\n    })\r\n\r\n    // 计算答复率\r\n    const replyRate = computed(() => {\r\n      if (data.registerTotal === 0) return '0%'\r\n      return Math.round((data.replyTotal / data.registerTotal) * 100) + '%'\r\n    })\r\n\r\n    // 计算立案率圆形进度条偏移量\r\n    const registerCircleOffset = computed(() => {\r\n      const circumference = 2 * Math.PI * data.circleRadius\r\n      const percentage = data.proposalTotal === 0 ? 0 : (data.registerTotal / data.proposalTotal)\r\n      return circumference - (circumference * percentage)\r\n    })\r\n\r\n    // 计算答复率圆形进度条偏移量\r\n    const replyCircleOffset = computed(() => {\r\n      const circumference = 2 * Math.PI * data.circleRadius\r\n      const percentage = data.registerTotal === 0 ? 0 : (data.replyTotal / data.registerTotal)\r\n      return circumference - (circumference * percentage)\r\n    })\r\n\r\n    return {\r\n      ...toRefs(data),\r\n      registerRate,\r\n      replyRate,\r\n      registerCircleOffset,\r\n      replyCircleOffset\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.ProposalWork {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .header_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 15px 0 15px;\r\n\r\n    .header_left {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_left_line {\r\n        width: 3px;\r\n        height: 14px;\r\n        background: #007AFF;\r\n      }\r\n\r\n      .header_left_title {\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #222222;\r\n        margin-left: 8px;\r\n        font-family: Source Han Serif SC, Source Han Serif SC;\r\n      }\r\n    }\r\n\r\n    .header_right {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_right_text {\r\n        font-weight: 400;\r\n        font-size: 12px;\r\n        color: #999999;\r\n      }\r\n\r\n      .header_right_more {\r\n        font-size: 14px;\r\n        color: #0271E3;\r\n        border-radius: 14px;\r\n        border: 1px solid #0271E3;\r\n        padding: 3px 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .proposal_overall_situation_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .proposal_overall_situation {\r\n      padding: 12px;\r\n\r\n      .statistics_row {\r\n        display: flex;\r\n        gap: 10px;\r\n        margin-bottom: 10px;\r\n\r\n        .statistics_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 17px 8px;\r\n          text-align: center;\r\n\r\n          .card_icon {\r\n            width: 32px;\r\n            height: 32px;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .card_label {\r\n            font-size: 12px;\r\n            color: #999;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .card_value {\r\n            font-size: 20px;\r\n\r\n            &.proposal_total_text {\r\n              color: #308FFF;\r\n            }\r\n\r\n            &.register_text {\r\n              color: #3A61CD;\r\n            }\r\n\r\n            &.reply_text {\r\n              color: #57BCAA;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .progress_row {\r\n        display: flex;\r\n        gap: 10px;\r\n        justify-content: space-between;\r\n\r\n        .progress_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 10px 16px;\r\n          background: #E8F7FF;\r\n\r\n          .progress_content {\r\n            display: flex;\r\n            align-items: center;\r\n          }\r\n\r\n          .progress_circle {\r\n            margin-right: 15px;\r\n            margin-top: 5px;\r\n          }\r\n\r\n          .progress_info {\r\n\r\n            .progress_label {\r\n              font-size: 12px;\r\n              color: #999999;\r\n              margin-bottom: 5px;\r\n            }\r\n\r\n            .progress_value {\r\n              font-size: 20px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .submit_status_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .submit_status {\r\n      padding: 12px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";OAaiBA,UAAoD;OAKpDC,UAA2D;OAK3DC,UAAwD;;EAtBlEC,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAgC;;EAOpCA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC,iBAAiB;EAACC,KAA4B,EAA5B;IAAA;EAAA;;;EAGtBD,KAAK,EAAC;AAAgC;;EAExCA,KAAK,EAAC,iBAAiB;EAACC,KAA4B,EAA5B;IAAA;EAAA;;;EAGtBD,KAAK,EAAC;AAA0B;;EAElCA,KAAK,EAAC,iBAAiB;EAACC,KAA4B,EAA5B;IAAA;EAAA;;;EAGtBD,KAAK,EAAC;AAAuB;;EAGjCA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAA+B;;EACnCE,KAAK,EAAC,IAAI;EAACC,MAAM,EAAC,IAAI;EAACC,OAAO,EAAC;;;;EAMjCJ,KAAK,EAAC;AAAe;;EAElBA,KAAK,EAAC,gBAAgB;EAACC,KAAuB,EAAvB;IAAA;EAAA;;;EAI9BD,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAgC;;EACpCE,KAAK,EAAC,IAAI;EAACC,MAAM,EAAC,IAAI;EAACC,OAAO,EAAC;;;;EAMjCJ,KAAK,EAAC;AAAe;;EAElBA,KAAK,EAAC,gBAAgB;EAACC,KAAuB,EAAvB;IAAA;EAAA;;;uBAtD3CI,mBAAA,CAuEM,OAvENC,UAuEM,GAtEJC,mBAAA,YAAe,EACfC,mBAAA,CA0DM,OA1DNC,UA0DM,G,4BAzDJD,mBAAA,CAKM;IALDR,KAAK,EAAC;EAAY,IACrBQ,mBAAA,CAGM;IAHDR,KAAK,EAAC;EAAa,IACtBQ,mBAAA,CAAsC;IAAhCR,KAAK,EAAC;EAAkB,IAC9BQ,mBAAA,CAA6C;IAAvCR,KAAK,EAAC;EAAmB,GAAC,QAAM,E,wBAG1CQ,mBAAA,CAkDM,OAlDNE,UAkDM,GAjDJF,mBAAA,CAgBM,OAhBNG,UAgBM,GAfJH,mBAAA,CAIM,OAJNI,UAIM,G,0BAHJJ,mBAAA,CAA0F;IAArFK,GAAoD,EAApDhB,UAAoD;IAACiB,GAAG,EAAC,OAAO;IAACd,KAAK,EAAC;yDAC5EQ,mBAAA,CAAmC;IAA9BR,KAAK,EAAC;EAAY,GAAC,OAAK,sBAC7BQ,mBAAA,CAAqE,OAArEO,UAAqE,EAAAC,gBAAA,CAAtBC,IAAA,CAAAC,aAAa,iB,GAE9DV,mBAAA,CAIM,OAJNW,UAIM,G,0BAHJX,mBAAA,CAAiG;IAA5FK,GAA2D,EAA3Df,UAA2D;IAACgB,GAAG,EAAC,OAAO;IAACd,KAAK,EAAC;yDACnFQ,mBAAA,CAAmC;IAA9BR,KAAK,EAAC;EAAY,GAAC,OAAK,sBAC7BQ,mBAAA,CAA+D,OAA/DY,UAA+D,EAAAJ,gBAAA,CAAtBC,IAAA,CAAAI,aAAa,iB,GAExDb,mBAAA,CAIM,OAJNc,UAIM,G,0BAHJd,mBAAA,CAA8F;IAAzFK,GAAwD,EAAxDd,UAAwD;IAACe,GAAG,EAAC,OAAO;IAACd,KAAK,EAAC;yDAChFQ,mBAAA,CAAmC;IAA9BR,KAAK,EAAC;EAAY,GAAC,OAAK,sBAC7BQ,mBAAA,CAAyD,OAAzDe,WAAyD,EAAAP,gBAAA,CAAnBC,IAAA,CAAAO,UAAU,iB,KAGpDhB,mBAAA,CA+BM,OA/BNiB,WA+BM,GA9BJjB,mBAAA,CAcM,OAdNkB,WAcM,GAbJlB,mBAAA,CAYM,OAZNmB,WAYM,GAXJnB,mBAAA,CAMM,OANNoB,WAMM,I,cALJvB,mBAAA,CAIM,OAJNwB,WAIM,G,0BAHJrB,mBAAA,CAA4F;IAApFsB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,IAAI;IAACC,IAAI,EAAC,MAAM;IAACC,MAAM,EAAC,sBAAsB;IAAC,cAAY,EAAC;+BACtF1B,mBAAA,CACmG;IAD3FsB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,IAAI;IAACC,IAAI,EAAC,MAAM;IAACC,MAAM,EAAC,SAAS;IAAC,cAAY,EAAC,GAAG;IAAC,kBAAgB,EAAC,OAAO;IAClG,mBAAiB,EAAEC,MAAA,CAAAC,oBAAoB;IAAE,gBAAc,EAAC,OAAO;IAACC,SAAS,EAAC;6CAGjF7B,mBAAA,CAGM,OAHN8B,WAGM,G,0BAFJ9B,mBAAA,CAAqC;IAAhCR,KAAK,EAAC;EAAgB,GAAC,KAAG,sBAC/BQ,mBAAA,CAA8E,QAA9E+B,WAA8E,EAAAvB,gBAAA,CAAtBmB,MAAA,CAAAK,YAAY,iB,OAI1EhC,mBAAA,CAcM,OAdNiC,WAcM,GAbJjC,mBAAA,CAYM,OAZNkC,WAYM,GAXJlC,mBAAA,CAMM,OANNmC,WAMM,I,cALJtC,mBAAA,CAIM,OAJNuC,WAIM,G,0BAHJpC,mBAAA,CAA4F;IAApFsB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,IAAI;IAACC,IAAI,EAAC,MAAM;IAACC,MAAM,EAAC,sBAAsB;IAAC,cAAY,EAAC;+BACtF1B,mBAAA,CACgG;IADxFsB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,IAAI;IAACC,IAAI,EAAC,MAAM;IAACC,MAAM,EAAC,SAAS;IAAC,cAAY,EAAC,GAAG;IAAC,kBAAgB,EAAC,OAAO;IAClG,mBAAiB,EAAEC,MAAA,CAAAU,iBAAiB;IAAE,gBAAc,EAAC,OAAO;IAACR,SAAS,EAAC;6CAG9E7B,mBAAA,CAGM,OAHNsC,WAGM,G,0BAFJtC,mBAAA,CAAqC;IAAhCR,KAAK,EAAC;EAAgB,GAAC,KAAG,sBAC/BQ,mBAAA,CAA2E,QAA3EuC,WAA2E,EAAA/B,gBAAA,CAAnBmB,MAAA,CAAAa,SAAS,iB,aAO7EzC,mBAAA,UAAa,E", "ignoreList": []}]}