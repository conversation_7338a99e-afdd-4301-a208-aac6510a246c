{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue?vue&type=template&id=6b17b738&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue", "mtime": 1753938495962}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_imports_0", "_imports_1", "_imports_2", "class", "style", "width", "height", "viewBox", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "src", "alt", "_hoisted_6", "_toDisplayString", "_ctx", "proposalTotal", "_hoisted_7", "_hoisted_8", "registerTotal", "_hoisted_9", "_hoisted_10", "replyTotal", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "cx", "cy", "r", "fill", "stroke", "$setup", "registerCircleOffset", "transform", "_hoisted_17", "_hoisted_18", "registerRate", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "replyCircleOffset", "_hoisted_24", "_hoisted_25", "replyRate", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "_hoisted_30", "committeeProposal", "_hoisted_31", "_hoisted_32", "boundaryProposal", "_hoisted_33", "_hoisted_34", "organizationProposal", "_hoisted_35", "_hoisted_36", "_createVNode", "_component_pieEchartsLegend", "id", "dataList", "typeAnalysisList", "title", "_hoisted_37", "_hoisted_38", "_component_ProgressBarChart", "desc", "replyType", "face", "percent", "value", "faceNum", "color", "letter", "letterNum", "_hoisted_39", "_hoisted_40"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ProposalWork\">\r\n    <!-- 提案整体情况 -->\r\n    <div class=\"proposal_overall_situation_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">提案整体情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"proposal_overall_situation\">\r\n        <div class=\"statistics_row\">\r\n          <div class=\"statistics_card\" style=\"background: #E8F7FF;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_total.png\" alt=\"提案总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">提案总件数</div>\r\n            <div class=\"card_value proposal_total_text\">{{ proposalTotal }}</div>\r\n          </div>\r\n          <div class=\"statistics_card\" style=\"background: #F1F5FF;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_register_num.png\" alt=\"立案总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">立案总件数</div>\r\n            <div class=\"card_value register_text\">{{ registerTotal }}</div>\r\n          </div>\r\n          <div class=\"statistics_card\" style=\"background: #DAF6F2;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_reply_num.png\" alt=\"答复总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">答复总件数</div>\r\n            <div class=\"card_value reply_text\">{{ replyTotal }}</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"progress_row\">\r\n          <div class=\"progress_card\">\r\n            <div class=\"progress_content\">\r\n              <div class=\"progress_circle blue_progress\">\r\n                <svg width=\"50\" height=\"50\" viewBox=\"0 0 80 80\">\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"rgba(58,147,255,0.2)\" stroke-width=\"8\" />\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"#3A61CD\" stroke-width=\"8\" stroke-dasharray=\"188.4\"\r\n                    :stroke-dashoffset=\"registerCircleOffset\" stroke-linecap=\"round\" transform=\"rotate(-90 40 40)\" />\r\n                </svg>\r\n              </div>\r\n              <div class=\"progress_info\">\r\n                <div class=\"progress_label\">立案率</div>\r\n                <span class=\"progress_value\" style=\"color: #3A61CD;\">{{ registerRate }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"progress_card\">\r\n            <div class=\"progress_content\">\r\n              <div class=\"progress_circle green_progress\">\r\n                <svg width=\"50\" height=\"50\" viewBox=\"0 0 80 80\">\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"rgba(58,147,255,0.2)\" stroke-width=\"8\" />\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"#57BCAA\" stroke-width=\"8\" stroke-dasharray=\"188.4\"\r\n                    :stroke-dashoffset=\"replyCircleOffset\" stroke-linecap=\"round\" transform=\"rotate(-90 40 40)\" />\r\n                </svg>\r\n              </div>\r\n              <div class=\"progress_info\">\r\n                <div class=\"progress_label\">答复率</div>\r\n                <span class=\"progress_value\" style=\"color: #57BCAA;\">{{ replyRate }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 提交情况 -->\r\n    <div class=\"submit_status_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">提交情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"submit_status\">\r\n        <div class=\"submit_statistics_row\">\r\n          <div class=\"submit_card\" style=\"background: #E8F4FF;\">\r\n            <div class=\"submit_value committee_text\">{{ committeeProposal }}</div>\r\n            <div class=\"submit_label\">委员提案</div>\r\n          </div>\r\n          <div class=\"submit_card\" style=\"background: #FFF8E1;\">\r\n            <div class=\"submit_value boundary_text\">{{ boundaryProposal }}</div>\r\n            <div class=\"submit_label\">界别提案</div>\r\n          </div>\r\n          <div class=\"submit_card\" style=\"background: #E8F5E8;\">\r\n            <div class=\"submit_value organization_text\">{{ organizationProposal }}</div>\r\n            <div class=\"submit_label\">组织提案</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 类型分布 -->\r\n    <div class=\"proposal_type_analysis_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">类型分布</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"proposal_type_analysis\">\r\n        <pieEchartsLegend id=\"typeAnalysisPie\" :dataList=\"typeAnalysisList\" title=\"类型分析\" />\r\n      </div>\r\n    </div>\r\n    <!-- 答复类型 -->\r\n    <div class=\"reply_type_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">答复类型</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"reply_type\">\r\n        <ProgressBarChart title=\"面复\" :desc=\"`占总件数${replyType.face}%`\" :percent=\"replyType.face\"\r\n          :value=\"replyType.faceNum\" color=\"linear-gradient(90deg, rgba(58,147,255,0.3) 0%, #3A93FF 100%)\" />\r\n        <ProgressBarChart title=\"函复\" :desc=\"`占提交数${replyType.letter}%`\" :percent=\"replyType.letter\"\r\n          :value=\"replyType.letterNum\" color=\"linear-gradient(90deg, rgba(255,115,141,0.3) 0%, #FF738D 100%)\" />\r\n      </div>\r\n    </div>\r\n    <!-- 办理单位统计（前十） -->\r\n    <div class=\"reply_type_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">办理单位统计（前十）</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"reply_type\">\r\n        <ProgressBarChart title=\"面复\" :desc=\"`占总件数${replyType.face}%`\" :percent=\"replyType.face\"\r\n          :value=\"replyType.faceNum\" color=\"linear-gradient(90deg, rgba(58,147,255,0.3) 0%, #3A93FF 100%)\" />\r\n        <ProgressBarChart title=\"函复\" :desc=\"`占提交数${replyType.letter}%`\" :percent=\"replyType.letter\"\r\n          :value=\"replyType.letterNum\" color=\"linear-gradient(90deg, rgba(255,115,141,0.3) 0%, #FF738D 100%)\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { toRefs, reactive, computed } from 'vue'\r\nimport pieEchartsLegend from './echartsComponent/pieEchartsLegend.vue'\r\nimport ProgressBarChart from './echartsComponent/ProgressBarChart.vue'\r\nexport default {\r\n  components: { pieEchartsLegend, ProgressBarChart },\r\n  name: 'ProposalWork',\r\n  setup () {\r\n    const data = reactive({\r\n      // 提案统计数据\r\n      proposalTotal: 1500, // 提案总件数\r\n      registerTotal: 600, // 立案总件数\r\n      replyTotal: 600, // 答复总件数\r\n\r\n      // 提交情况数据\r\n      committeeProposal: 456, // 委员提案\r\n      boundaryProposal: 354, // 界别提案\r\n      organizationProposal: 221, // 组织提案\r\n\r\n      // 计算属性相关数据\r\n      circleRadius: 30, // 圆形进度条半径\r\n      circleStrokeWidth: 8, // 圆形进度条线宽\r\n      typeAnalysisList: [\r\n        { name: '发改财政', value: 22.52, color: '#3DC3F0' },\r\n        { name: '民政市场', value: 18.33, color: '#4AC6A8' },\r\n        { name: '公安司法', value: 12.5, color: '#F9C846' },\r\n        { name: '区市政府', value: 11.34, color: '#6DD3A0' },\r\n        { name: '科技工信', value: 9.56, color: '#7B8DF9' },\r\n        { name: '教育文化', value: 8.09, color: '#F97C9C' },\r\n        { name: '派出机构', value: 4.21, color: '#F9A846' },\r\n        { name: '驻青单位', value: 3.71, color: '#F97C46' },\r\n        { name: '住建交通', value: 3.65, color: '#A97CF9' },\r\n        { name: '农村卫生', value: 3.21, color: '#4A9CF9' },\r\n        { name: '其他机构', value: 1.86, color: '#BFBFBF' },\r\n        { name: '党群其他', value: 1.02, color: '#F9C8C8' }\r\n      ],\r\n      replyType: { face: 60, faceNum: 360, letter: 40, letterNum: 240 }\r\n    })\r\n\r\n    // 计算立案率\r\n    const registerRate = computed(() => {\r\n      if (data.proposalTotal === 0) return '0%'\r\n      return Math.round((data.registerTotal / data.proposalTotal) * 100) + '%'\r\n    })\r\n\r\n    // 计算答复率\r\n    const replyRate = computed(() => {\r\n      if (data.registerTotal === 0) return '0%'\r\n      return Math.round((data.replyTotal / data.registerTotal) * 100) + '%'\r\n    })\r\n\r\n    // 计算立案率圆形进度条偏移量\r\n    const registerCircleOffset = computed(() => {\r\n      const circumference = 2 * Math.PI * data.circleRadius\r\n      const percentage = data.proposalTotal === 0 ? 0 : (data.registerTotal / data.proposalTotal)\r\n      return circumference - (circumference * percentage)\r\n    })\r\n\r\n    // 计算答复率圆形进度条偏移量\r\n    const replyCircleOffset = computed(() => {\r\n      const circumference = 2 * Math.PI * data.circleRadius\r\n      const percentage = data.registerTotal === 0 ? 0 : (data.replyTotal / data.registerTotal)\r\n      return circumference - (circumference * percentage)\r\n    })\r\n\r\n    return {\r\n      ...toRefs(data),\r\n      registerRate,\r\n      replyRate,\r\n      registerCircleOffset,\r\n      replyCircleOffset\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.ProposalWork {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .header_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 15px 0 15px;\r\n\r\n    .header_left {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_left_line {\r\n        width: 3px;\r\n        height: 14px;\r\n        background: #007AFF;\r\n      }\r\n\r\n      .header_left_title {\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #222222;\r\n        margin-left: 8px;\r\n        font-family: Source Han Serif SC, Source Han Serif SC;\r\n      }\r\n    }\r\n\r\n    .header_right {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_right_text {\r\n        font-weight: 400;\r\n        font-size: 12px;\r\n        color: #999999;\r\n      }\r\n\r\n      .header_right_more {\r\n        font-size: 14px;\r\n        color: #0271E3;\r\n        border-radius: 14px;\r\n        border: 1px solid #0271E3;\r\n        padding: 3px 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .proposal_overall_situation_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .proposal_overall_situation {\r\n      padding: 12px;\r\n\r\n      .statistics_row {\r\n        display: flex;\r\n        gap: 10px;\r\n        margin-bottom: 10px;\r\n\r\n        .statistics_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 17px 8px;\r\n          text-align: center;\r\n\r\n          .card_icon {\r\n            width: 32px;\r\n            height: 32px;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .card_label {\r\n            font-size: 12px;\r\n            color: #999;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .card_value {\r\n            font-size: 20px;\r\n\r\n            &.proposal_total_text {\r\n              color: #308FFF;\r\n            }\r\n\r\n            &.register_text {\r\n              color: #3A61CD;\r\n            }\r\n\r\n            &.reply_text {\r\n              color: #57BCAA;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .progress_row {\r\n        display: flex;\r\n        gap: 10px;\r\n        justify-content: space-between;\r\n\r\n        .progress_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 10px 16px;\r\n          background: #E8F7FF;\r\n\r\n          .progress_content {\r\n            display: flex;\r\n            align-items: center;\r\n          }\r\n\r\n          .progress_circle {\r\n            margin-right: 15px;\r\n            margin-top: 5px;\r\n          }\r\n\r\n          .progress_info {\r\n\r\n            .progress_label {\r\n              font-size: 12px;\r\n              color: #999999;\r\n              margin-bottom: 5px;\r\n            }\r\n\r\n            .progress_value {\r\n              font-size: 20px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .submit_status_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .submit_status {\r\n      padding: 12px;\r\n\r\n      .submit_statistics_row {\r\n        display: flex;\r\n        gap: 10px;\r\n\r\n        .submit_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 24px 16px;\r\n          text-align: center;\r\n\r\n          .submit_value {\r\n            font-size: 20px;\r\n            margin-bottom: 10px;\r\n\r\n            &.committee_text {\r\n              color: #3B91FB;\r\n            }\r\n\r\n            &.boundary_text {\r\n              color: #EAB308;\r\n            }\r\n\r\n            &.organization_text {\r\n              color: #43DDBB;\r\n            }\r\n          }\r\n\r\n          .submit_label {\r\n            font-size: 14px;\r\n            color: #666666;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .proposal_type_analysis_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .proposal_type_analysis {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n    }\r\n  }\r\n\r\n  .reply_type_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .reply_type {\r\n      padding: 5px 18px 18px 18px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";OAaiBA,UAAoD;OAKpDC,UAA2D;OAK3DC,UAAwD;;EAtBlEC,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAgC;;EAOpCA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC,iBAAiB;EAACC,KAA4B,EAA5B;IAAA;EAAA;;;EAGtBD,KAAK,EAAC;AAAgC;;EAExCA,KAAK,EAAC,iBAAiB;EAACC,KAA4B,EAA5B;IAAA;EAAA;;;EAGtBD,KAAK,EAAC;AAA0B;;EAElCA,KAAK,EAAC,iBAAiB;EAACC,KAA4B,EAA5B;IAAA;EAAA;;;EAGtBD,KAAK,EAAC;AAAuB;;EAGjCA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAA+B;;EACnCE,KAAK,EAAC,IAAI;EAACC,MAAM,EAAC,IAAI;EAACC,OAAO,EAAC;;;;EAMjCJ,KAAK,EAAC;AAAe;;EAElBA,KAAK,EAAC,gBAAgB;EAACC,KAAuB,EAAvB;IAAA;EAAA;;;EAI9BD,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAgC;;EACpCE,KAAK,EAAC,IAAI;EAACC,MAAM,EAAC,IAAI;EAACC,OAAO,EAAC;;;;EAMjCJ,KAAK,EAAC;AAAe;;EAElBA,KAAK,EAAC,gBAAgB;EAACC,KAAuB,EAAvB;IAAA;EAAA;;;EAQpCD,KAAK,EAAC;AAAmB;;EAOvBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC,aAAa;EAACC,KAA4B,EAA5B;IAAA;EAAA;;;EAClBD,KAAK,EAAC;AAA6B;;EAGrCA,KAAK,EAAC,aAAa;EAACC,KAA4B,EAA5B;IAAA;EAAA;;;EAClBD,KAAK,EAAC;AAA4B;;EAGpCA,KAAK,EAAC,aAAa;EAACC,KAA4B,EAA5B;IAAA;EAAA;;;EAClBD,KAAK,EAAC;AAAgC;;EAO9CA,KAAK,EAAC;AAA4B;;EAOhCA,KAAK,EAAC;AAAwB;;EAKhCA,KAAK,EAAC;AAAgB;;EAOpBA,KAAK,EAAC;AAAY;;EAQpBA,KAAK,EAAC;AAAgB;;EAOpBA,KAAK,EAAC;AAAY;;;;uBAzH3BK,mBAAA,CAgIM,OAhINC,UAgIM,GA/HJC,mBAAA,YAAe,EACfC,mBAAA,CA0DM,OA1DNC,UA0DM,G,4BAzDJD,mBAAA,CAKM;IALDR,KAAK,EAAC;EAAY,IACrBQ,mBAAA,CAGM;IAHDR,KAAK,EAAC;EAAa,IACtBQ,mBAAA,CAAsC;IAAhCR,KAAK,EAAC;EAAkB,IAC9BQ,mBAAA,CAA6C;IAAvCR,KAAK,EAAC;EAAmB,GAAC,QAAM,E,wBAG1CQ,mBAAA,CAkDM,OAlDNE,UAkDM,GAjDJF,mBAAA,CAgBM,OAhBNG,UAgBM,GAfJH,mBAAA,CAIM,OAJNI,UAIM,G,0BAHJJ,mBAAA,CAA0F;IAArFK,GAAoD,EAApDhB,UAAoD;IAACiB,GAAG,EAAC,OAAO;IAACd,KAAK,EAAC;yDAC5EQ,mBAAA,CAAmC;IAA9BR,KAAK,EAAC;EAAY,GAAC,OAAK,sBAC7BQ,mBAAA,CAAqE,OAArEO,UAAqE,EAAAC,gBAAA,CAAtBC,IAAA,CAAAC,aAAa,iB,GAE9DV,mBAAA,CAIM,OAJNW,UAIM,G,0BAHJX,mBAAA,CAAiG;IAA5FK,GAA2D,EAA3Df,UAA2D;IAACgB,GAAG,EAAC,OAAO;IAACd,KAAK,EAAC;yDACnFQ,mBAAA,CAAmC;IAA9BR,KAAK,EAAC;EAAY,GAAC,OAAK,sBAC7BQ,mBAAA,CAA+D,OAA/DY,UAA+D,EAAAJ,gBAAA,CAAtBC,IAAA,CAAAI,aAAa,iB,GAExDb,mBAAA,CAIM,OAJNc,UAIM,G,0BAHJd,mBAAA,CAA8F;IAAzFK,GAAwD,EAAxDd,UAAwD;IAACe,GAAG,EAAC,OAAO;IAACd,KAAK,EAAC;yDAChFQ,mBAAA,CAAmC;IAA9BR,KAAK,EAAC;EAAY,GAAC,OAAK,sBAC7BQ,mBAAA,CAAyD,OAAzDe,WAAyD,EAAAP,gBAAA,CAAnBC,IAAA,CAAAO,UAAU,iB,KAGpDhB,mBAAA,CA+BM,OA/BNiB,WA+BM,GA9BJjB,mBAAA,CAcM,OAdNkB,WAcM,GAbJlB,mBAAA,CAYM,OAZNmB,WAYM,GAXJnB,mBAAA,CAMM,OANNoB,WAMM,I,cALJvB,mBAAA,CAIM,OAJNwB,WAIM,G,0BAHJrB,mBAAA,CAA4F;IAApFsB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,IAAI;IAACC,IAAI,EAAC,MAAM;IAACC,MAAM,EAAC,sBAAsB;IAAC,cAAY,EAAC;+BACtF1B,mBAAA,CACmG;IAD3FsB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,IAAI;IAACC,IAAI,EAAC,MAAM;IAACC,MAAM,EAAC,SAAS;IAAC,cAAY,EAAC,GAAG;IAAC,kBAAgB,EAAC,OAAO;IAClG,mBAAiB,EAAEC,MAAA,CAAAC,oBAAoB;IAAE,gBAAc,EAAC,OAAO;IAACC,SAAS,EAAC;6CAGjF7B,mBAAA,CAGM,OAHN8B,WAGM,G,0BAFJ9B,mBAAA,CAAqC;IAAhCR,KAAK,EAAC;EAAgB,GAAC,KAAG,sBAC/BQ,mBAAA,CAA8E,QAA9E+B,WAA8E,EAAAvB,gBAAA,CAAtBmB,MAAA,CAAAK,YAAY,iB,OAI1EhC,mBAAA,CAcM,OAdNiC,WAcM,GAbJjC,mBAAA,CAYM,OAZNkC,WAYM,GAXJlC,mBAAA,CAMM,OANNmC,WAMM,I,cALJtC,mBAAA,CAIM,OAJNuC,WAIM,G,0BAHJpC,mBAAA,CAA4F;IAApFsB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,IAAI;IAACC,IAAI,EAAC,MAAM;IAACC,MAAM,EAAC,sBAAsB;IAAC,cAAY,EAAC;+BACtF1B,mBAAA,CACgG;IADxFsB,EAAE,EAAC,IAAI;IAACC,EAAE,EAAC,IAAI;IAACC,CAAC,EAAC,IAAI;IAACC,IAAI,EAAC,MAAM;IAACC,MAAM,EAAC,SAAS;IAAC,cAAY,EAAC,GAAG;IAAC,kBAAgB,EAAC,OAAO;IAClG,mBAAiB,EAAEC,MAAA,CAAAU,iBAAiB;IAAE,gBAAc,EAAC,OAAO;IAACR,SAAS,EAAC;6CAG9E7B,mBAAA,CAGM,OAHNsC,WAGM,G,0BAFJtC,mBAAA,CAAqC;IAAhCR,KAAK,EAAC;EAAgB,GAAC,KAAG,sBAC/BQ,mBAAA,CAA2E,QAA3EuC,WAA2E,EAAA/B,gBAAA,CAAnBmB,MAAA,CAAAa,SAAS,iB,aAO7EzC,mBAAA,UAAa,EACbC,mBAAA,CAuBM,OAvBNyC,WAuBM,G,4BAtBJzC,mBAAA,CAKM;IALDR,KAAK,EAAC;EAAY,IACrBQ,mBAAA,CAGM;IAHDR,KAAK,EAAC;EAAa,IACtBQ,mBAAA,CAAsC;IAAhCR,KAAK,EAAC;EAAkB,IAC9BQ,mBAAA,CAA2C;IAArCR,KAAK,EAAC;EAAmB,GAAC,MAAI,E,wBAGxCQ,mBAAA,CAeM,OAfN0C,WAeM,GAdJ1C,mBAAA,CAaM,OAbN2C,WAaM,GAZJ3C,mBAAA,CAGM,OAHN4C,WAGM,GAFJ5C,mBAAA,CAAsE,OAAtE6C,WAAsE,EAAArC,gBAAA,CAA1BC,IAAA,CAAAqC,iBAAiB,kB,4BAC7D9C,mBAAA,CAAoC;IAA/BR,KAAK,EAAC;EAAc,GAAC,MAAI,qB,GAEhCQ,mBAAA,CAGM,OAHN+C,WAGM,GAFJ/C,mBAAA,CAAoE,OAApEgD,WAAoE,EAAAxC,gBAAA,CAAzBC,IAAA,CAAAwC,gBAAgB,kB,4BAC3DjD,mBAAA,CAAoC;IAA/BR,KAAK,EAAC;EAAc,GAAC,MAAI,qB,GAEhCQ,mBAAA,CAGM,OAHNkD,WAGM,GAFJlD,mBAAA,CAA4E,OAA5EmD,WAA4E,EAAA3C,gBAAA,CAA7BC,IAAA,CAAA2C,oBAAoB,kB,4BACnEpD,mBAAA,CAAoC;IAA/BR,KAAK,EAAC;EAAc,GAAC,MAAI,qB,SAKtCO,mBAAA,UAAa,EACbC,mBAAA,CAUM,OAVNqD,WAUM,G,4BATJrD,mBAAA,CAKM;IALDR,KAAK,EAAC;EAAY,IACrBQ,mBAAA,CAGM;IAHDR,KAAK,EAAC;EAAa,IACtBQ,mBAAA,CAAsC;IAAhCR,KAAK,EAAC;EAAkB,IAC9BQ,mBAAA,CAA2C;IAArCR,KAAK,EAAC;EAAmB,GAAC,MAAI,E,wBAGxCQ,mBAAA,CAEM,OAFNsD,WAEM,GADJC,YAAA,CAAmFC,2BAAA;IAAjEC,EAAE,EAAC,iBAAiB;IAAEC,QAAQ,EAAEjD,IAAA,CAAAkD,gBAAgB;IAAEC,KAAK,EAAC;6CAG9E7D,mBAAA,UAAa,EACbC,mBAAA,CAaM,OAbN6D,WAaM,G,4BAZJ7D,mBAAA,CAKM;IALDR,KAAK,EAAC;EAAY,IACrBQ,mBAAA,CAGM;IAHDR,KAAK,EAAC;EAAa,IACtBQ,mBAAA,CAAsC;IAAhCR,KAAK,EAAC;EAAkB,IAC9BQ,mBAAA,CAA2C;IAArCR,KAAK,EAAC;EAAmB,GAAC,MAAI,E,wBAGxCQ,mBAAA,CAKM,OALN8D,WAKM,GAJJP,YAAA,CACqGQ,2BAAA;IADnFH,KAAK,EAAC,IAAI;IAAEI,IAAI,SAASvD,IAAA,CAAAwD,SAAS,CAACC,IAAI;IAAMC,OAAO,EAAE1D,IAAA,CAAAwD,SAAS,CAACC,IAAI;IACnFE,KAAK,EAAE3D,IAAA,CAAAwD,SAAS,CAACI,OAAO;IAAEC,KAAK,EAAC;yDACnCf,YAAA,CACwGQ,2BAAA;IADtFH,KAAK,EAAC,IAAI;IAAEI,IAAI,SAASvD,IAAA,CAAAwD,SAAS,CAACM,MAAM;IAAMJ,OAAO,EAAE1D,IAAA,CAAAwD,SAAS,CAACM,MAAM;IACvFH,KAAK,EAAE3D,IAAA,CAAAwD,SAAS,CAACO,SAAS;IAAEF,KAAK,EAAC;6DAGzCvE,mBAAA,gBAAmB,EACnBC,mBAAA,CAaM,OAbNyE,WAaM,G,4BAZJzE,mBAAA,CAKM;IALDR,KAAK,EAAC;EAAY,IACrBQ,mBAAA,CAGM;IAHDR,KAAK,EAAC;EAAa,IACtBQ,mBAAA,CAAsC;IAAhCR,KAAK,EAAC;EAAkB,IAC9BQ,mBAAA,CAAiD;IAA3CR,KAAK,EAAC;EAAmB,GAAC,YAAU,E,wBAG9CQ,mBAAA,CAKM,OALN0E,WAKM,GAJJnB,YAAA,CACqGQ,2BAAA;IADnFH,KAAK,EAAC,IAAI;IAAEI,IAAI,SAASvD,IAAA,CAAAwD,SAAS,CAACC,IAAI;IAAMC,OAAO,EAAE1D,IAAA,CAAAwD,SAAS,CAACC,IAAI;IACnFE,KAAK,EAAE3D,IAAA,CAAAwD,SAAS,CAACI,OAAO;IAAEC,KAAK,EAAC;yDACnCf,YAAA,CACwGQ,2BAAA;IADtFH,KAAK,EAAC,IAAI;IAAEI,IAAI,SAASvD,IAAA,CAAAwD,SAAS,CAACM,MAAM;IAAMJ,OAAO,EAAE1D,IAAA,CAAAwD,SAAS,CAACM,MAAM;IACvFH,KAAK,EAAE3D,IAAA,CAAAwD,SAAS,CAACO,SAAS;IAAEF,KAAK,EAAC", "ignoreList": []}]}