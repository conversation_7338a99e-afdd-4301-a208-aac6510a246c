{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\PublicOpinion.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\PublicOpinion.vue", "mtime": 1753943363546}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\PublicOpinion.vue"], "names": [], "mappings": ";AA0IA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC1H,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;MACzG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;MAC9F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAClE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAClE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpE,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACjE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACjE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnE,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACvC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACtC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACvC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACtC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACtC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACtC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACtC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACxC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACtC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACrC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACtC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACrC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACtC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACrC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACrC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACrC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACrC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MACrC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACpC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACpC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACpC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACrC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACvC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACpC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACpC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACpC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACpC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACpC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACvC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACxB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACzF,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAC3F,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACzF,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAC1F,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAC1F,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QAC5F,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACzF,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;MAC5F,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACzB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MAC7B,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACzB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAC9B;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EAC3B;AACF", "file": "D:/zy/xm/h5/qdzx_h5/product-app/src/views/SmartBrainLargeScreen/component/PublicOpinion.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"PublicOpinion\">\r\n    <!-- 社情民意整体情况 -->\r\n    <div class=\"overall_situation_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">社情民意整体情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"overall_situation_list\">\r\n        <PublicOpinionOverallSituationChart id=\"publicOpinionOverall\" :total-count=\"totalCount\"\r\n          :adopted-count=\"adoptedCount\" />\r\n      </div>\r\n    </div>\r\n    <!-- 采用情况 -->\r\n    <div class=\"adopt_situation_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">采用情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"adopt_situation_list\">\r\n        <ProgressBarChart title=\"委员提交\" desc=\"占总件数60%\" :percent=\"committeeMember.submitPercent\"\r\n          :value=\"committeeMember.submitNum\" color=\"linear-gradient(90deg, rgba(58,147,255,0.3) 0%, #3A93FF 100%)\" />\r\n        <ProgressBarChart title=\"采用情况\" desc=\"占提交数42%\" :percent=\"committeeMember.adoptSituationPercent\"\r\n          :value=\"committeeMember.adoptSituationNum\"\r\n          color=\"linear-gradient(90deg, rgba(255,115,141,0.3) 0%, #FF738D 100%)\" />\r\n        <ProgressBarChart title=\"单位提交\" desc=\"占总件数60%\" :percent=\"unit.submitPercent\" :value=\"unit.submitNum\"\r\n          color=\"linear-gradient(90deg, rgba(58,147,255,0.3) 0%, #3A93FF 100%)\" />\r\n        <ProgressBarChart title=\"采用情况\" desc=\"占提交数42%\" :percent=\"unit.adoptSituationPercent\"\r\n          :value=\"unit.adoptSituationNum\" color=\"linear-gradient(90deg, rgba(255,115,141,0.3) 0%, #FF738D 100%)\" />\r\n      </div>\r\n      <div class=\"adopt_situation_distribution_text\">采用情况分布</div>\r\n      <div class=\"adopt_situation_distribution_charts\">\r\n        <PieChart id=\"adoptSituationDistribution\" :chart-data=\"adoptSituationDistribution\" :radius=\"['35%', '60%']\" />\r\n      </div>\r\n    </div>\r\n    <!-- 批示情况 -->\r\n    <div class=\"instructions_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">批示情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"instructions_list\">\r\n        <div class=\"instructions_item\" v-for=\"(item, index) in instructions\" :key=\"index\"\r\n          :style=\"`background: ${item.bg}`\">\r\n          <div class=\"instructions_item_value\" :style=\"`color: ${item.color}`\">{{ item.value }}</div>\r\n          <div class=\"instructions_item_label\">{{ item.label }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 各单位上报与采用情况 -->\r\n    <div class=\"report_adopt_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">各单位上报与采用情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"report_adopt_list\">\r\n        <DoubleBarChart id=\"party_double_line\" :data=\"partyData\" :legend=\"['报送篇数', '采用篇数']\" :color=\"[\r\n          ['#56A0FF', 'rgba(86,160,255,0.1)'],\r\n          ['#FF738D', 'rgba(255,115,141,0.1)']\r\n        ]\" title=\"八大党派及民主工商联\" style=\"height: 260px;\" />\r\n        <DoubleBarChart id=\"district_double_line\" :data=\"districtData\" :legend=\"['报送篇数', '采用篇数']\" :color=\"[\r\n          ['#56A0FF', 'rgba(86,160,255,0.1)'],\r\n          ['#FF738D', 'rgba(255,115,141,0.1)']\r\n        ]\" title=\"各区市\" style=\"height: 260px;\" />\r\n        <DoubleBarChart id=\"office_double_line\" :data=\"officeData\" :legend=\"['报送篇数', '采用篇数']\" :color=\"[\r\n          ['#56A0FF', 'rgba(86,160,255,0.1)'],\r\n          ['#FF738D', 'rgba(255,115,141,0.1)']\r\n        ]\" title=\"各单位办公室\" style=\"height: 260px;\" />\r\n      </div>\r\n    </div>\r\n    <!-- 个人报送与采用情况 -->\r\n    <div class=\"submit_adopt_situation_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">个人报送与采用情况</span>\r\n        </div>\r\n        <div class=\"header_right\" @click=\"openMore('notice')\">\r\n          <span class=\"header_right_more\">查看全部</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"submit_adopt_situation_list\">\r\n        <div class=\"submit_adopt_table\">\r\n          <div class=\"submit_adopt_table_header\">\r\n            <span class=\"party-header-column\">姓名</span>\r\n            <span class=\"count-header-column\">报送件数</span>\r\n            <span class=\"count-header-column\">采用件数</span>\r\n          </div>\r\n          <div class=\"submit_adopt_table_row\" v-for=\"(item, idx) in submitAdoptSituationData\" :key=\"item.name\"\r\n            :class=\"{ 'row-alt': idx % 2 === 1 }\">\r\n            <span class=\"party-column name-cell\">\r\n              <img :src=\"item.avatar\" class=\"avatar\" />\r\n              <span class=\"name\">{{ item.name }}</span>\r\n            </span>\r\n            <span class=\"count-column\">{{ item.submit }}</span>\r\n            <span class=\"count-column\">{{ item.adopt }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 类别分析 -->\r\n    <div class=\"category_analysis_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">类别分析</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"category_analysis_list\">\r\n        <barChart id=\"categoryAnalysis\" :data=\"categoryData\" :color=\"['#559FFF', 'rgba(85,159,255,0.3)']\"\r\n          style=\"height:200px\" />\r\n      </div>\r\n    </div>\r\n    <!-- 热词分析 -->\r\n    <div class=\"hot_words_analysis_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">热词分析</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"hot_words_analysis_list\">\r\n        <wordCloudEcharts id=\"wordcloud\" :wordList=\"wordCloudData\"\r\n          :colorList=\"['#1890FF', '#FF6B35', '#52C41A', '#722ED1', '#1890FF', '#FF69B4', '#52C41A', '#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']\"\r\n          :sizeRange=\"[2, 10]\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { toRefs, reactive } from 'vue'\r\nimport PublicOpinionOverallSituationChart from './echartsComponent/PublicOpinionOverallSituationChart.vue'\r\nimport ProgressBarChart from './echartsComponent/ProgressBarChart.vue'\r\nimport PieChart from './echartsComponent/PieChart.vue'\r\nimport DoubleBarChart from './echartsComponent/DoubleBarChart.vue'\r\nimport barChart from './echartsComponent/barChart.vue'\r\nimport wordCloudEcharts from './echartsComponent/wordCloudEcharts.vue'\r\nexport default {\r\n  name: 'NetworkPolitics',\r\n  components: { PublicOpinionOverallSituationChart, ProgressBarChart, PieChart, DoubleBarChart, barChart, wordCloudEcharts },\r\n  setup () {\r\n    const data = reactive({\r\n      totalCount: 50,\r\n      adoptedCount: 20,\r\n      committeeMember: { submitPercent: 60, submitNum: 345, adoptSituationPercent: 42, adoptSituationNum: 102 },\r\n      unit: { submitPercent: 60, submitNum: 345, adoptSituationPercent: 42, adoptSituationNum: 102 },\r\n      adoptSituationDistribution: [\r\n        { name: '市政协采用', value: 135, percentage: '40%', color: '#4A90E2' },\r\n        { name: '省政协采用', value: 131, percentage: '30%', color: '#4CD9C0' },\r\n        { name: '全国政协采用', value: 126, percentage: '20%', color: '#F56A6A' }\r\n      ],\r\n      instructions: [\r\n        { label: '市政协批示', value: '135', bg: '#EFF6FF', color: '#3B91FB' },\r\n        { label: '省政协批示', value: '131', bg: '#FDF8F0', color: '#EAB308' },\r\n        { label: '全国政协批示', value: '126', bg: '#F0FDF4', color: '#43DDBB' }\r\n      ],\r\n      partyData: [\r\n        { name: '民革', value1: 102, value2: 80 },\r\n        { name: '民盟', value1: 56, value2: 30 },\r\n        { name: '民建', value1: 120, value2: 75 },\r\n        { name: '民进', value1: 34, value2: 20 },\r\n        { name: '农工', value1: 89, value2: 60 },\r\n        { name: '致公', value1: 95, value2: 70 },\r\n        { name: '九三', value1: 80, value2: 55 },\r\n        { name: '工商联', value1: 45, value2: 25 }\r\n      ],\r\n      districtData: [\r\n        { name: '市南', value1: 55, value2: 13 },\r\n        { name: '市北', value1: 20, value2: 6 },\r\n        { name: '李沧', value1: 35, value2: 10 },\r\n        { name: '崂山', value1: 18, value2: 4 },\r\n        { name: '黄岛', value1: 52, value2: 12 },\r\n        { name: '城阳', value1: 10, value2: 2 },\r\n        { name: '即墨', value1: 25, value2: 5 },\r\n        { name: '胶州', value1: 30, value2: 7 },\r\n        { name: '平度', value1: 12, value2: 3 },\r\n        { name: '莱西', value1: 8, value2: 1 }\r\n      ],\r\n      officeData: [\r\n        { name: '委员', value1: 8, value2: 2 },\r\n        { name: '提案', value1: 2, value2: 1 },\r\n        { name: '经济', value1: 5, value2: 1 },\r\n        { name: '农业', value1: 18, value2: 6 },\r\n        { name: '人口环', value1: 50, value2: 15 },\r\n        { name: '教科', value1: 3, value2: 1 },\r\n        { name: '社法', value1: 2, value2: 1 },\r\n        { name: '港澳', value1: 1, value2: 0 },\r\n        { name: '文史', value1: 2, value2: 1 },\r\n        { name: '民宗', value1: 1, value2: 0 },\r\n        { name: '财经', value1: 50, value2: 15 }\r\n      ],\r\n      submitAdoptSituationData: [\r\n        { avatar: require('@/assets/img/largeScreen/man.png'), name: '张伟', submit: 12, adopt: 3 },\r\n        { avatar: require('@/assets/img/largeScreen/woman.png'), name: '刘芳', submit: 11, adopt: 2 },\r\n        { avatar: require('@/assets/img/largeScreen/man.png'), name: '陈明', submit: 11, adopt: 2 },\r\n        { avatar: require('@/assets/img/largeScreen/man.png'), name: '林小华', submit: 11, adopt: 2 },\r\n        { avatar: require('@/assets/img/largeScreen/man.png'), name: '赵天宇', submit: 10, adopt: 1 },\r\n        { avatar: require('@/assets/img/largeScreen/woman.png'), name: '吴静怡', submit: 10, adopt: 1 },\r\n        { avatar: require('@/assets/img/largeScreen/man.png'), name: '黄浩然', submit: 8, adopt: 1 },\r\n        { avatar: require('@/assets/img/largeScreen/woman.png'), name: '周梦琪', submit: 7, adopt: 1 }\r\n      ],\r\n      categoryData: [\r\n        { name: '社会', value: 115 },\r\n        { name: '政治', value: 140 },\r\n        { name: '经济', value: 60 },\r\n        { name: '文化', value: 115 },\r\n        { name: '生态文明', value: 125 }\r\n      ],\r\n      wordCloudData: [\r\n        { name: '乡村振兴', value: 180 },\r\n        { name: '就业优先', value: 165 },\r\n        { name: '科技创新', value: 150 },\r\n        { name: '改革开放', value: 135 },\r\n        { name: '依法治国', value: 120 },\r\n        { name: '教育人才', value: 105 },\r\n        { name: '社会保障', value: 90 },\r\n        { name: '热词', value: 75 },\r\n        { name: '绿色发展', value: 60 },\r\n        { name: '数字中国', value: 45 },\r\n        { name: '共同富裕', value: 40 },\r\n        { name: '文化自信', value: 35 },\r\n        { name: '国家安全', value: 30 },\r\n        { name: '人民至上', value: 25 },\r\n        { name: '中国式现代化', value: 20 }\r\n      ]\r\n    })\r\n\r\n    return { ...toRefs(data) }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.PublicOpinion {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .header_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 15px 0 15px;\r\n\r\n    .header_left {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_left_line {\r\n        width: 3px;\r\n        height: 14px;\r\n        background: #007AFF;\r\n      }\r\n\r\n      .header_left_title {\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #222222;\r\n        margin-left: 8px;\r\n        font-family: Source Han Serif SC, Source Han Serif SC;\r\n      }\r\n    }\r\n\r\n    .header_right {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_right_text {\r\n        font-weight: 400;\r\n        font-size: 12px;\r\n        color: #999999;\r\n      }\r\n\r\n      .header_right_more {\r\n        font-size: 14px;\r\n        color: #0271E3;\r\n        border-radius: 14px;\r\n        border: 1px solid #0271E3;\r\n        padding: 3px 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .overall_situation_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .overall_situation_list {\r\n      height: 160px;\r\n    }\r\n  }\r\n\r\n  .adopt_situation_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .adopt_situation_list {\r\n      padding: 5px 18px 18px 18px;\r\n    }\r\n\r\n    .adopt_situation_distribution_text {\r\n      font-size: 15px;\r\n      color: #222222;\r\n      font-family: Source Han Serif SC, Source Han Serif SC;\r\n      padding-left: 18px;\r\n    }\r\n\r\n    .adopt_situation_distribution_charts {\r\n      width: 100%;\r\n      height: 260px;\r\n      margin-top: 10px;\r\n    }\r\n  }\r\n\r\n  .instructions_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .instructions_list {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 18px;\r\n\r\n      .instructions_item {\r\n        width: 93px;\r\n        height: 90px;\r\n        border-radius: 4px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        .instructions_item_value {\r\n          font-size: 19px;\r\n        }\r\n\r\n        .instructions_item_label {\r\n          font-size: 13px;\r\n          color: #666666;\r\n          margin-top: 14px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .report_adopt_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n  }\r\n\r\n  .submit_adopt_situation_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .submit_adopt_situation_list {\r\n      margin-top: 15px;\r\n\r\n      .submit_adopt_table {\r\n        width: 100%;\r\n        background: #fff;\r\n\r\n        .submit_adopt_table_header,\r\n        .submit_adopt_table_row {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 12px 15px;\r\n          border-bottom: 1px solid #f0f0f0;\r\n\r\n          &:last-child {\r\n            border-bottom: none;\r\n          }\r\n\r\n          .party-header-column {\r\n            flex: 2;\r\n            text-align: left;\r\n            font-size: 14px;\r\n            color: #999;\r\n          }\r\n\r\n          .count-header-column {\r\n            flex: 1;\r\n            text-align: center;\r\n            font-size: 14px;\r\n            color: #999;\r\n          }\r\n\r\n          .party-column {\r\n            flex: 2;\r\n            text-align: left;\r\n            font-size: 14px;\r\n            color: #333;\r\n          }\r\n\r\n          .count-column {\r\n            flex: 1;\r\n            text-align: center;\r\n            font-size: 14px;\r\n            color: #333;\r\n          }\r\n\r\n          .name-cell {\r\n            display: flex;\r\n            align-items: center;\r\n\r\n            .avatar {\r\n              width: 28px;\r\n              height: 28px;\r\n              border-radius: 50%;\r\n              margin-right: 8px;\r\n              object-fit: cover;\r\n              border: 1px solid #e0e0e0;\r\n            }\r\n\r\n            .name {\r\n              font-size: 14px;\r\n              color: #333;\r\n            }\r\n          }\r\n        }\r\n\r\n        .submit_adopt_table_header {\r\n          background: #F1F8FF;\r\n          font-weight: 600;\r\n          color: #222;\r\n          font-size: 14px;\r\n        }\r\n\r\n        .submit_adopt_table_row {\r\n          background: #fff;\r\n          color: #333;\r\n          font-size: 14px;\r\n\r\n          &.row-alt {\r\n            background: #F1F8FF;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .category_analysis_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .category_analysis_list {\r\n      padding: 18px;\r\n    }\r\n  }\r\n\r\n  .hot_words_analysis_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .hot_words_analysis_list {}\r\n  }\r\n}\r\n</style>\r\n"]}]}