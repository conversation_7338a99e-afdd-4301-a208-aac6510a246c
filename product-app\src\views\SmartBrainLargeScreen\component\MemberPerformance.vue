<template>
  <div class="MemberPerformance">
    <!-- 年度履职汇总 -->
    <div class="performanceunit_summary_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">年度履职汇总</span>
        </div>
      </div>
      <div class="performanceunit_summary">
        <div class="performance_grid">
          <div v-for="(item, index) in performanceList" :key="index" class="performance_card"
            :style="{ background: item.bgColor }">
            <img :src="item.icon" :alt="item.label" class="icon_img" />
            <div class="card_content">
              <div class="card_label">{{ item.label }}</div>
              <div class="card_value" :style="{ color: item.valueColor }">{{ item.value }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 会议类型统计 -->
    <div class="meeting_type_statistics_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">会议类型统计</span>
        </div>
      </div>
      <div class="meeting_type_statistics">
        <PieChart id="meetingType" :chart-data="meetingTypeList" :radius="['35%', '60%']" :center="['50%', '30%']" />
      </div>
    </div>
    <!-- 活动类型统计 -->
    <div class="activity_type_statistics_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">活动类型统计</span>
        </div>
      </div>
      <div class="activity_type_statistics">
        <ActivityTypeChart :data="activityTypeList" height="500px" />
      </div>
    </div>
  </div>
</template>
<script>
import { toRefs, reactive } from 'vue'
import PieChart from './echartsComponent/PieChart.vue'
import ActivityTypeChart from './echartsComponent/ActivityTypeChart.vue'
export default {
  name: 'MemberPerformance',
  components: { PieChart, ActivityTypeChart },
  setup () {
    const data = reactive({
      // 年度履职汇总数据
      performanceList: [
        {
          label: '提交提案',
          value: 1500,
          icon: require('../../../assets/img/largeScreen/icon_proposal.png'),
          bgColor: '#F1F5FF',
          valueColor: '#3A61CD'
        },
        {
          label: '提交社情民意',
          value: 1057,
          icon: require('../../../assets/img/largeScreen/icon_opinion.png'),
          bgColor: '#DAF6F2',
          valueColor: '#57BCAA'
        },
        {
          label: '网络议政',
          value: 215,
          icon: require('../../../assets/img/largeScreen/icon_network.png'),
          bgColor: '#E8F7FF',
          valueColor: '#308FFF'
        },
        {
          label: '参加会议',
          value: 361,
          icon: require('../../../assets/img/largeScreen/icon_meeting.png'),
          bgColor: '#FDF8F0',
          valueColor: '#EAB308'
        },
        {
          label: '参加活动',
          value: 104,
          icon: require('../../../assets/img/largeScreen/icon_activity.png'),
          bgColor: '#FDEFEF',
          valueColor: '#FD7575'
        },
        {
          label: '其他履职',
          value: 241,
          icon: require('../../../assets/img/largeScreen/icon_other.png'),
          bgColor: '#E5F8FF',
          valueColor: '#1FC6FF'
        }
      ],
      meetingTypeList: [
        { name: '其他会议', value: 28, percentage: '', color: '#4488EB' },
        { name: '主席会议', value: 12, percentage: '', color: '#43DDBB' },
        { name: '常委会议', value: 10, percentage: '', color: '#FF6665' },
        { name: '全体会议', value: 2, percentage: '', color: '#ECE522' }
      ],
      // 活动类型统计数据
      activityTypeList: [
        { name: '视察', value: 32, color: '#FF9999' },
        { name: '调研', value: 20, color: '#66B3FF' },
        { name: '协商', value: 14, color: '#FF9999' },
        { name: '学习培训', value: 22, color: '#66B3FF' },
        { name: '联系界别群众', value: 8, color: '#FF9999' },
        { name: '提案审查', value: 25, color: '#66B3FF' },
        { name: '提案答办', value: 13, color: '#FF9999' },
        { name: '提案评议', value: 32, color: '#66B3FF' },
        { name: '委员联络小组', value: 15, color: '#FF9999' },
        { name: '委员会客厅', value: 25, color: '#66B3FF' },
        { name: '联系社会组织', value: 10, color: '#FF9999' },
        { name: '界别群众重点关切问题情况通报会', value: 20, color: '#66B3FF' },
        { name: '社情民意座谈会', value: 16, color: '#FF9999' },
        { name: '接受媒体采访', value: 28, color: '#66B3FF' },
        { name: '经政协推荐参加有关会议活动', value: 5, color: '#FF9999' },
        { name: '宣讲党的政策', value: 7, color: '#66B3FF' },
        { name: '服务为民', value: 32, color: '#FF9999' }
      ]
    })
    return {
      ...toRefs(data)
    }
  }
}
</script>
<style lang="less" scoped>
.MemberPerformance {
  width: 100%;
  height: 100%;

  .header_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 15px 0 15px;

    .header_left {
      display: flex;
      align-items: center;

      .header_left_line {
        width: 3px;
        height: 14px;
        background: #007AFF;
      }

      .header_left_title {
        font-weight: bold;
        font-size: 15px;
        color: #222222;
        margin-left: 8px;
        font-family: Source Han Serif SC, Source Han Serif SC;
      }
    }

    .header_right {
      display: flex;
      align-items: center;

      .header_right_text {
        font-weight: 400;
        font-size: 12px;
        color: #999999;
      }

      .header_right_more {
        font-size: 14px;
        color: #0271E3;
        border-radius: 14px;
        border: 1px solid #0271E3;
        padding: 3px 10px;
      }
    }
  }

  .performanceunit_summary_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .performanceunit_summary {
      padding: 12px;

      .performance_grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;

        .performance_card {
          display: flex;
          align-items: center;
          padding: 16px 20px;
          border-radius: 4px;

          .icon_img {
            width: 32px;
            height: 32px;
            object-fit: contain;
            margin-right: 14px;
          }

          .card_content {
            flex: 1;

            .card_label {
              font-size: 12px;
              color: #999;
              margin-bottom: 7px;
            }

            .card_value {
              font-size: 20px;
              color: #3A61CD;
            }
          }
        }
      }
    }
  }

  .meeting_type_statistics_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .meeting_type_statistics {
      width: 100%;
      height: 260px;
      margin-top: 20px;
    }
  }

  .activity_type_statistics_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .activity_type_statistics {
      padding: 15px;
    }
  }
}
</style>
