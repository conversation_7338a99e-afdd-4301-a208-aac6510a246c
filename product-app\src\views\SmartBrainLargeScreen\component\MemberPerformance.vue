<template>
  <div class="MemberPerformance">
    <!-- 年度履职汇总 -->
    <div class="performanceunit_summary_box">
      <div class="header_box">
        <div class="header_left">
          <span class="header_left_line"></span>
          <span class="header_left_title">年度履职汇总</span>
        </div>
      </div>
      <div class="performanceunit_summary">
        
      </div>
    </div>
  </div>
</template>
<script>
import { toRefs, reactive } from 'vue'
export default {
  name: 'MemberPerformance',
  setup () {
    const data = reactive({

    })
    return {
      ...toRefs(data)
    }
  }
}
</script>
<style lang="less" scoped>
.MemberPerformance {
  width: 100%;
  height: 100%;

  .header_box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 15px 0 15px;

    .header_left {
      display: flex;
      align-items: center;

      .header_left_line {
        width: 3px;
        height: 14px;
        background: #007AFF;
      }

      .header_left_title {
        font-weight: bold;
        font-size: 15px;
        color: #222222;
        margin-left: 8px;
        font-family: Source Han Serif SC, Source Han Serif SC;
      }
    }

    .header_right {
      display: flex;
      align-items: center;

      .header_right_text {
        font-weight: 400;
        font-size: 12px;
        color: #999999;
      }

      .header_right_more {
        font-size: 14px;
        color: #0271E3;
        border-radius: 14px;
        border: 1px solid #0271E3;
        padding: 3px 10px;
      }
    }
  }

  .performanceunit_summary_box {
    background: #FFFFFF;
    border-radius: 6px;
    margin: 12px 0;

    .performanceunit_summary { }
  }
}
</style>
