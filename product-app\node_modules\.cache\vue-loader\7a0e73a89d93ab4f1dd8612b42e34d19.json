{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue?vue&type=template&id=6b17b738&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue", "mtime": 1753940910433}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgY2xhc3M9IlByb3Bvc2FsV29yayI+DQogICAgPCEtLSDmj5DmoYjmlbTkvZPmg4XlhrUgLS0+DQogICAgPGRpdiBjbGFzcz0icHJvcG9zYWxfb3ZlcmFsbF9zaXR1YXRpb25fYm94Ij4NCiAgICAgIDxkaXYgY2xhc3M9ImhlYWRlcl9ib3giPg0KICAgICAgICA8ZGl2IGNsYXNzPSJoZWFkZXJfbGVmdCI+DQogICAgICAgICAgPHNwYW4gY2xhc3M9ImhlYWRlcl9sZWZ0X2xpbmUiPjwvc3Bhbj4NCiAgICAgICAgICA8c3BhbiBjbGFzcz0iaGVhZGVyX2xlZnRfdGl0bGUiPuaPkOahiOaVtOS9k+aDheWGtTwvc3Bhbj4NCiAgICAgICAgPC9kaXY+DQogICAgICA8L2Rpdj4NCiAgICAgIDxkaXYgY2xhc3M9InByb3Bvc2FsX292ZXJhbGxfc2l0dWF0aW9uIj4NCiAgICAgICAgPGRpdiBjbGFzcz0ic3RhdGlzdGljc19yb3ciPg0KICAgICAgICAgIDxkaXYgY2xhc3M9InN0YXRpc3RpY3NfY2FyZCIgc3R5bGU9ImJhY2tncm91bmQ6ICNFOEY3RkY7Ij4NCiAgICAgICAgICAgIDxpbWcgc3JjPSIuLi8uLi8uLi9hc3NldHMvaW1nL2xhcmdlU2NyZWVuL2ljb25fdG90YWwucG5nIiBhbHQ9IuaPkOahiOaAu+S7tuaVsCIgY2xhc3M9ImNhcmRfaWNvbiIgLz4NCiAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNhcmRfbGFiZWwiPuaPkOahiOaAu+S7tuaVsDwvZGl2Pg0KICAgICAgICAgICAgPGRpdiBjbGFzcz0iY2FyZF92YWx1ZSBwcm9wb3NhbF90b3RhbF90ZXh0Ij57eyBwcm9wb3NhbFRvdGFsIH19PC9kaXY+DQogICAgICAgICAgPC9kaXY+DQogICAgICAgICAgPGRpdiBjbGFzcz0ic3RhdGlzdGljc19jYXJkIiBzdHlsZT0iYmFja2dyb3VuZDogI0YxRjVGRjsiPg0KICAgICAgICAgICAgPGltZyBzcmM9Ii4uLy4uLy4uL2Fzc2V0cy9pbWcvbGFyZ2VTY3JlZW4vaWNvbl9yZWdpc3Rlcl9udW0ucG5nIiBhbHQ9Iueri+ahiOaAu+S7tuaVsCIgY2xhc3M9ImNhcmRfaWNvbiIgLz4NCiAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNhcmRfbGFiZWwiPueri+ahiOaAu+S7tuaVsDwvZGl2Pg0KICAgICAgICAgICAgPGRpdiBjbGFzcz0iY2FyZF92YWx1ZSByZWdpc3Rlcl90ZXh0Ij57eyByZWdpc3RlclRvdGFsIH19PC9kaXY+DQogICAgICAgICAgPC9kaXY+DQogICAgICAgICAgPGRpdiBjbGFzcz0ic3RhdGlzdGljc19jYXJkIiBzdHlsZT0iYmFja2dyb3VuZDogI0RBRjZGMjsiPg0KICAgICAgICAgICAgPGltZyBzcmM9Ii4uLy4uLy4uL2Fzc2V0cy9pbWcvbGFyZ2VTY3JlZW4vaWNvbl9yZXBseV9udW0ucG5nIiBhbHQ9IuetlOWkjeaAu+S7tuaVsCIgY2xhc3M9ImNhcmRfaWNvbiIgLz4NCiAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNhcmRfbGFiZWwiPuetlOWkjeaAu+S7tuaVsDwvZGl2Pg0KICAgICAgICAgICAgPGRpdiBjbGFzcz0iY2FyZF92YWx1ZSByZXBseV90ZXh0Ij57eyByZXBseVRvdGFsIH19PC9kaXY+DQogICAgICAgICAgPC9kaXY+DQogICAgICAgIDwvZGl2Pg0KICAgICAgICA8ZGl2IGNsYXNzPSJwcm9ncmVzc19yb3ciPg0KICAgICAgICAgIDxkaXYgY2xhc3M9InByb2dyZXNzX2NhcmQiPg0KICAgICAgICAgICAgPGRpdiBjbGFzcz0icHJvZ3Jlc3NfY29udGVudCI+DQogICAgICAgICAgICAgIDxkaXYgY2xhc3M9InByb2dyZXNzX2NpcmNsZSBibHVlX3Byb2dyZXNzIj4NCiAgICAgICAgICAgICAgICA8c3ZnIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDgwIDgwIj4NCiAgICAgICAgICAgICAgICAgIDxjaXJjbGUgY3g9IjQwIiBjeT0iNDAiIHI9IjMwIiBmaWxsPSJub25lIiBzdHJva2U9InJnYmEoNTgsMTQ3LDI1NSwwLjIpIiBzdHJva2Utd2lkdGg9IjgiIC8+DQogICAgICAgICAgICAgICAgICA8Y2lyY2xlIGN4PSI0MCIgY3k9IjQwIiByPSIzMCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjM0E2MUNEIiBzdHJva2Utd2lkdGg9IjgiIHN0cm9rZS1kYXNoYXJyYXk9IjE4OC40Ig0KICAgICAgICAgICAgICAgICAgICA6c3Ryb2tlLWRhc2hvZmZzZXQ9InJlZ2lzdGVyQ2lyY2xlT2Zmc2V0IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHRyYW5zZm9ybT0icm90YXRlKC05MCA0MCA0MCkiIC8+DQogICAgICAgICAgICAgICAgPC9zdmc+DQogICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJwcm9ncmVzc19pbmZvIj4NCiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJwcm9ncmVzc19sYWJlbCI+56uL5qGI546HPC9kaXY+DQogICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9InByb2dyZXNzX3ZhbHVlIiBzdHlsZT0iY29sb3I6ICMzQTYxQ0Q7Ij57eyByZWdpc3RlclJhdGUgfX08L3NwYW4+DQogICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgPC9kaXY+DQogICAgICAgICAgPGRpdiBjbGFzcz0icHJvZ3Jlc3NfY2FyZCI+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJwcm9ncmVzc19jb250ZW50Ij4NCiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0icHJvZ3Jlc3NfY2lyY2xlIGdyZWVuX3Byb2dyZXNzIj4NCiAgICAgICAgICAgICAgICA8c3ZnIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDgwIDgwIj4NCiAgICAgICAgICAgICAgICAgIDxjaXJjbGUgY3g9IjQwIiBjeT0iNDAiIHI9IjMwIiBmaWxsPSJub25lIiBzdHJva2U9InJnYmEoNTgsMTQ3LDI1NSwwLjIpIiBzdHJva2Utd2lkdGg9IjgiIC8+DQogICAgICAgICAgICAgICAgICA8Y2lyY2xlIGN4PSI0MCIgY3k9IjQwIiByPSIzMCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjNTdCQ0FBIiBzdHJva2Utd2lkdGg9IjgiIHN0cm9rZS1kYXNoYXJyYXk9IjE4OC40Ig0KICAgICAgICAgICAgICAgICAgICA6c3Ryb2tlLWRhc2hvZmZzZXQ9InJlcGx5Q2lyY2xlT2Zmc2V0IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHRyYW5zZm9ybT0icm90YXRlKC05MCA0MCA0MCkiIC8+DQogICAgICAgICAgICAgICAgPC9zdmc+DQogICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJwcm9ncmVzc19pbmZvIj4NCiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJwcm9ncmVzc19sYWJlbCI+562U5aSN546HPC9kaXY+DQogICAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9InByb2dyZXNzX3ZhbHVlIiBzdHlsZT0iY29sb3I6ICM1N0JDQUE7Ij57eyByZXBseVJhdGUgfX08L3NwYW4+DQogICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgPC9kaXY+DQogICAgICAgIDwvZGl2Pg0KICAgICAgPC9kaXY+DQogICAgPC9kaXY+DQogICAgPCEtLSDmj5DkuqTmg4XlhrUgLS0+DQogICAgPGRpdiBjbGFzcz0ic3VibWl0X3N0YXR1c19ib3giPg0KICAgICAgPGRpdiBjbGFzcz0iaGVhZGVyX2JveCI+DQogICAgICAgIDxkaXYgY2xhc3M9ImhlYWRlcl9sZWZ0Ij4NCiAgICAgICAgICA8c3BhbiBjbGFzcz0iaGVhZGVyX2xlZnRfbGluZSI+PC9zcGFuPg0KICAgICAgICAgIDxzcGFuIGNsYXNzPSJoZWFkZXJfbGVmdF90aXRsZSI+5o+Q5Lqk5oOF5Ya1PC9zcGFuPg0KICAgICAgICA8L2Rpdj4NCiAgICAgIDwvZGl2Pg0KICAgICAgPGRpdiBjbGFzcz0ic3VibWl0X3N0YXR1cyI+DQogICAgICAgIDxkaXYgY2xhc3M9InN1Ym1pdF9zdGF0aXN0aWNzX3JvdyI+DQogICAgICAgICAgPGRpdiBjbGFzcz0ic3VibWl0X2NhcmQiIHN0eWxlPSJiYWNrZ3JvdW5kOiAjRThGNEZGOyI+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJzdWJtaXRfdmFsdWUgY29tbWl0dGVlX3RleHQiPnt7IGNvbW1pdHRlZVByb3Bvc2FsIH19PC9kaXY+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJzdWJtaXRfbGFiZWwiPuWnlOWRmOaPkOahiDwvZGl2Pg0KICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgIDxkaXYgY2xhc3M9InN1Ym1pdF9jYXJkIiBzdHlsZT0iYmFja2dyb3VuZDogI0ZGRjhFMTsiPg0KICAgICAgICAgICAgPGRpdiBjbGFzcz0ic3VibWl0X3ZhbHVlIGJvdW5kYXJ5X3RleHQiPnt7IGJvdW5kYXJ5UHJvcG9zYWwgfX08L2Rpdj4NCiAgICAgICAgICAgIDxkaXYgY2xhc3M9InN1Ym1pdF9sYWJlbCI+55WM5Yir5o+Q5qGIPC9kaXY+DQogICAgICAgICAgPC9kaXY+DQogICAgICAgICAgPGRpdiBjbGFzcz0ic3VibWl0X2NhcmQiIHN0eWxlPSJiYWNrZ3JvdW5kOiAjRThGNUU4OyI+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJzdWJtaXRfdmFsdWUgb3JnYW5pemF0aW9uX3RleHQiPnt7IG9yZ2FuaXphdGlvblByb3Bvc2FsIH19PC9kaXY+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJzdWJtaXRfbGFiZWwiPue7hOe7h+aPkOahiDwvZGl2Pg0KICAgICAgICAgIDwvZGl2Pg0KICAgICAgICA8L2Rpdj4NCiAgICAgIDwvZGl2Pg0KICAgIDwvZGl2Pg0KICAgIDwhLS0g57G75Z6L5YiG5biDIC0tPg0KICAgIDxkaXYgY2xhc3M9InByb3Bvc2FsX3R5cGVfYW5hbHlzaXNfYm94Ij4NCiAgICAgIDxkaXYgY2xhc3M9ImhlYWRlcl9ib3giPg0KICAgICAgICA8ZGl2IGNsYXNzPSJoZWFkZXJfbGVmdCI+DQogICAgICAgICAgPHNwYW4gY2xhc3M9ImhlYWRlcl9sZWZ0X2xpbmUiPjwvc3Bhbj4NCiAgICAgICAgICA8c3BhbiBjbGFzcz0iaGVhZGVyX2xlZnRfdGl0bGUiPuexu+Wei+WIhuW4gzwvc3Bhbj4NCiAgICAgICAgPC9kaXY+DQogICAgICA8L2Rpdj4NCiAgICAgIDxkaXYgY2xhc3M9InByb3Bvc2FsX3R5cGVfYW5hbHlzaXMiPg0KICAgICAgICA8cGllRWNoYXJ0c0xlZ2VuZCBpZD0idHlwZUFuYWx5c2lzUGllIiA6ZGF0YUxpc3Q9InR5cGVBbmFseXNpc0xpc3QiIHRpdGxlPSLnsbvlnovliIbmnpAiIC8+DQogICAgICA8L2Rpdj4NCiAgICA8L2Rpdj4NCiAgICA8IS0tIOetlOWkjeexu+WeiyAtLT4NCiAgICA8ZGl2IGNsYXNzPSJyZXBseV90eXBlX2JveCI+DQogICAgICA8ZGl2IGNsYXNzPSJoZWFkZXJfYm94Ij4NCiAgICAgICAgPGRpdiBjbGFzcz0iaGVhZGVyX2xlZnQiPg0KICAgICAgICAgIDxzcGFuIGNsYXNzPSJoZWFkZXJfbGVmdF9saW5lIj48L3NwYW4+DQogICAgICAgICAgPHNwYW4gY2xhc3M9ImhlYWRlcl9sZWZ0X3RpdGxlIj7nrZTlpI3nsbvlnos8L3NwYW4+DQogICAgICAgIDwvZGl2Pg0KICAgICAgPC9kaXY+DQogICAgICA8ZGl2IGNsYXNzPSJyZXBseV90eXBlIj4NCiAgICAgICAgPFByb2dyZXNzQmFyQ2hhcnQgdGl0bGU9IumdouWkjSIgOmRlc2M9ImDljaDmgLvku7bmlbAke3JlcGx5VHlwZS5mYWNlfSVgIiA6cGVyY2VudD0icmVwbHlUeXBlLmZhY2UiDQogICAgICAgICAgOnZhbHVlPSJyZXBseVR5cGUuZmFjZU51bSIgY29sb3I9ImxpbmVhci1ncmFkaWVudCg5MGRlZywgcmdiYSg1OCwxNDcsMjU1LDAuMykgMCUsICMzQTkzRkYgMTAwJSkiIC8+DQogICAgICAgIDxQcm9ncmVzc0JhckNoYXJ0IHRpdGxlPSLlh73lpI0iIDpkZXNjPSJg5Y2g5o+Q5Lqk5pWwJHtyZXBseVR5cGUubGV0dGVyfSVgIiA6cGVyY2VudD0icmVwbHlUeXBlLmxldHRlciINCiAgICAgICAgICA6dmFsdWU9InJlcGx5VHlwZS5sZXR0ZXJOdW0iIGNvbG9yPSJsaW5lYXItZ3JhZGllbnQoOTBkZWcsIHJnYmEoMjU1LDExNSwxNDEsMC4zKSAwJSwgI0ZGNzM4RCAxMDAlKSIgLz4NCiAgICAgIDwvZGl2Pg0KICAgIDwvZGl2Pg0KICAgIDwhLS0g5Yqe55CG5Y2V5L2N57uf6K6h77yI5YmN5Y2B77yJIC0tPg0KICAgIDxkaXYgY2xhc3M9ImhhbmRsZV91bml0X3N0YXRpc3RpY3NfYm94Ij4NCiAgICAgIDxkaXYgY2xhc3M9ImhlYWRlcl9ib3giPg0KICAgICAgICA8ZGl2IGNsYXNzPSJoZWFkZXJfbGVmdCI+DQogICAgICAgICAgPHNwYW4gY2xhc3M9ImhlYWRlcl9sZWZ0X2xpbmUiPjwvc3Bhbj4NCiAgICAgICAgICA8c3BhbiBjbGFzcz0iaGVhZGVyX2xlZnRfdGl0bGUiPuWKnueQhuWNleS9jee7n+iuoe+8iOWJjeWNge+8iTwvc3Bhbj4NCiAgICAgICAgPC9kaXY+DQogICAgICA8L2Rpdj4NCiAgICAgIDxkaXYgY2xhc3M9ImhhbmRsZV91bml0X3N0YXRpc3RpY3MiPg0KICAgICAgICA8IS0tIOihqOWktCAtLT4NCiAgICAgICAgPGRpdiBjbGFzcz0icmFua2luZ19oZWFkZXIiPg0KICAgICAgICAgIDxkaXYgY2xhc3M9ImhlYWRlcl9yYW5rIj7mjpLlkI08L2Rpdj4NCiAgICAgICAgICA8ZGl2IGNsYXNzPSJoZWFkZXJfdW5pdCI+5Y2V5L2N5ZCN56ewPC9kaXY+DQogICAgICAgICAgPGRpdiBjbGFzcz0iaGVhZGVyX2NvdW50Ij7lip7nkIbmlbDph488L2Rpdj4NCiAgICAgICAgPC9kaXY+DQoNCiAgICAgICAgPCEtLSDmjpLlkI3liJfooaggLS0+DQogICAgICAgIDxkaXYgY2xhc3M9InJhbmtpbmdfbGlzdCI+DQogICAgICAgICAgPGRpdiB2LWZvcj0iKGl0ZW0sIGluZGV4KSBpbiB1bml0UmFua2luZ0xpc3QiIDprZXk9ImluZGV4IiBjbGFzcz0icmFua2luZ19pdGVtIg0KICAgICAgICAgICAgOmNsYXNzPSJ7ICd0b3BfdGhyZWUnOiBpbmRleCA8IDMgfSI+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJyYW5rX251bWJlciI+DQogICAgICAgICAgICAgIDwhLS0g5YmN5LiJ5ZCN5pi+56S65Zu+54mH77yM5YW25LuW5pi+56S65pWw5a2XIC0tPg0KICAgICAgICAgICAgICA8aW1nIHYtaWY9ImluZGV4IDwgMyIgOnNyYz0iZ2V0UmFua0ljb24oaW5kZXggKyAxKSIgOmFsdD0iYOesrCR7aW5kZXggKyAxfeWQjWAiIGNsYXNzPSJyYW5rX2ljb24iIC8+DQogICAgICAgICAgICAgIDxzcGFuIHYtZWxzZSBjbGFzcz0icmFua190ZXh0Ij57eyBpbmRleCArIDEgfX08L3NwYW4+DQogICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgIDxkaXYgY2xhc3M9InVuaXRfbmFtZSI+e3sgaXRlbS5uYW1lIH19PC9kaXY+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJoYW5kbGVfY291bnQiPnt7IGl0ZW0uY291bnQgfX08L2Rpdj4NCiAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgPC9kaXY+DQogICAgICA8L2Rpdj4NCiAgICA8L2Rpdj4NCiAgICA8IS0tIOWQhOS4k+WnlOS8muaPkOahiOaVsCAtLT4NCiAgICA8ZGl2IGNsYXNzPSJjb21taXR0ZWVfcHJvcG9zYWxfbnVtX2JveCI+DQogICAgICA8ZGl2IGNsYXNzPSJoZWFkZXJfYm94Ij4NCiAgICAgICAgPGRpdiBjbGFzcz0iaGVhZGVyX2xlZnQiPg0KICAgICAgICAgIDxzcGFuIGNsYXNzPSJoZWFkZXJfbGVmdF9saW5lIj48L3NwYW4+DQogICAgICAgICAgPHNwYW4gY2xhc3M9ImhlYWRlcl9sZWZ0X3RpdGxlIj7lkITkuJPlp5TkvJrmj5DmoYjmlbA8L3NwYW4+DQogICAgICAgIDwvZGl2Pg0KICAgICAgPC9kaXY+DQogICAgICA8ZGl2IGNsYXNzPSJjb21taXR0ZWVfcHJvcG9zYWxfbnVtIj4NCiAgICAgICAgPGhvcml6b250YWxCYXJFY2hhcnRzIGlkPSJjb21taXR0ZWVQcm9wb3NhbE51bSIgOmJhckxpc3Q9ImJhckxpc3QiIGNvbG9yU3RhcnQ9IiNGRkZGRkYiIGNvbG9yRW5kPSIjRUY4MTdDIg0KICAgICAgICAgIHN0eWxlPSJoZWlnaHQ6IDI2MHB4OyIgLz4NCiAgICAgIDwvZGl2Pg0KICAgIDwvZGl2Pg0KICAgIDwhLS0g6YeN54K55o+Q5qGIIC0tPg0KICAgIDxkaXYgY2xhc3M9ImtleV9wcm9wb3NhbF9ib3giPg0KICAgICAgPGRpdiBjbGFzcz0iaGVhZGVyX2JveCI+DQogICAgICAgIDxkaXYgY2xhc3M9ImhlYWRlcl9sZWZ0Ij4NCiAgICAgICAgICA8c3BhbiBjbGFzcz0iaGVhZGVyX2xlZnRfbGluZSI+PC9zcGFuPg0KICAgICAgICAgIDxzcGFuIGNsYXNzPSJoZWFkZXJfbGVmdF90aXRsZSI+6YeN54K55o+Q5qGIPC9zcGFuPg0KICAgICAgICA8L2Rpdj4NCiAgICAgICAgPGRpdiBjbGFzcz0iaGVhZGVyX3JpZ2h0IiBAY2xpY2s9Im9wZW5Nb3JlKCdub3RpY2UnKSI+DQogICAgICAgICAgPHNwYW4gY2xhc3M9ImhlYWRlcl9yaWdodF9tb3JlIj7mn6XnnIvlhajpg6g8L3NwYW4+DQogICAgICAgIDwvZGl2Pg0KICAgICAgPC9kaXY+DQogICAgICA8ZGl2IGNsYXNzPSJrZXlfcHJvcG9zYWxfbGlzdCI+DQogICAgICAgIDxkaXYgY2xhc3M9ImtleV9wcm9wb3NhbF9pdGVtIiB2LWZvcj0iKGl0ZW0sIGlkeCkgaW4ga2V5UHJvcG9zYWxMaXN0IiA6a2V5PSJpZHgiPg0KICAgICAgICAgIDxkaXYgY2xhc3M9ImtleV9wcm9wb3NhbF9pdGVtX3RpdGxlIj57eyBpdGVtLnRpdGxlIH19PC9kaXY+DQogICAgICAgICAgPGRpdiBjbGFzcz0ia2V5X3Byb3Bvc2FsX2l0ZW1fZGF0ZSI+e3sgaXRlbS5uYW1lIH19PC9kaXY+DQogICAgICAgIDwvZGl2Pg0KICAgICAgPC9kaXY+DQogICAgPC9kaXY+DQogICAgPCEtLSDng63or43liIbmnpAgLS0+DQogICAgPGRpdiBjbGFzcz0iaG90X3dvcmRfYW5hbHlzaXNfYm94Ij4NCiAgICAgIDxkaXYgY2xhc3M9ImhlYWRlcl9ib3giPg0KICAgICAgICA8ZGl2IGNsYXNzPSJoZWFkZXJfbGVmdCI+DQogICAgICAgICAgPHNwYW4gY2xhc3M9ImhlYWRlcl9sZWZ0X2xpbmUiPjwvc3Bhbj4NCiAgICAgICAgICA8c3BhbiBjbGFzcz0iaGVhZGVyX2xlZnRfdGl0bGUiPueDreivjeWIhuaekDwvc3Bhbj4NCiAgICAgICAgPC9kaXY+DQogICAgICA8L2Rpdj4NCiAgICAgIDxkaXYgY2xhc3M9ImhvdF93b3JkX2FuYWx5c2lzX2xpc3QiPg0KICAgICAgICA8d29yZENsb3VkRWNoYXJ0cyBpZD0id29yZGNsb3VkIiA6d29yZExpc3Q9IndvcmRDbG91ZERhdGEiDQogICAgICAgICAgOmNvbG9yTGlzdD0iWycjMTg5MEZGJywgJyNGRjZCMzUnLCAnIzUyQzQxQScsICcjNzIyRUQxJywgJyMxODkwRkYnLCAnI0ZGNjlCNCcsICcjNTJDNDFBJywgJyNGRkQ3MDAnLCAnI0ZGNkI2QicsICcjNEVDREM0JywgJyM0NUI3RDEnLCAnIzk2Q0VCNCcsICcjRkZFQUE3JywgJyNEREEwREQnLCAnIzk4RDhDOCddIg0KICAgICAgICAgIDpzaXplUmFuZ2U9IlsyLCAxMF0iIC8+DQogICAgICA8L2Rpdj4NCiAgICA8L2Rpdj4NCiAgPC9kaXY+DQo="}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CA<PERSON>;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACzF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtE,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAChG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChE,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC7F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1D,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;kBAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACpG,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/E,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC3B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;kBAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;kBAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACjG,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;cACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC5E,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3E,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACpF,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACpG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACzG,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cAC/F,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9E,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/zy/xm/h5/qdzx_h5/product-app/src/views/SmartBrainLargeScreen/component/ProposalWork.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"ProposalWork\">\r\n    <!-- 提案整体情况 -->\r\n    <div class=\"proposal_overall_situation_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">提案整体情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"proposal_overall_situation\">\r\n        <div class=\"statistics_row\">\r\n          <div class=\"statistics_card\" style=\"background: #E8F7FF;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_total.png\" alt=\"提案总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">提案总件数</div>\r\n            <div class=\"card_value proposal_total_text\">{{ proposalTotal }}</div>\r\n          </div>\r\n          <div class=\"statistics_card\" style=\"background: #F1F5FF;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_register_num.png\" alt=\"立案总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">立案总件数</div>\r\n            <div class=\"card_value register_text\">{{ registerTotal }}</div>\r\n          </div>\r\n          <div class=\"statistics_card\" style=\"background: #DAF6F2;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_reply_num.png\" alt=\"答复总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">答复总件数</div>\r\n            <div class=\"card_value reply_text\">{{ replyTotal }}</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"progress_row\">\r\n          <div class=\"progress_card\">\r\n            <div class=\"progress_content\">\r\n              <div class=\"progress_circle blue_progress\">\r\n                <svg width=\"50\" height=\"50\" viewBox=\"0 0 80 80\">\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"rgba(58,147,255,0.2)\" stroke-width=\"8\" />\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"#3A61CD\" stroke-width=\"8\" stroke-dasharray=\"188.4\"\r\n                    :stroke-dashoffset=\"registerCircleOffset\" stroke-linecap=\"round\" transform=\"rotate(-90 40 40)\" />\r\n                </svg>\r\n              </div>\r\n              <div class=\"progress_info\">\r\n                <div class=\"progress_label\">立案率</div>\r\n                <span class=\"progress_value\" style=\"color: #3A61CD;\">{{ registerRate }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"progress_card\">\r\n            <div class=\"progress_content\">\r\n              <div class=\"progress_circle green_progress\">\r\n                <svg width=\"50\" height=\"50\" viewBox=\"0 0 80 80\">\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"rgba(58,147,255,0.2)\" stroke-width=\"8\" />\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"#57BCAA\" stroke-width=\"8\" stroke-dasharray=\"188.4\"\r\n                    :stroke-dashoffset=\"replyCircleOffset\" stroke-linecap=\"round\" transform=\"rotate(-90 40 40)\" />\r\n                </svg>\r\n              </div>\r\n              <div class=\"progress_info\">\r\n                <div class=\"progress_label\">答复率</div>\r\n                <span class=\"progress_value\" style=\"color: #57BCAA;\">{{ replyRate }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 提交情况 -->\r\n    <div class=\"submit_status_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">提交情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"submit_status\">\r\n        <div class=\"submit_statistics_row\">\r\n          <div class=\"submit_card\" style=\"background: #E8F4FF;\">\r\n            <div class=\"submit_value committee_text\">{{ committeeProposal }}</div>\r\n            <div class=\"submit_label\">委员提案</div>\r\n          </div>\r\n          <div class=\"submit_card\" style=\"background: #FFF8E1;\">\r\n            <div class=\"submit_value boundary_text\">{{ boundaryProposal }}</div>\r\n            <div class=\"submit_label\">界别提案</div>\r\n          </div>\r\n          <div class=\"submit_card\" style=\"background: #E8F5E8;\">\r\n            <div class=\"submit_value organization_text\">{{ organizationProposal }}</div>\r\n            <div class=\"submit_label\">组织提案</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 类型分布 -->\r\n    <div class=\"proposal_type_analysis_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">类型分布</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"proposal_type_analysis\">\r\n        <pieEchartsLegend id=\"typeAnalysisPie\" :dataList=\"typeAnalysisList\" title=\"类型分析\" />\r\n      </div>\r\n    </div>\r\n    <!-- 答复类型 -->\r\n    <div class=\"reply_type_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">答复类型</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"reply_type\">\r\n        <ProgressBarChart title=\"面复\" :desc=\"`占总件数${replyType.face}%`\" :percent=\"replyType.face\"\r\n          :value=\"replyType.faceNum\" color=\"linear-gradient(90deg, rgba(58,147,255,0.3) 0%, #3A93FF 100%)\" />\r\n        <ProgressBarChart title=\"函复\" :desc=\"`占提交数${replyType.letter}%`\" :percent=\"replyType.letter\"\r\n          :value=\"replyType.letterNum\" color=\"linear-gradient(90deg, rgba(255,115,141,0.3) 0%, #FF738D 100%)\" />\r\n      </div>\r\n    </div>\r\n    <!-- 办理单位统计（前十） -->\r\n    <div class=\"handle_unit_statistics_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">办理单位统计（前十）</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"handle_unit_statistics\">\r\n        <!-- 表头 -->\r\n        <div class=\"ranking_header\">\r\n          <div class=\"header_rank\">排名</div>\r\n          <div class=\"header_unit\">单位名称</div>\r\n          <div class=\"header_count\">办理数量</div>\r\n        </div>\r\n\r\n        <!-- 排名列表 -->\r\n        <div class=\"ranking_list\">\r\n          <div v-for=\"(item, index) in unitRankingList\" :key=\"index\" class=\"ranking_item\"\r\n            :class=\"{ 'top_three': index < 3 }\">\r\n            <div class=\"rank_number\">\r\n              <!-- 前三名显示图片，其他显示数字 -->\r\n              <img v-if=\"index < 3\" :src=\"getRankIcon(index + 1)\" :alt=\"`第${index + 1}名`\" class=\"rank_icon\" />\r\n              <span v-else class=\"rank_text\">{{ index + 1 }}</span>\r\n            </div>\r\n            <div class=\"unit_name\">{{ item.name }}</div>\r\n            <div class=\"handle_count\">{{ item.count }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 各专委会提案数 -->\r\n    <div class=\"committee_proposal_num_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">各专委会提案数</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"committee_proposal_num\">\r\n        <horizontalBarEcharts id=\"committeeProposalNum\" :barList=\"barList\" colorStart=\"#FFFFFF\" colorEnd=\"#EF817C\"\r\n          style=\"height: 260px;\" />\r\n      </div>\r\n    </div>\r\n    <!-- 重点提案 -->\r\n    <div class=\"key_proposal_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">重点提案</span>\r\n        </div>\r\n        <div class=\"header_right\" @click=\"openMore('notice')\">\r\n          <span class=\"header_right_more\">查看全部</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"key_proposal_list\">\r\n        <div class=\"key_proposal_item\" v-for=\"(item, idx) in keyProposalList\" :key=\"idx\">\r\n          <div class=\"key_proposal_item_title\">{{ item.title }}</div>\r\n          <div class=\"key_proposal_item_date\">{{ item.name }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 热词分析 -->\r\n    <div class=\"hot_word_analysis_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">热词分析</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"hot_word_analysis_list\">\r\n        <wordCloudEcharts id=\"wordcloud\" :wordList=\"wordCloudData\"\r\n          :colorList=\"['#1890FF', '#FF6B35', '#52C41A', '#722ED1', '#1890FF', '#FF69B4', '#52C41A', '#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']\"\r\n          :sizeRange=\"[2, 10]\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { toRefs, reactive, computed } from 'vue'\r\nimport pieEchartsLegend from './echartsComponent/pieEchartsLegend.vue'\r\nimport ProgressBarChart from './echartsComponent/ProgressBarChart.vue'\r\nimport horizontalBarEcharts from './echartsComponent/horizontalBarEcharts.vue'\r\nimport wordCloudEcharts from './echartsComponent/wordCloudEcharts.vue'\r\nexport default {\r\n  components: { pieEchartsLegend, ProgressBarChart, horizontalBarEcharts, wordCloudEcharts },\r\n  name: 'ProposalWork',\r\n  setup () {\r\n    const data = reactive({\r\n      // 提案统计数据\r\n      proposalTotal: 1500, // 提案总件数\r\n      registerTotal: 600, // 立案总件数\r\n      replyTotal: 600, // 答复总件数\r\n\r\n      // 提交情况数据\r\n      committeeProposal: 456, // 委员提案\r\n      boundaryProposal: 354, // 界别提案\r\n      organizationProposal: 221, // 组织提案\r\n\r\n      // 计算属性相关数据\r\n      circleRadius: 30, // 圆形进度条半径\r\n      circleStrokeWidth: 8, // 圆形进度条线宽\r\n      typeAnalysisList: [\r\n        { name: '发改财政', value: 22.52, color: '#3DC3F0' },\r\n        { name: '民政市场', value: 18.33, color: '#4AC6A8' },\r\n        { name: '公安司法', value: 12.5, color: '#F9C846' },\r\n        { name: '区市政府', value: 11.34, color: '#6DD3A0' },\r\n        { name: '科技工信', value: 9.56, color: '#7B8DF9' },\r\n        { name: '教育文化', value: 8.09, color: '#F97C9C' },\r\n        { name: '派出机构', value: 4.21, color: '#F9A846' },\r\n        { name: '驻青单位', value: 3.71, color: '#F97C46' },\r\n        { name: '住建交通', value: 3.65, color: '#A97CF9' },\r\n        { name: '农村卫生', value: 3.21, color: '#4A9CF9' },\r\n        { name: '其他机构', value: 1.86, color: '#BFBFBF' },\r\n        { name: '党群其他', value: 1.02, color: '#F9C8C8' }\r\n      ],\r\n      replyType: { face: 60, faceNum: 360, letter: 40, letterNum: 240 },\r\n\r\n      // 办理单位统计数据（前十）\r\n      unitRankingList: [\r\n        { name: '市教育局', count: 89 },\r\n        { name: '市民政局', count: 75 },\r\n        { name: '市劳动局', count: 70 },\r\n        { name: '市农业农村委', count: 63 },\r\n        { name: '市交通运输管理局', count: 60 },\r\n        { name: '市经济信息委', count: 50 },\r\n        { name: '市发改委', count: 46 },\r\n        { name: '市教委', count: 46 },\r\n        { name: '市林业局', count: 44 },\r\n        { name: '市规划自然资源局', count: 41 }\r\n      ],\r\n      barList: [\r\n        { name: '教育界', value: 35 },\r\n        { name: '医药卫生界', value: 15 },\r\n        { name: '经济界', value: 14 },\r\n        { name: '工商联界', value: 21 },\r\n        { name: '民革界', value: 15 },\r\n        { name: '特邀界', value: 21 },\r\n        { name: '妇联界', value: 8 },\r\n        { name: '工会界', value: 8 },\r\n        { name: '社会福利与社会保障界', value: 14 }\r\n      ],\r\n      keyProposalList: [\r\n        { id: '1', title: '关于强化社区快递物流体系基础设施建...', name: '赵国胜' },\r\n        { id: '2', title: '关于预制菜不进学校食堂的提案', name: '李毅' },\r\n        { id: '3', title: '关于上下班高峰期道路拥堵的提案', name: '王洪妮' },\r\n        { id: '4', title: '关于商讨每周工作4天振消费的提案', name: '张万强' },\r\n        { id: '5', title: '关于加强垃圾分类末端处理的提案', name: '王洪妮' }\r\n      ],\r\n      wordCloudData: [\r\n        { name: '乡村振兴', value: 180 },\r\n        { name: '就业优先', value: 165 },\r\n        { name: '科技创新', value: 150 },\r\n        { name: '改革开放', value: 135 },\r\n        { name: '依法治国', value: 120 },\r\n        { name: '教育人才', value: 105 },\r\n        { name: '社会保障', value: 90 },\r\n        { name: '热词', value: 75 },\r\n        { name: '绿色发展', value: 60 },\r\n        { name: '数字中国', value: 45 },\r\n        { name: '共同富裕', value: 40 }\r\n      ]\r\n    })\r\n\r\n    // 计算立案率\r\n    const registerRate = computed(() => {\r\n      if (data.proposalTotal === 0) return '0%'\r\n      return Math.round((data.registerTotal / data.proposalTotal) * 100) + '%'\r\n    })\r\n\r\n    // 计算答复率\r\n    const replyRate = computed(() => {\r\n      if (data.registerTotal === 0) return '0%'\r\n      return Math.round((data.replyTotal / data.registerTotal) * 100) + '%'\r\n    })\r\n\r\n    // 计算立案率圆形进度条偏移量\r\n    const registerCircleOffset = computed(() => {\r\n      const circumference = 2 * Math.PI * data.circleRadius\r\n      const percentage = data.proposalTotal === 0 ? 0 : (data.registerTotal / data.proposalTotal)\r\n      return circumference - (circumference * percentage)\r\n    })\r\n\r\n    // 计算答复率圆形进度条偏移量\r\n    const replyCircleOffset = computed(() => {\r\n      const circumference = 2 * Math.PI * data.circleRadius\r\n      const percentage = data.registerTotal === 0 ? 0 : (data.replyTotal / data.registerTotal)\r\n      return circumference - (circumference * percentage)\r\n    })\r\n\r\n    // 获取排名图标\r\n    const getRankIcon = (rank) => {\r\n      // 这里先返回占位图片路径，您可以后续替换为实际的图片路径\r\n      const iconMap = {\r\n        1: require('../../../assets/img/largeScreen/icon_rank_one.png'), // 第一名图标\r\n        2: require('../../../assets/img/largeScreen/icon_rank_two.png'), // 第二名图标\r\n        3: require('../../../assets/img/largeScreen/icon_rank_three.png') // 第三名图标\r\n      }\r\n      return iconMap[rank] || ''\r\n    }\r\n\r\n    return {\r\n      ...toRefs(data),\r\n      registerRate,\r\n      replyRate,\r\n      registerCircleOffset,\r\n      replyCircleOffset,\r\n      getRankIcon\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.ProposalWork {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .header_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 15px 0 15px;\r\n\r\n    .header_left {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_left_line {\r\n        width: 3px;\r\n        height: 14px;\r\n        background: #007AFF;\r\n      }\r\n\r\n      .header_left_title {\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #222222;\r\n        margin-left: 8px;\r\n        font-family: Source Han Serif SC, Source Han Serif SC;\r\n      }\r\n    }\r\n\r\n    .header_right {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_right_text {\r\n        font-weight: 400;\r\n        font-size: 12px;\r\n        color: #999999;\r\n      }\r\n\r\n      .header_right_more {\r\n        font-size: 14px;\r\n        color: #0271E3;\r\n        border-radius: 14px;\r\n        border: 1px solid #0271E3;\r\n        padding: 3px 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .proposal_overall_situation_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .proposal_overall_situation {\r\n      padding: 12px;\r\n\r\n      .statistics_row {\r\n        display: flex;\r\n        gap: 10px;\r\n        margin-bottom: 10px;\r\n\r\n        .statistics_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 17px 8px;\r\n          text-align: center;\r\n\r\n          .card_icon {\r\n            width: 32px;\r\n            height: 32px;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .card_label {\r\n            font-size: 12px;\r\n            color: #999;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .card_value {\r\n            font-size: 20px;\r\n\r\n            &.proposal_total_text {\r\n              color: #308FFF;\r\n            }\r\n\r\n            &.register_text {\r\n              color: #3A61CD;\r\n            }\r\n\r\n            &.reply_text {\r\n              color: #57BCAA;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .progress_row {\r\n        display: flex;\r\n        gap: 10px;\r\n        justify-content: space-between;\r\n\r\n        .progress_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 10px 16px;\r\n          background: #E8F7FF;\r\n\r\n          .progress_content {\r\n            display: flex;\r\n            align-items: center;\r\n          }\r\n\r\n          .progress_circle {\r\n            margin-right: 15px;\r\n            margin-top: 5px;\r\n          }\r\n\r\n          .progress_info {\r\n\r\n            .progress_label {\r\n              font-size: 12px;\r\n              color: #999999;\r\n              margin-bottom: 5px;\r\n            }\r\n\r\n            .progress_value {\r\n              font-size: 20px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .submit_status_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .submit_status {\r\n      padding: 12px;\r\n\r\n      .submit_statistics_row {\r\n        display: flex;\r\n        gap: 10px;\r\n\r\n        .submit_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 24px 16px;\r\n          text-align: center;\r\n\r\n          .submit_value {\r\n            font-size: 20px;\r\n            margin-bottom: 10px;\r\n\r\n            &.committee_text {\r\n              color: #3B91FB;\r\n            }\r\n\r\n            &.boundary_text {\r\n              color: #EAB308;\r\n            }\r\n\r\n            &.organization_text {\r\n              color: #43DDBB;\r\n            }\r\n          }\r\n\r\n          .submit_label {\r\n            font-size: 14px;\r\n            color: #666666;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .proposal_type_analysis_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .proposal_type_analysis {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n    }\r\n  }\r\n\r\n  .reply_type_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .reply_type {\r\n      padding: 5px 18px 18px 18px;\r\n    }\r\n  }\r\n\r\n  .handle_unit_statistics_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .handle_unit_statistics {\r\n      padding: 10px 0;\r\n\r\n      .ranking_header {\r\n        display: flex;\r\n        align-items: center;\r\n        height: 36px;\r\n        padding: 0 18px 0 5px;\r\n        background: #F1F8FF;\r\n        border-radius: 4px 4px 0 0;\r\n\r\n        .header_rank {\r\n          width: 60px;\r\n          font-size: 14px;\r\n          color: #999999;\r\n          text-align: center;\r\n        }\r\n\r\n        .header_unit {\r\n          flex: 1;\r\n          font-size: 14px;\r\n          color: #999999;\r\n          text-align: left;\r\n          padding-left: 10px;\r\n        }\r\n\r\n        .header_count {\r\n          width: 80px;\r\n          font-size: 14px;\r\n          color: #999999;\r\n          text-align: right;\r\n        }\r\n      }\r\n\r\n      .ranking_list {\r\n        .ranking_item {\r\n          display: flex;\r\n          align-items: center;\r\n          height: 36px;\r\n          padding: 0 26px 0 5px;\r\n\r\n          // 斑马纹效果：奇数行白色，偶数行浅蓝色\r\n          &:nth-child(odd) {\r\n            background: #FFFFFF;\r\n          }\r\n\r\n          &:nth-child(even) {\r\n            background: #F1F8FF;\r\n          }\r\n\r\n          .rank_number {\r\n            width: 60px;\r\n            display: flex;\r\n            justify-content: center;\r\n            align-items: center;\r\n            height: 100%;\r\n\r\n            .rank_icon {\r\n              width: 24px;\r\n              height: 25px;\r\n              object-fit: contain;\r\n            }\r\n\r\n            .rank_text {\r\n              font-size: 14px;\r\n              color: #999999;\r\n            }\r\n          }\r\n\r\n          .unit_name {\r\n            flex: 1;\r\n            font-size: 14px;\r\n            color: #333333;\r\n            padding-left: 10px;\r\n            line-height: 1;\r\n          }\r\n\r\n          .handle_count {\r\n            width: 80px;\r\n            font-size: 14px;\r\n            color: #333333;\r\n            text-align: right;\r\n            line-height: 1;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .committee_proposal_num_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .committee_proposal_num {\r\n      padding: 12px;\r\n    }\r\n  }\r\n\r\n  .key_proposal_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .key_proposal_list {\r\n      padding: 8px 15px;\r\n      background: #fff;\r\n\r\n      .key_proposal_item {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        padding: 12px 0;\r\n        border-bottom: 1px solid #f0f0f0;\r\n\r\n        &:last-child {\r\n          border-bottom: none;\r\n        }\r\n\r\n        .key_proposal_item_title {\r\n          flex: 1;\r\n          font-size: 14px;\r\n          color: #666666;\r\n          white-space: nowrap;\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n        }\r\n\r\n        .key_proposal_item_date {\r\n          font-size: 14px;\r\n          color: #666;\r\n          flex-shrink: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .hot_word_analysis_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .hot_word_analysis_list {\r\n      padding: 5px 15px 15px 15px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}