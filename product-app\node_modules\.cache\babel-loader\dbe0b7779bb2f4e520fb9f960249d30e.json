{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue?vue&type=template&id=5c6a5fca&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue", "mtime": 1753951703798}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgb3BlbkJsb2NrIGFzIF9vcGVuQmxvY2ssIGNyZWF0ZUVsZW1lbnRCbG9jayBhcyBfY3JlYXRlRWxlbWVudEJsb2NrIH0gZnJvbSAidnVlIjsKY29uc3QgX2hvaXN0ZWRfMSA9IFsiaWQiXTsKZXhwb3J0IGZ1bmN0aW9uIHJlbmRlcihfY3R4LCBfY2FjaGUsICRwcm9wcywgJHNldHVwLCAkZGF0YSwgJG9wdGlvbnMpIHsKICByZXR1cm4gX29wZW5CbG9jaygpLCBfY3JlYXRlRWxlbWVudEJsb2NrKCJkaXYiLCB7CiAgICBjbGFzczogImFjdGl2aXR5LXR5cGUtY2hhcnQiLAogICAgaWQ6ICRzZXR1cC5jaGFydElkCiAgfSwgbnVsbCwgOCAvKiBQUk9QUyAqLywgX2hvaXN0ZWRfMSk7Cn0="}, {"version": 3, "names": ["_createElementBlock", "class", "id", "$setup", "chartId"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue"], "sourcesContent": ["<template>\n  <div class=\"activity-type-chart\" :id=\"chartId\"></div>\n</template>\n\n<script>\nimport { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'ActivityTypeChart',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    data: {\n      type: Array,\n      default: () => []\n    }\n  },\n  setup (props) {\n    const chartId = ref(props.id)\n    let chartInstance = null\n\n    const initChart = () => {\n      var charts = {\n        cityList: props.data.map(v => v.name),\n        cityData: props.data.map(v => v.value)\n      }\n      var top10CityList = charts.cityList\n      var top10CityData = charts.cityData\n      const lineY = []\n      const lineT = []\n      for (var i = 0; i < charts.cityList.length; i++) {\n        var x = i\n        if (x > 1) {\n          x = 2\n        }\n        var data = {\n          name: charts.cityList[i],\n          value: top10CityData[i],\n          barGap: '-100%',\n          itemStyle: {\n            color: {\n              type: 'linear',\n              x: 0,\n              y: 0,\n              x2: 1,\n              y2: 0,\n              colorStops: [\n                { offset: 0, color: '#FFFFFF' },\n                { offset: 1, color: '#559FFF' }\n              ]\n            }\n          }\n        }\n        var data1 = {\n          value: top10CityData[i],\n          label: {\n            show: true,\n            position: 'right',\n            color: '#999',\n            fontSize: 14,\n            distance: 10\n          },\n          itemStyle: {\n            color: {\n              type: 'linear',\n              x: 0,\n              y: 0,\n              x2: 1,\n              y2: 0,\n              colorStops: [\n                { offset: 0, color: '#FFFFFF' },\n                { offset: 1, color: '#EF817C' }\n              ]\n            }\n          }\n        }\n        lineY.push(data)\n        lineT.push(data1)\n        console.log('lineT===>', lineT)\n        console.log('lineT===>', lineT)\n      }\n      nextTick(() => {\n        const dom = document.getElementById(chartId.value)\n        if (!dom) {\n          console.error('Chart DOM element not found:', chartId.value)\n          return\n        }\n        if (!chartInstance) {\n          chartInstance = echarts.init(dom)\n        }\n        const option = {\n          tooltip: {\n            trigger: 'item',\n            formatter: (p) => {\n              if (p.seriesName === 'total') {\n                return ''\n              }\n              return `${p.name}<br/>${p.value}`\n            }\n          },\n          grid: {\n            left: '0%',\n            right: '20%',\n            top: '2%',\n            bottom: '2%',\n            containLabel: true\n          },\n          yAxis: {\n            type: 'category',\n            inverse: true,\n            axisTick: {\n              show: false\n            },\n            axisLine: {\n              show: false\n            },\n            axisLabel: {\n              show: false,\n              inside: false\n            },\n            data: top10CityList\n          },\n          xAxis: {\n            type: 'value',\n            axisTick: {\n              show: false\n            },\n            axisLine: {\n              show: false\n            },\n            splitLine: {\n              show: false\n            },\n            axisLabel: {\n              show: false\n            }\n          },\n          series: [\n            {\n              name: 'total',\n              type: 'bar',\n              barGap: '-100%',\n              barWidth: 10,\n              data: lineT,\n              legendHoverLink: false\n            },\n            {\n              name: 'bar',\n              type: 'bar',\n              barWidth: 10,\n              data: lineY,\n              label: {\n                normal: {\n                  show: true,\n                  fontSize: '12px',\n                  color: '#999',\n                  position: [0, '-20px'],\n                  formatter: '{b}'\n                }\n              }\n            }\n          ]\n        }\n        //   const option = {\n\n        //     xAxis: {\n        //       type: 'value',\n        //       axisTick: {\n        //         show: false\n        //       },\n        //       axisLine: {\n        //         show: false\n        //       },\n        //       splitLine: {\n        //         show: false\n        //       },\n        //       axisLabel: {\n        //         show: false\n        //       }\n        //     },\n        //     yAxis: [\n        //       {\n        //         type: 'category',\n        //         inverse: true,\n        //         axisTick: {\n        //           show: false\n        //         },\n        //         axisLine: {\n        //           show: false\n        //         },\n        //         axisLabel: {\n        //           show: false // 隐藏Y轴标签，名称将显示在柱状图上方\n        //         },\n        //         data: props.data.map(item => item.name)\n        //       }\n        //     ],\n        //     series: [\n        //       {\n        //         type: 'bar',\n        //         data: props.data.map((item, index) => ({\n        //           value: item.value,\n        // itemStyle: {\n        //   color: index % 2 === 0\n        //     ? {\n        //       type: 'linear',\n        //       x: 0,\n        //       y: 0,\n        //       x2: 1,\n        //       y2: 0,\n        //       colorStops: [\n        //         { offset: 0, color: '#FFFFFF' },\n        //         { offset: 1, color: '#EF817C' }\n        //       ]\n        //     }\n        //     : {\n        //       type: 'linear',\n        //       x: 0,\n        //       y: 0,\n        //       x2: 1,\n        //       y2: 0,\n        //       colorStops: [\n        //         { offset: 0, color: '#FFFFFF' },\n        //         { offset: 1, color: '#559FFF' }\n        //       ]\n        //     }\n        // }\n        //         })),\n        //         barWidth: 10,\n        //         label: [\n        //           // 在柱状图上方显示活动名称\n        //           {\n        //             show: true,\n        //             position: 'top',\n        //             color: '#666666',\n        //             fontSize: 12,\n        //             fontWeight: 'normal',\n        //             formatter: function (params) {\n        //               // 直接从Y轴数据中获取名称\n        //               return props.data[params.dataIndex].name || ''\n        //             },\n        //             offset: [0, -8]\n        //           },\n        //           // 在柱状图右侧显示数值\n        //           {\n        //             show: true,\n        //             position: 'right',\n        //             color: '#666666',\n        //             fontSize: 12,\n        //             fontWeight: 'normal',\n        //             formatter: '{c}',\n        //             offset: [8, 0]\n        //           }\n        //         ]\n        //       }\n        //     ]\n        //   }\n        chartInstance.setOption(option)\n      })\n    }\n\n    const resizeChart = () => {\n      if (chartInstance) {\n        chartInstance.resize()\n      }\n    }\n\n    onMounted(() => {\n      initChart()\n      window.addEventListener('resize', resizeChart)\n    })\n\n    onUnmounted(() => {\n      if (chartInstance) {\n        chartInstance.dispose()\n      }\n      window.removeEventListener('resize', resizeChart)\n    })\n\n    watch(() => props.data, () => {\n      if (chartInstance) {\n        initChart()\n      }\n    }, { deep: true })\n\n    return {\n      chartId\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.activity-type-chart {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"], "mappings": ";;;uBACEA,mBAAA,CAAqD;IAAhDC,KAAK,EAAC,qBAAqB;IAAEC,EAAE,EAAEC,MAAA,CAAAC", "ignoreList": []}]}