{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue", "mtime": 1753932951993}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgdG9SZWZzLCByZWFjdGl2ZSB9IGZyb20gJ3Z1ZSc7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnUHJvcG9zYWxXb3JrJywKICBzZXR1cCgpIHsKICAgIGNvbnN0IGRhdGEgPSByZWFjdGl2ZSh7fSk7CiAgICByZXR1cm4gewogICAgICAuLi50b1JlZnMoZGF0YSkKICAgIH07CiAgfQp9Ow=="}, {"version": 3, "names": ["toRefs", "reactive", "name", "setup", "data"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ProposalWork\">\r\n    <!-- 提案整体情况 -->\r\n    <div class=\"proposal_overall_situation_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">提案整体情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"proposal_overall_situation\">\r\n        <div class=\"statistics_row\">\r\n          <div class=\"statistics_card\" style=\"background: #E8F7FF;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_total.png\" alt=\"提案总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">提案总件数</div>\r\n            <div class=\"card_value proposal_total_text\">1500</div>\r\n          </div>\r\n          <div class=\"statistics_card\" style=\"background: #F1F5FF;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_register_num.png\" alt=\"立案总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">立案总件数</div>\r\n            <div class=\"card_value register_text\">600</div>\r\n          </div>\r\n          <div class=\"statistics_card\" style=\"background: #DAF6F2;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_reply_num.png\" alt=\"答复总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">答复总件数</div>\r\n            <div class=\"card_value reply_text\">600</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"progress_row\">\r\n          <div class=\"progress_card\">\r\n            <div class=\"progress_content\">\r\n              <div class=\"progress_circle blue_progress\">\r\n                <svg width=\"60\" height=\"60\" viewBox=\"0 0 80 80\">\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"rgba(58,147,255,0.2)\" stroke-width=\"8\" />\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"#3A61CD\" stroke-width=\"8\" stroke-dasharray=\"188.4\"\r\n                    stroke-dashoffset=\"90\" stroke-linecap=\"round\" transform=\"rotate(-90 40 40)\" />\r\n                </svg>\r\n              </div>\r\n              <div class=\"progress_info\">\r\n                <div class=\"progress_label\">立案率</div>\r\n                <span class=\"progress_value\" style=\"color: #3A61CD;\">69%</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"progress_card\">\r\n            <div class=\"progress_content\">\r\n              <div class=\"progress_circle green_progress\">\r\n                <svg width=\"60\" height=\"60\" viewBox=\"0 0 80 80\">\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"rgba(58,147,255,0.2)\" stroke-width=\"8\" />\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"#57BCAA\" stroke-width=\"8\" stroke-dasharray=\"188.4\"\r\n                    stroke-dashoffset=\"58.4\" stroke-linecap=\"round\" transform=\"rotate(-90 40 40)\" />\r\n                </svg>\r\n              </div>\r\n              <div class=\"progress_info\">\r\n                <div class=\"progress_label\">答复率</div>\r\n                <span class=\"progress_value\" style=\"color: #57BCAA;\">69%</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { toRefs, reactive } from 'vue'\r\nexport default {\r\n  name: 'ProposalWork',\r\n  setup () {\r\n    const data = reactive({\r\n\r\n    })\r\n    return { ...toRefs(data) }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.ProposalWork {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .header_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 15px 0 15px;\r\n\r\n    .header_left {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_left_line {\r\n        width: 3px;\r\n        height: 14px;\r\n        background: #007AFF;\r\n      }\r\n\r\n      .header_left_title {\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #222222;\r\n        margin-left: 8px;\r\n        font-family: Source Han Serif SC, Source Han Serif SC;\r\n      }\r\n    }\r\n\r\n    .header_right {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_right_text {\r\n        font-weight: 400;\r\n        font-size: 12px;\r\n        color: #999999;\r\n      }\r\n\r\n      .header_right_more {\r\n        font-size: 14px;\r\n        color: #0271E3;\r\n        border-radius: 14px;\r\n        border: 1px solid #0271E3;\r\n        padding: 3px 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .proposal_overall_situation_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .proposal_overall_situation {\r\n      padding: 12px;\r\n\r\n      .statistics_row {\r\n        display: flex;\r\n        gap: 10px;\r\n        margin-bottom: 10px;\r\n\r\n        .statistics_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 17px 8px;\r\n          text-align: center;\r\n\r\n          .card_icon {\r\n            width: 32px;\r\n            height: 32px;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .card_label {\r\n            font-size: 12px;\r\n            color: #999;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .card_value {\r\n            font-size: 20px;\r\n\r\n            &.proposal_total_text {\r\n              color: #308FFF;\r\n            }\r\n\r\n            &.register_text {\r\n              color: #3A61CD;\r\n            }\r\n\r\n            &.reply_text {\r\n              color: #57BCAA;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .progress_row {\r\n        display: flex;\r\n        gap: 10px;\r\n        justify-content: space-between;\r\n\r\n        .progress_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 10px 16px;\r\n          background: #E8F7FF;\r\n\r\n          .progress_content {\r\n            display: flex;\r\n            align-items: center;\r\n          }\r\n\r\n          .progress_circle {\r\n            margin-right: 15px;\r\n            margin-top: 5px;\r\n          }\r\n\r\n          .progress_info {\r\n\r\n            .progress_label {\r\n              font-size: 12px;\r\n              color: #999999;\r\n              margin-bottom: 5px;\r\n            }\r\n\r\n            .progress_value {\r\n              font-size: 20px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAiEA,SAASA,MAAM,EAAEC,QAAO,QAAS,KAAI;AACrC,eAAe;EACbC,IAAI,EAAE,cAAc;EACpBC,KAAIA,CAAA,EAAK;IACP,MAAMC,IAAG,GAAIH,QAAQ,CAAC,CAEtB,CAAC;IACD,OAAO;MAAE,GAAGD,MAAM,CAACI,IAAI;IAAE;EAC3B;AACF", "ignoreList": []}]}