{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue", "mtime": 1753938495962}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["toRefs", "reactive", "computed", "pieEchartsLegend", "ProgressBarChart", "components", "name", "setup", "data", "proposalTotal", "registerTotal", "replyTotal", "committeeProposal", "boundaryProposal", "organizationProposal", "circleRadius", "circleStrokeWidth", "typeAnalysisList", "value", "color", "replyType", "face", "faceNum", "letter", "letterNum", "registerRate", "Math", "round", "replyRate", "registerCircleOffset", "circumference", "PI", "percentage", "replyCircleOffset"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ProposalWork\">\r\n    <!-- 提案整体情况 -->\r\n    <div class=\"proposal_overall_situation_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">提案整体情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"proposal_overall_situation\">\r\n        <div class=\"statistics_row\">\r\n          <div class=\"statistics_card\" style=\"background: #E8F7FF;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_total.png\" alt=\"提案总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">提案总件数</div>\r\n            <div class=\"card_value proposal_total_text\">{{ proposalTotal }}</div>\r\n          </div>\r\n          <div class=\"statistics_card\" style=\"background: #F1F5FF;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_register_num.png\" alt=\"立案总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">立案总件数</div>\r\n            <div class=\"card_value register_text\">{{ registerTotal }}</div>\r\n          </div>\r\n          <div class=\"statistics_card\" style=\"background: #DAF6F2;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_reply_num.png\" alt=\"答复总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">答复总件数</div>\r\n            <div class=\"card_value reply_text\">{{ replyTotal }}</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"progress_row\">\r\n          <div class=\"progress_card\">\r\n            <div class=\"progress_content\">\r\n              <div class=\"progress_circle blue_progress\">\r\n                <svg width=\"50\" height=\"50\" viewBox=\"0 0 80 80\">\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"rgba(58,147,255,0.2)\" stroke-width=\"8\" />\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"#3A61CD\" stroke-width=\"8\" stroke-dasharray=\"188.4\"\r\n                    :stroke-dashoffset=\"registerCircleOffset\" stroke-linecap=\"round\" transform=\"rotate(-90 40 40)\" />\r\n                </svg>\r\n              </div>\r\n              <div class=\"progress_info\">\r\n                <div class=\"progress_label\">立案率</div>\r\n                <span class=\"progress_value\" style=\"color: #3A61CD;\">{{ registerRate }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"progress_card\">\r\n            <div class=\"progress_content\">\r\n              <div class=\"progress_circle green_progress\">\r\n                <svg width=\"50\" height=\"50\" viewBox=\"0 0 80 80\">\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"rgba(58,147,255,0.2)\" stroke-width=\"8\" />\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"#57BCAA\" stroke-width=\"8\" stroke-dasharray=\"188.4\"\r\n                    :stroke-dashoffset=\"replyCircleOffset\" stroke-linecap=\"round\" transform=\"rotate(-90 40 40)\" />\r\n                </svg>\r\n              </div>\r\n              <div class=\"progress_info\">\r\n                <div class=\"progress_label\">答复率</div>\r\n                <span class=\"progress_value\" style=\"color: #57BCAA;\">{{ replyRate }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 提交情况 -->\r\n    <div class=\"submit_status_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">提交情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"submit_status\">\r\n        <div class=\"submit_statistics_row\">\r\n          <div class=\"submit_card\" style=\"background: #E8F4FF;\">\r\n            <div class=\"submit_value committee_text\">{{ committeeProposal }}</div>\r\n            <div class=\"submit_label\">委员提案</div>\r\n          </div>\r\n          <div class=\"submit_card\" style=\"background: #FFF8E1;\">\r\n            <div class=\"submit_value boundary_text\">{{ boundaryProposal }}</div>\r\n            <div class=\"submit_label\">界别提案</div>\r\n          </div>\r\n          <div class=\"submit_card\" style=\"background: #E8F5E8;\">\r\n            <div class=\"submit_value organization_text\">{{ organizationProposal }}</div>\r\n            <div class=\"submit_label\">组织提案</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 类型分布 -->\r\n    <div class=\"proposal_type_analysis_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">类型分布</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"proposal_type_analysis\">\r\n        <pieEchartsLegend id=\"typeAnalysisPie\" :dataList=\"typeAnalysisList\" title=\"类型分析\" />\r\n      </div>\r\n    </div>\r\n    <!-- 答复类型 -->\r\n    <div class=\"reply_type_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">答复类型</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"reply_type\">\r\n        <ProgressBarChart title=\"面复\" :desc=\"`占总件数${replyType.face}%`\" :percent=\"replyType.face\"\r\n          :value=\"replyType.faceNum\" color=\"linear-gradient(90deg, rgba(58,147,255,0.3) 0%, #3A93FF 100%)\" />\r\n        <ProgressBarChart title=\"函复\" :desc=\"`占提交数${replyType.letter}%`\" :percent=\"replyType.letter\"\r\n          :value=\"replyType.letterNum\" color=\"linear-gradient(90deg, rgba(255,115,141,0.3) 0%, #FF738D 100%)\" />\r\n      </div>\r\n    </div>\r\n    <!-- 办理单位统计（前十） -->\r\n    <div class=\"reply_type_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">办理单位统计（前十）</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"reply_type\">\r\n        <ProgressBarChart title=\"面复\" :desc=\"`占总件数${replyType.face}%`\" :percent=\"replyType.face\"\r\n          :value=\"replyType.faceNum\" color=\"linear-gradient(90deg, rgba(58,147,255,0.3) 0%, #3A93FF 100%)\" />\r\n        <ProgressBarChart title=\"函复\" :desc=\"`占提交数${replyType.letter}%`\" :percent=\"replyType.letter\"\r\n          :value=\"replyType.letterNum\" color=\"linear-gradient(90deg, rgba(255,115,141,0.3) 0%, #FF738D 100%)\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { toRefs, reactive, computed } from 'vue'\r\nimport pieEchartsLegend from './echartsComponent/pieEchartsLegend.vue'\r\nimport ProgressBarChart from './echartsComponent/ProgressBarChart.vue'\r\nexport default {\r\n  components: { pieEchartsLegend, ProgressBarChart },\r\n  name: 'ProposalWork',\r\n  setup () {\r\n    const data = reactive({\r\n      // 提案统计数据\r\n      proposalTotal: 1500, // 提案总件数\r\n      registerTotal: 600, // 立案总件数\r\n      replyTotal: 600, // 答复总件数\r\n\r\n      // 提交情况数据\r\n      committeeProposal: 456, // 委员提案\r\n      boundaryProposal: 354, // 界别提案\r\n      organizationProposal: 221, // 组织提案\r\n\r\n      // 计算属性相关数据\r\n      circleRadius: 30, // 圆形进度条半径\r\n      circleStrokeWidth: 8, // 圆形进度条线宽\r\n      typeAnalysisList: [\r\n        { name: '发改财政', value: 22.52, color: '#3DC3F0' },\r\n        { name: '民政市场', value: 18.33, color: '#4AC6A8' },\r\n        { name: '公安司法', value: 12.5, color: '#F9C846' },\r\n        { name: '区市政府', value: 11.34, color: '#6DD3A0' },\r\n        { name: '科技工信', value: 9.56, color: '#7B8DF9' },\r\n        { name: '教育文化', value: 8.09, color: '#F97C9C' },\r\n        { name: '派出机构', value: 4.21, color: '#F9A846' },\r\n        { name: '驻青单位', value: 3.71, color: '#F97C46' },\r\n        { name: '住建交通', value: 3.65, color: '#A97CF9' },\r\n        { name: '农村卫生', value: 3.21, color: '#4A9CF9' },\r\n        { name: '其他机构', value: 1.86, color: '#BFBFBF' },\r\n        { name: '党群其他', value: 1.02, color: '#F9C8C8' }\r\n      ],\r\n      replyType: { face: 60, faceNum: 360, letter: 40, letterNum: 240 }\r\n    })\r\n\r\n    // 计算立案率\r\n    const registerRate = computed(() => {\r\n      if (data.proposalTotal === 0) return '0%'\r\n      return Math.round((data.registerTotal / data.proposalTotal) * 100) + '%'\r\n    })\r\n\r\n    // 计算答复率\r\n    const replyRate = computed(() => {\r\n      if (data.registerTotal === 0) return '0%'\r\n      return Math.round((data.replyTotal / data.registerTotal) * 100) + '%'\r\n    })\r\n\r\n    // 计算立案率圆形进度条偏移量\r\n    const registerCircleOffset = computed(() => {\r\n      const circumference = 2 * Math.PI * data.circleRadius\r\n      const percentage = data.proposalTotal === 0 ? 0 : (data.registerTotal / data.proposalTotal)\r\n      return circumference - (circumference * percentage)\r\n    })\r\n\r\n    // 计算答复率圆形进度条偏移量\r\n    const replyCircleOffset = computed(() => {\r\n      const circumference = 2 * Math.PI * data.circleRadius\r\n      const percentage = data.registerTotal === 0 ? 0 : (data.replyTotal / data.registerTotal)\r\n      return circumference - (circumference * percentage)\r\n    })\r\n\r\n    return {\r\n      ...toRefs(data),\r\n      registerRate,\r\n      replyRate,\r\n      registerCircleOffset,\r\n      replyCircleOffset\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.ProposalWork {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .header_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 15px 0 15px;\r\n\r\n    .header_left {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_left_line {\r\n        width: 3px;\r\n        height: 14px;\r\n        background: #007AFF;\r\n      }\r\n\r\n      .header_left_title {\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #222222;\r\n        margin-left: 8px;\r\n        font-family: Source Han Serif SC, Source Han Serif SC;\r\n      }\r\n    }\r\n\r\n    .header_right {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_right_text {\r\n        font-weight: 400;\r\n        font-size: 12px;\r\n        color: #999999;\r\n      }\r\n\r\n      .header_right_more {\r\n        font-size: 14px;\r\n        color: #0271E3;\r\n        border-radius: 14px;\r\n        border: 1px solid #0271E3;\r\n        padding: 3px 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .proposal_overall_situation_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .proposal_overall_situation {\r\n      padding: 12px;\r\n\r\n      .statistics_row {\r\n        display: flex;\r\n        gap: 10px;\r\n        margin-bottom: 10px;\r\n\r\n        .statistics_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 17px 8px;\r\n          text-align: center;\r\n\r\n          .card_icon {\r\n            width: 32px;\r\n            height: 32px;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .card_label {\r\n            font-size: 12px;\r\n            color: #999;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .card_value {\r\n            font-size: 20px;\r\n\r\n            &.proposal_total_text {\r\n              color: #308FFF;\r\n            }\r\n\r\n            &.register_text {\r\n              color: #3A61CD;\r\n            }\r\n\r\n            &.reply_text {\r\n              color: #57BCAA;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .progress_row {\r\n        display: flex;\r\n        gap: 10px;\r\n        justify-content: space-between;\r\n\r\n        .progress_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 10px 16px;\r\n          background: #E8F7FF;\r\n\r\n          .progress_content {\r\n            display: flex;\r\n            align-items: center;\r\n          }\r\n\r\n          .progress_circle {\r\n            margin-right: 15px;\r\n            margin-top: 5px;\r\n          }\r\n\r\n          .progress_info {\r\n\r\n            .progress_label {\r\n              font-size: 12px;\r\n              color: #999999;\r\n              margin-bottom: 5px;\r\n            }\r\n\r\n            .progress_value {\r\n              font-size: 20px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .submit_status_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .submit_status {\r\n      padding: 12px;\r\n\r\n      .submit_statistics_row {\r\n        display: flex;\r\n        gap: 10px;\r\n\r\n        .submit_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 24px 16px;\r\n          text-align: center;\r\n\r\n          .submit_value {\r\n            font-size: 20px;\r\n            margin-bottom: 10px;\r\n\r\n            &.committee_text {\r\n              color: #3B91FB;\r\n            }\r\n\r\n            &.boundary_text {\r\n              color: #EAB308;\r\n            }\r\n\r\n            &.organization_text {\r\n              color: #43DDBB;\r\n            }\r\n          }\r\n\r\n          .submit_label {\r\n            font-size: 14px;\r\n            color: #666666;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .proposal_type_analysis_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .proposal_type_analysis {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n    }\r\n  }\r\n\r\n  .reply_type_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .reply_type {\r\n      padding: 5px 18px 18px 18px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAoIA,SAASA,MAAM,EAAEC,QAAQ,EAAEC,QAAO,QAAS,KAAI;AAC/C,OAAOC,gBAAe,MAAO,yCAAwC;AACrE,OAAOC,gBAAe,MAAO,yCAAwC;AACrE,eAAe;EACbC,UAAU,EAAE;IAAEF,gBAAgB;IAAEC;EAAiB,CAAC;EAClDE,IAAI,EAAE,cAAc;EACpBC,KAAIA,CAAA,EAAK;IACP,MAAMC,IAAG,GAAIP,QAAQ,CAAC;MACpB;MACAQ,aAAa,EAAE,IAAI;MAAE;MACrBC,aAAa,EAAE,GAAG;MAAE;MACpBC,UAAU,EAAE,GAAG;MAAE;;MAEjB;MACAC,iBAAiB,EAAE,GAAG;MAAE;MACxBC,gBAAgB,EAAE,GAAG;MAAE;MACvBC,oBAAoB,EAAE,GAAG;MAAE;;MAE3B;MACAC,YAAY,EAAE,EAAE;MAAE;MAClBC,iBAAiB,EAAE,CAAC;MAAE;MACtBC,gBAAgB,EAAE,CAChB;QAAEX,IAAI,EAAE,MAAM;QAAEY,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAU,CAAC,EAChD;QAAEb,IAAI,EAAE,MAAM;QAAEY,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAU,CAAC,EAChD;QAAEb,IAAI,EAAE,MAAM;QAAEY,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAU,CAAC,EAC/C;QAAEb,IAAI,EAAE,MAAM;QAAEY,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAU,CAAC,EAChD;QAAEb,IAAI,EAAE,MAAM;QAAEY,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAU,CAAC,EAC/C;QAAEb,IAAI,EAAE,MAAM;QAAEY,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAU,CAAC,EAC/C;QAAEb,IAAI,EAAE,MAAM;QAAEY,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAU,CAAC,EAC/C;QAAEb,IAAI,EAAE,MAAM;QAAEY,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAU,CAAC,EAC/C;QAAEb,IAAI,EAAE,MAAM;QAAEY,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAU,CAAC,EAC/C;QAAEb,IAAI,EAAE,MAAM;QAAEY,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAU,CAAC,EAC/C;QAAEb,IAAI,EAAE,MAAM;QAAEY,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAU,CAAC,EAC/C;QAAEb,IAAI,EAAE,MAAM;QAAEY,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAU,EAC/C;MACDC,SAAS,EAAE;QAAEC,IAAI,EAAE,EAAE;QAAEC,OAAO,EAAE,GAAG;QAAEC,MAAM,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAI;IAClE,CAAC;;IAED;IACA,MAAMC,YAAW,GAAIvB,QAAQ,CAAC,MAAM;MAClC,IAAIM,IAAI,CAACC,aAAY,KAAM,CAAC,EAAE,OAAO,IAAG;MACxC,OAAOiB,IAAI,CAACC,KAAK,CAAEnB,IAAI,CAACE,aAAY,GAAIF,IAAI,CAACC,aAAa,GAAI,GAAG,IAAI,GAAE;IACzE,CAAC;;IAED;IACA,MAAMmB,SAAQ,GAAI1B,QAAQ,CAAC,MAAM;MAC/B,IAAIM,IAAI,CAACE,aAAY,KAAM,CAAC,EAAE,OAAO,IAAG;MACxC,OAAOgB,IAAI,CAACC,KAAK,CAAEnB,IAAI,CAACG,UAAS,GAAIH,IAAI,CAACE,aAAa,GAAI,GAAG,IAAI,GAAE;IACtE,CAAC;;IAED;IACA,MAAMmB,oBAAmB,GAAI3B,QAAQ,CAAC,MAAM;MAC1C,MAAM4B,aAAY,GAAI,IAAIJ,IAAI,CAACK,EAAC,GAAIvB,IAAI,CAACO,YAAW;MACpD,MAAMiB,UAAS,GAAIxB,IAAI,CAACC,aAAY,KAAM,IAAI,IAAKD,IAAI,CAACE,aAAY,GAAIF,IAAI,CAACC,aAAa;MAC1F,OAAOqB,aAAY,GAAKA,aAAY,GAAIE,UAAU;IACpD,CAAC;;IAED;IACA,MAAMC,iBAAgB,GAAI/B,QAAQ,CAAC,MAAM;MACvC,MAAM4B,aAAY,GAAI,IAAIJ,IAAI,CAACK,EAAC,GAAIvB,IAAI,CAACO,YAAW;MACpD,MAAMiB,UAAS,GAAIxB,IAAI,CAACE,aAAY,KAAM,IAAI,IAAKF,IAAI,CAACG,UAAS,GAAIH,IAAI,CAACE,aAAa;MACvF,OAAOoB,aAAY,GAAKA,aAAY,GAAIE,UAAU;IACpD,CAAC;IAED,OAAO;MACL,GAAGhC,MAAM,CAACQ,IAAI,CAAC;MACfiB,YAAY;MACZG,SAAS;MACTC,oBAAoB;MACpBI;IACF;EACF;AACF", "ignoreList": []}]}