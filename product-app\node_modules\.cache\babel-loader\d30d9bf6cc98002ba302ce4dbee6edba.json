{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue", "mtime": 1753936042886}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["toRefs", "reactive", "computed", "name", "setup", "data", "proposalTotal", "registerTotal", "replyTotal", "circleRadius", "circleStrokeWidth", "registerRate", "Math", "round", "replyRate", "registerCircleOffset", "circumference", "PI", "percentage", "replyCircleOffset"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ProposalWork\">\r\n    <!-- 提案整体情况 -->\r\n    <div class=\"proposal_overall_situation_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">提案整体情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"proposal_overall_situation\">\r\n        <div class=\"statistics_row\">\r\n          <div class=\"statistics_card\" style=\"background: #E8F7FF;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_total.png\" alt=\"提案总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">提案总件数</div>\r\n            <div class=\"card_value proposal_total_text\">{{ proposalTotal }}</div>\r\n          </div>\r\n          <div class=\"statistics_card\" style=\"background: #F1F5FF;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_register_num.png\" alt=\"立案总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">立案总件数</div>\r\n            <div class=\"card_value register_text\">{{ registerTotal }}</div>\r\n          </div>\r\n          <div class=\"statistics_card\" style=\"background: #DAF6F2;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_reply_num.png\" alt=\"答复总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">答复总件数</div>\r\n            <div class=\"card_value reply_text\">{{ replyTotal }}</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"progress_row\">\r\n          <div class=\"progress_card\">\r\n            <div class=\"progress_content\">\r\n              <div class=\"progress_circle blue_progress\">\r\n                <svg width=\"50\" height=\"50\" viewBox=\"0 0 80 80\">\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"rgba(58,147,255,0.2)\" stroke-width=\"8\" />\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"#3A61CD\" stroke-width=\"8\" stroke-dasharray=\"188.4\"\r\n                    :stroke-dashoffset=\"registerCircleOffset\" stroke-linecap=\"round\" transform=\"rotate(-90 40 40)\" />\r\n                </svg>\r\n              </div>\r\n              <div class=\"progress_info\">\r\n                <div class=\"progress_label\">立案率</div>\r\n                <span class=\"progress_value\" style=\"color: #3A61CD;\">{{ registerRate }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"progress_card\">\r\n            <div class=\"progress_content\">\r\n              <div class=\"progress_circle green_progress\">\r\n                <svg width=\"50\" height=\"50\" viewBox=\"0 0 80 80\">\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"rgba(58,147,255,0.2)\" stroke-width=\"8\" />\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"#57BCAA\" stroke-width=\"8\" stroke-dasharray=\"188.4\"\r\n                    :stroke-dashoffset=\"replyCircleOffset\" stroke-linecap=\"round\" transform=\"rotate(-90 40 40)\" />\r\n                </svg>\r\n              </div>\r\n              <div class=\"progress_info\">\r\n                <div class=\"progress_label\">答复率</div>\r\n                <span class=\"progress_value\" style=\"color: #57BCAA;\">{{ replyRate }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 提交情况 -->\r\n    <div class=\"submit_status_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">提交情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"submit_status\"></div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { toRefs, reactive, computed } from 'vue'\r\nexport default {\r\n  name: 'ProposalWork',\r\n  setup () {\r\n    const data = reactive({\r\n      // 提案统计数据\r\n      proposalTotal: 1500, // 提案总件数\r\n      registerTotal: 600, // 立案总件数\r\n      replyTotal: 600, // 答复总件数\r\n      // 计算属性相关数据\r\n      circleRadius: 30, // 圆形进度条半径\r\n      circleStrokeWidth: 8 // 圆形进度条线宽\r\n    })\r\n\r\n    // 计算立案率\r\n    const registerRate = computed(() => {\r\n      if (data.proposalTotal === 0) return '0%'\r\n      return Math.round((data.registerTotal / data.proposalTotal) * 100) + '%'\r\n    })\r\n\r\n    // 计算答复率\r\n    const replyRate = computed(() => {\r\n      if (data.registerTotal === 0) return '0%'\r\n      return Math.round((data.replyTotal / data.registerTotal) * 100) + '%'\r\n    })\r\n\r\n    // 计算立案率圆形进度条偏移量\r\n    const registerCircleOffset = computed(() => {\r\n      const circumference = 2 * Math.PI * data.circleRadius\r\n      const percentage = data.proposalTotal === 0 ? 0 : (data.registerTotal / data.proposalTotal)\r\n      return circumference - (circumference * percentage)\r\n    })\r\n\r\n    // 计算答复率圆形进度条偏移量\r\n    const replyCircleOffset = computed(() => {\r\n      const circumference = 2 * Math.PI * data.circleRadius\r\n      const percentage = data.registerTotal === 0 ? 0 : (data.replyTotal / data.registerTotal)\r\n      return circumference - (circumference * percentage)\r\n    })\r\n\r\n    return {\r\n      ...toRefs(data),\r\n      registerRate,\r\n      replyRate,\r\n      registerCircleOffset,\r\n      replyCircleOffset\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.ProposalWork {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .header_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 15px 0 15px;\r\n\r\n    .header_left {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_left_line {\r\n        width: 3px;\r\n        height: 14px;\r\n        background: #007AFF;\r\n      }\r\n\r\n      .header_left_title {\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #222222;\r\n        margin-left: 8px;\r\n        font-family: Source Han Serif SC, Source Han Serif SC;\r\n      }\r\n    }\r\n\r\n    .header_right {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_right_text {\r\n        font-weight: 400;\r\n        font-size: 12px;\r\n        color: #999999;\r\n      }\r\n\r\n      .header_right_more {\r\n        font-size: 14px;\r\n        color: #0271E3;\r\n        border-radius: 14px;\r\n        border: 1px solid #0271E3;\r\n        padding: 3px 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .proposal_overall_situation_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .proposal_overall_situation {\r\n      padding: 12px;\r\n\r\n      .statistics_row {\r\n        display: flex;\r\n        gap: 10px;\r\n        margin-bottom: 10px;\r\n\r\n        .statistics_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 17px 8px;\r\n          text-align: center;\r\n\r\n          .card_icon {\r\n            width: 32px;\r\n            height: 32px;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .card_label {\r\n            font-size: 12px;\r\n            color: #999;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .card_value {\r\n            font-size: 20px;\r\n\r\n            &.proposal_total_text {\r\n              color: #308FFF;\r\n            }\r\n\r\n            &.register_text {\r\n              color: #3A61CD;\r\n            }\r\n\r\n            &.reply_text {\r\n              color: #57BCAA;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .progress_row {\r\n        display: flex;\r\n        gap: 10px;\r\n        justify-content: space-between;\r\n\r\n        .progress_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 10px 16px;\r\n          background: #E8F7FF;\r\n\r\n          .progress_content {\r\n            display: flex;\r\n            align-items: center;\r\n          }\r\n\r\n          .progress_circle {\r\n            margin-right: 15px;\r\n            margin-top: 5px;\r\n          }\r\n\r\n          .progress_info {\r\n\r\n            .progress_label {\r\n              font-size: 12px;\r\n              color: #999999;\r\n              margin-bottom: 5px;\r\n            }\r\n\r\n            .progress_value {\r\n              font-size: 20px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .submit_status_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .submit_status {\r\n      padding: 12px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AA2EA,SAASA,MAAM,EAAEC,QAAQ,EAAEC,QAAO,QAAS,KAAI;AAC/C,eAAe;EACbC,IAAI,EAAE,cAAc;EACpBC,KAAIA,CAAA,EAAK;IACP,MAAMC,IAAG,GAAIJ,QAAQ,CAAC;MACpB;MACAK,aAAa,EAAE,IAAI;MAAE;MACrBC,aAAa,EAAE,GAAG;MAAE;MACpBC,UAAU,EAAE,GAAG;MAAE;MACjB;MACAC,YAAY,EAAE,EAAE;MAAE;MAClBC,iBAAiB,EAAE,EAAE;IACvB,CAAC;;IAED;IACA,MAAMC,YAAW,GAAIT,QAAQ,CAAC,MAAM;MAClC,IAAIG,IAAI,CAACC,aAAY,KAAM,CAAC,EAAE,OAAO,IAAG;MACxC,OAAOM,IAAI,CAACC,KAAK,CAAER,IAAI,CAACE,aAAY,GAAIF,IAAI,CAACC,aAAa,GAAI,GAAG,IAAI,GAAE;IACzE,CAAC;;IAED;IACA,MAAMQ,SAAQ,GAAIZ,QAAQ,CAAC,MAAM;MAC/B,IAAIG,IAAI,CAACE,aAAY,KAAM,CAAC,EAAE,OAAO,IAAG;MACxC,OAAOK,IAAI,CAACC,KAAK,CAAER,IAAI,CAACG,UAAS,GAAIH,IAAI,CAACE,aAAa,GAAI,GAAG,IAAI,GAAE;IACtE,CAAC;;IAED;IACA,MAAMQ,oBAAmB,GAAIb,QAAQ,CAAC,MAAM;MAC1C,MAAMc,aAAY,GAAI,IAAIJ,IAAI,CAACK,EAAC,GAAIZ,IAAI,CAACI,YAAW;MACpD,MAAMS,UAAS,GAAIb,IAAI,CAACC,aAAY,KAAM,IAAI,IAAKD,IAAI,CAACE,aAAY,GAAIF,IAAI,CAACC,aAAa;MAC1F,OAAOU,aAAY,GAAKA,aAAY,GAAIE,UAAU;IACpD,CAAC;;IAED;IACA,MAAMC,iBAAgB,GAAIjB,QAAQ,CAAC,MAAM;MACvC,MAAMc,aAAY,GAAI,IAAIJ,IAAI,CAACK,EAAC,GAAIZ,IAAI,CAACI,YAAW;MACpD,MAAMS,UAAS,GAAIb,IAAI,CAACE,aAAY,KAAM,IAAI,IAAKF,IAAI,CAACG,UAAS,GAAIH,IAAI,CAACE,aAAa;MACvF,OAAOS,aAAY,GAAKA,aAAY,GAAIE,UAAU;IACpD,CAAC;IAED,OAAO;MACL,GAAGlB,MAAM,CAACK,IAAI,CAAC;MACfM,YAAY;MACZG,SAAS;MACTC,oBAAoB;MACpBI;IACF;EACF;AACF", "ignoreList": []}]}