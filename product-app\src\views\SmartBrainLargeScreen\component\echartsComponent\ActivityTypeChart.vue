<template>
  <div class="activity-type-chart" ref="chartContainer"></div>
</template>

<script>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'ActivityTypeChart',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    height: {
      type: String,
      default: '400px'
    }
  },
  setup (props) {
    const chartContainer = ref(null)
    let chartInstance = null

    const initChart = () => {
      if (!chartContainer.value) return

      chartInstance = echarts.init(chartContainer.value)

      const option = {
        grid: {
          left: '5%',
          right: '15%',
          top: '8%',
          bottom: '2%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          show: false,
          max: function (value) {
            return Math.ceil(value.max * 1.1)
          }
        },
        yAxis: {
          type: 'category',
          data: props.data.map(item => item.name),
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false // 隐藏Y轴标签，因为我们要在柱状图上方显示
          },
          inverse: true // 反转Y轴，让第一项在顶部
        },
        series: [
          {
            type: 'bar',
            data: props.data.map((item, index) => ({
              value: item.value,
              itemStyle: {
                color: item.color || (index % 2 === 0 ? '#FF9999' : '#66B3FF')
              }
            })),
            barHeight: 14,
            label: [
              // 在柱状图右侧显示数值
              {
                show: true,
                position: 'right',
                color: '#333333',
                fontSize: 11,
                fontWeight: 500,
                formatter: '{c}',
                offset: [8, 0]
              },
              // 在柱状图上方显示名称
              {
                show: true,
                position: 'top',
                color: '#666666',
                fontSize: 11,
                fontWeight: 400,
                formatter: function (params) {
                  const name = props.data[params.dataIndex]?.name || ''
                  // 如果文字太长，进行换行处理
                  if (name.length > 10) {
                    return name.substring(0, 10) + '\n' + name.substring(10)
                  }
                  return name
                },
                offset: [0, -5],
                lineHeight: 14
              }
            ],
            itemStyle: {
              borderRadius: [0, 4, 4, 0]
            }
          }
        ],
        animation: true,
        animationDuration: 1000,
        animationEasing: 'cubicOut'
      }

      chartInstance.setOption(option)
    }

    const resizeChart = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    onMounted(() => {
      initChart()
      window.addEventListener('resize', resizeChart)
    })

    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.dispose()
      }
      window.removeEventListener('resize', resizeChart)
    })

    watch(() => props.data, () => {
      if (chartInstance) {
        initChart()
      }
    }, { deep: true })

    return {
      chartContainer
    }
  }
}
</script>

<style lang="less" scoped>
.activity-type-chart {
  width: 100%;
  height: v-bind(height);
}
</style>
