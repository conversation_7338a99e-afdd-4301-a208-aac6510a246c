<template>
  <div class="activity-type-chart" :id="chartId"></div>
</template>

<script>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'ActivityTypeChart',
  props: {
    id: {
      type: String,
      required: true
    },
    data: {
      type: Array,
      default: () => []
    }
  },
  setup (props) {
    const chartId = ref(props.id)
    let chartInstance = null

    const initChart = () => {
      nextTick(() => {
        const dom = document.getElementById(chartId.value)
        if (!dom) {
          console.error('Chart DOM element not found:', chartId.value)
          return
        }
        if (!chartInstance) {
          chartInstance = echarts.init(dom)
        }
        const option = {
          tooltip: {
            trigger: 'item'
          },
          grid: {
            left: '0%',
            right: '2%',
            top: '2%',
            bottom: '2%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisLabel: {
              show: false
            }
          },
          yAxis: [
            {
              type: 'category',
              inverse: true,
              axisTick: {
                show: false
              },
              axisLine: {
                show: false
              },
              axisLabel: {
                show: false,
                inside: false,
                color: '#666666',
                fontSize: 11,
                interval: 0,
                lineHeight: 14,
                formatter: function (value) {
                  if (value.length > 10) {
                    return value.substring(0, 10) + '\n' + value.substring(10)
                  }
                  return value
                }
              },
              data: props.data.map(item => item.name)
            }
          ],
          series: [
            {
              type: 'bar',
              data: props.data.map((item, index) => ({
                value: item.value,
                itemStyle: {
                  color: index % 2 === 0
                    ? {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 1,
                      y2: 0,
                      colorStops: [
                        { offset: 0, color: '#FFFFFF' },
                        { offset: 1, color: '#EF817C' }
                      ]
                    }
                    : {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 1,
                      y2: 0,
                      colorStops: [
                        { offset: 0, color: '#FFFFFF' },
                        { offset: 1, color: '#559FFF' }
                      ]
                    }
                }
              })),
              barWidth: 10,
              label: {
                show: true,
                position: 'right',
                color: '#999',
                fontSize: 14
              }
            }
          ]
        }
        chartInstance.setOption(option)
      })
    }

    const resizeChart = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    onMounted(() => {
      initChart()
      window.addEventListener('resize', resizeChart)
    })

    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.dispose()
      }
      window.removeEventListener('resize', resizeChart)
    })

    watch(() => props.data, () => {
      if (chartInstance) {
        initChart()
      }
    }, { deep: true })

    return {
      chartId
    }
  }
}
</script>

<style lang="less" scoped>
.activity-type-chart {
  width: 100%;
  height: 100%;
}
</style>
