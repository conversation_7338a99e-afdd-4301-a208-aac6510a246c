<template>
  <div class="activity-type-chart" :id="chartId"></div>
</template>

<script>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'ActivityTypeChart',
  props: {
    id: {
      type: String,
      required: true
    },
    data: {
      type: Array,
      default: () => []
    }
  },
  setup (props) {
    const chartId = ref(props.id)
    let chartInstance = null

    const initChart = () => {
      nextTick(() => {
        const dom = document.getElementById(chartId.value)
        if (!dom) {
          console.error('Chart DOM element not found:', chartId.value)
          return
        }
        if (!chartInstance) {
          chartInstance = echarts.init(dom)
        }
        const option = {
          tooltip: {
            trigger: 'item'
          },
          grid: {
            left: '0%',
            right: '2%',
            top: '2%',
            bottom: '2%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisLabel: {
              show: false
            }
          },
          yAxis: [
            {
              type: 'category',
              inverse: true,
              axisTick: {
                show: false
              },
              axisLine: {
                show: false
              },
              axisLabel: {
                show: false // 隐藏Y轴标签，在柱状图上方显示
              },
              data: props.data.map(item => item.name)
            }
          ],
          series: [
            {
              type: 'bar',
              data: props.data.map((item, index) => ({
                value: item.value,
                itemStyle: {
                  color: index % 2 === 0
                    ? {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 1,
                      y2: 0,
                      colorStops: [
                        { offset: 0, color: '#FFFFFF' },
                        { offset: 1, color: '#EF817C' }
                      ]
                    }
                    : {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 1,
                      y2: 0,
                      colorStops: [
                        { offset: 0, color: '#FFFFFF' },
                        { offset: 1, color: '#559FFF' }
                      ]
                    }
                }
              })),
              barWidth: 10,
              label: [
                // 在柱状图上方显示活动名称
                {
                  show: true,
                  position: 'top',
                  color: '#666666',
                  fontSize: 11,
                  fontWeight: 400,
                  formatter: function (params) {
                    const item = props.data[params.dataIndex]
                    const name = item && item.name ? item.name : ''
                    // 如果文字太长，进行换行处理
                    if (name.length > 10) {
                      return name.substring(0, 10) + '\n' + name.substring(10)
                    }
                    return name
                  },
                  offset: [0, -8],
                  lineHeight: 14
                },
                // 在柱状图右侧显示数值
                {
                  show: true,
                  position: 'right',
                  color: '#333333',
                  fontSize: 11,
                  fontWeight: 500,
                  formatter: '{c}',
                  offset: [8, 0]
                }
              ]
            }
          ]
        }
        chartInstance.setOption(option)
      })
    }

    const resizeChart = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
    }

    onMounted(() => {
      initChart()
      window.addEventListener('resize', resizeChart)
    })

    onUnmounted(() => {
      if (chartInstance) {
        chartInstance.dispose()
      }
      window.removeEventListener('resize', resizeChart)
    })

    watch(() => props.data, () => {
      if (chartInstance) {
        initChart()
      }
    }, { deep: true })

    return {
      chartId
    }
  }
}
</script>

<style lang="less" scoped>
.activity-type-chart {
  width: 100%;
  height: 100%;
}
</style>
