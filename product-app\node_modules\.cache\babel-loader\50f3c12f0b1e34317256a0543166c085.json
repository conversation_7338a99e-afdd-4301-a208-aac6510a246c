{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\PublicOpinion.vue?vue&type=template&id=e9c37430&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\PublicOpinion.vue", "mtime": 1753943363546}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_PublicOpinionOverallSituationChart", "id", "_ctx", "totalCount", "adoptedCount", "_hoisted_4", "_hoisted_5", "_component_ProgressBarChart", "title", "desc", "percent", "committeeMember", "submitPercent", "value", "submitNum", "color", "adoptSituationPercent", "adoptSituationNum", "unit", "_hoisted_6", "_component_<PERSON><PERSON><PERSON>", "adoptSituationDistribution", "radius", "_hoisted_7", "_hoisted_8", "_Fragment", "_renderList", "instructions", "item", "index", "key", "style", "_normalizeStyle", "bg", "_hoisted_9", "_toDisplayString", "label", "_hoisted_10", "_hoisted_11", "_component_DoubleBar<PERSON>hart", "data", "partyData", "legend", "districtData", "officeData", "_hoisted_12", "_hoisted_13", "onClick", "_cache", "$event", "openMore", "_hoisted_14", "_hoisted_15", "submitAdoptSituationData", "idx", "_normalizeClass", "name", "_hoisted_16", "src", "avatar", "_hoisted_18", "_hoisted_19", "submit", "_hoisted_20", "adopt", "_hoisted_21", "_hoisted_22", "_component_bar<PERSON>hart", "categoryData", "_hoisted_23", "_hoisted_24", "_component_wordCloudEcharts", "wordList", "wordCloudData", "colorList", "sizeRange"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\PublicOpinion.vue"], "sourcesContent": ["<template>\r\n  <div class=\"PublicOpinion\">\r\n    <!-- 社情民意整体情况 -->\r\n    <div class=\"overall_situation_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">社情民意整体情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"overall_situation_list\">\r\n        <PublicOpinionOverallSituationChart id=\"publicOpinionOverall\" :total-count=\"totalCount\"\r\n          :adopted-count=\"adoptedCount\" />\r\n      </div>\r\n    </div>\r\n    <!-- 采用情况 -->\r\n    <div class=\"adopt_situation_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">采用情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"adopt_situation_list\">\r\n        <ProgressBarChart title=\"委员提交\" desc=\"占总件数60%\" :percent=\"committeeMember.submitPercent\"\r\n          :value=\"committeeMember.submitNum\" color=\"linear-gradient(90deg, rgba(58,147,255,0.3) 0%, #3A93FF 100%)\" />\r\n        <ProgressBarChart title=\"采用情况\" desc=\"占提交数42%\" :percent=\"committeeMember.adoptSituationPercent\"\r\n          :value=\"committeeMember.adoptSituationNum\"\r\n          color=\"linear-gradient(90deg, rgba(255,115,141,0.3) 0%, #FF738D 100%)\" />\r\n        <ProgressBarChart title=\"单位提交\" desc=\"占总件数60%\" :percent=\"unit.submitPercent\" :value=\"unit.submitNum\"\r\n          color=\"linear-gradient(90deg, rgba(58,147,255,0.3) 0%, #3A93FF 100%)\" />\r\n        <ProgressBarChart title=\"采用情况\" desc=\"占提交数42%\" :percent=\"unit.adoptSituationPercent\"\r\n          :value=\"unit.adoptSituationNum\" color=\"linear-gradient(90deg, rgba(255,115,141,0.3) 0%, #FF738D 100%)\" />\r\n      </div>\r\n      <div class=\"adopt_situation_distribution_text\">采用情况分布</div>\r\n      <div class=\"adopt_situation_distribution_charts\">\r\n        <PieChart id=\"adoptSituationDistribution\" :chart-data=\"adoptSituationDistribution\" :radius=\"['35%', '60%']\" />\r\n      </div>\r\n    </div>\r\n    <!-- 批示情况 -->\r\n    <div class=\"instructions_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">批示情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"instructions_list\">\r\n        <div class=\"instructions_item\" v-for=\"(item, index) in instructions\" :key=\"index\"\r\n          :style=\"`background: ${item.bg}`\">\r\n          <div class=\"instructions_item_value\" :style=\"`color: ${item.color}`\">{{ item.value }}</div>\r\n          <div class=\"instructions_item_label\">{{ item.label }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 各单位上报与采用情况 -->\r\n    <div class=\"report_adopt_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">各单位上报与采用情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"report_adopt_list\">\r\n        <DoubleBarChart id=\"party_double_line\" :data=\"partyData\" :legend=\"['报送篇数', '采用篇数']\" :color=\"[\r\n          ['#56A0FF', 'rgba(86,160,255,0.1)'],\r\n          ['#FF738D', 'rgba(255,115,141,0.1)']\r\n        ]\" title=\"八大党派及民主工商联\" style=\"height: 260px;\" />\r\n        <DoubleBarChart id=\"district_double_line\" :data=\"districtData\" :legend=\"['报送篇数', '采用篇数']\" :color=\"[\r\n          ['#56A0FF', 'rgba(86,160,255,0.1)'],\r\n          ['#FF738D', 'rgba(255,115,141,0.1)']\r\n        ]\" title=\"各区市\" style=\"height: 260px;\" />\r\n        <DoubleBarChart id=\"office_double_line\" :data=\"officeData\" :legend=\"['报送篇数', '采用篇数']\" :color=\"[\r\n          ['#56A0FF', 'rgba(86,160,255,0.1)'],\r\n          ['#FF738D', 'rgba(255,115,141,0.1)']\r\n        ]\" title=\"各单位办公室\" style=\"height: 260px;\" />\r\n      </div>\r\n    </div>\r\n    <!-- 个人报送与采用情况 -->\r\n    <div class=\"submit_adopt_situation_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">个人报送与采用情况</span>\r\n        </div>\r\n        <div class=\"header_right\" @click=\"openMore('notice')\">\r\n          <span class=\"header_right_more\">查看全部</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"submit_adopt_situation_list\">\r\n        <div class=\"submit_adopt_table\">\r\n          <div class=\"submit_adopt_table_header\">\r\n            <span class=\"party-header-column\">姓名</span>\r\n            <span class=\"count-header-column\">报送件数</span>\r\n            <span class=\"count-header-column\">采用件数</span>\r\n          </div>\r\n          <div class=\"submit_adopt_table_row\" v-for=\"(item, idx) in submitAdoptSituationData\" :key=\"item.name\"\r\n            :class=\"{ 'row-alt': idx % 2 === 1 }\">\r\n            <span class=\"party-column name-cell\">\r\n              <img :src=\"item.avatar\" class=\"avatar\" />\r\n              <span class=\"name\">{{ item.name }}</span>\r\n            </span>\r\n            <span class=\"count-column\">{{ item.submit }}</span>\r\n            <span class=\"count-column\">{{ item.adopt }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 类别分析 -->\r\n    <div class=\"category_analysis_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">类别分析</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"category_analysis_list\">\r\n        <barChart id=\"categoryAnalysis\" :data=\"categoryData\" :color=\"['#559FFF', 'rgba(85,159,255,0.3)']\"\r\n          style=\"height:200px\" />\r\n      </div>\r\n    </div>\r\n    <!-- 热词分析 -->\r\n    <div class=\"hot_words_analysis_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">热词分析</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"hot_words_analysis_list\">\r\n        <wordCloudEcharts id=\"wordcloud\" :wordList=\"wordCloudData\"\r\n          :colorList=\"['#1890FF', '#FF6B35', '#52C41A', '#722ED1', '#1890FF', '#FF69B4', '#52C41A', '#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']\"\r\n          :sizeRange=\"[2, 10]\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { toRefs, reactive } from 'vue'\r\nimport PublicOpinionOverallSituationChart from './echartsComponent/PublicOpinionOverallSituationChart.vue'\r\nimport ProgressBarChart from './echartsComponent/ProgressBarChart.vue'\r\nimport PieChart from './echartsComponent/PieChart.vue'\r\nimport DoubleBarChart from './echartsComponent/DoubleBarChart.vue'\r\nimport barChart from './echartsComponent/barChart.vue'\r\nimport wordCloudEcharts from './echartsComponent/wordCloudEcharts.vue'\r\nexport default {\r\n  name: 'NetworkPolitics',\r\n  components: { PublicOpinionOverallSituationChart, ProgressBarChart, PieChart, DoubleBarChart, barChart, wordCloudEcharts },\r\n  setup () {\r\n    const data = reactive({\r\n      totalCount: 50,\r\n      adoptedCount: 20,\r\n      committeeMember: { submitPercent: 60, submitNum: 345, adoptSituationPercent: 42, adoptSituationNum: 102 },\r\n      unit: { submitPercent: 60, submitNum: 345, adoptSituationPercent: 42, adoptSituationNum: 102 },\r\n      adoptSituationDistribution: [\r\n        { name: '市政协采用', value: 135, percentage: '40%', color: '#4A90E2' },\r\n        { name: '省政协采用', value: 131, percentage: '30%', color: '#4CD9C0' },\r\n        { name: '全国政协采用', value: 126, percentage: '20%', color: '#F56A6A' }\r\n      ],\r\n      instructions: [\r\n        { label: '市政协批示', value: '135', bg: '#EFF6FF', color: '#3B91FB' },\r\n        { label: '省政协批示', value: '131', bg: '#FDF8F0', color: '#EAB308' },\r\n        { label: '全国政协批示', value: '126', bg: '#F0FDF4', color: '#43DDBB' }\r\n      ],\r\n      partyData: [\r\n        { name: '民革', value1: 102, value2: 80 },\r\n        { name: '民盟', value1: 56, value2: 30 },\r\n        { name: '民建', value1: 120, value2: 75 },\r\n        { name: '民进', value1: 34, value2: 20 },\r\n        { name: '农工', value1: 89, value2: 60 },\r\n        { name: '致公', value1: 95, value2: 70 },\r\n        { name: '九三', value1: 80, value2: 55 },\r\n        { name: '工商联', value1: 45, value2: 25 }\r\n      ],\r\n      districtData: [\r\n        { name: '市南', value1: 55, value2: 13 },\r\n        { name: '市北', value1: 20, value2: 6 },\r\n        { name: '李沧', value1: 35, value2: 10 },\r\n        { name: '崂山', value1: 18, value2: 4 },\r\n        { name: '黄岛', value1: 52, value2: 12 },\r\n        { name: '城阳', value1: 10, value2: 2 },\r\n        { name: '即墨', value1: 25, value2: 5 },\r\n        { name: '胶州', value1: 30, value2: 7 },\r\n        { name: '平度', value1: 12, value2: 3 },\r\n        { name: '莱西', value1: 8, value2: 1 }\r\n      ],\r\n      officeData: [\r\n        { name: '委员', value1: 8, value2: 2 },\r\n        { name: '提案', value1: 2, value2: 1 },\r\n        { name: '经济', value1: 5, value2: 1 },\r\n        { name: '农业', value1: 18, value2: 6 },\r\n        { name: '人口环', value1: 50, value2: 15 },\r\n        { name: '教科', value1: 3, value2: 1 },\r\n        { name: '社法', value1: 2, value2: 1 },\r\n        { name: '港澳', value1: 1, value2: 0 },\r\n        { name: '文史', value1: 2, value2: 1 },\r\n        { name: '民宗', value1: 1, value2: 0 },\r\n        { name: '财经', value1: 50, value2: 15 }\r\n      ],\r\n      submitAdoptSituationData: [\r\n        { avatar: require('@/assets/img/largeScreen/man.png'), name: '张伟', submit: 12, adopt: 3 },\r\n        { avatar: require('@/assets/img/largeScreen/woman.png'), name: '刘芳', submit: 11, adopt: 2 },\r\n        { avatar: require('@/assets/img/largeScreen/man.png'), name: '陈明', submit: 11, adopt: 2 },\r\n        { avatar: require('@/assets/img/largeScreen/man.png'), name: '林小华', submit: 11, adopt: 2 },\r\n        { avatar: require('@/assets/img/largeScreen/man.png'), name: '赵天宇', submit: 10, adopt: 1 },\r\n        { avatar: require('@/assets/img/largeScreen/woman.png'), name: '吴静怡', submit: 10, adopt: 1 },\r\n        { avatar: require('@/assets/img/largeScreen/man.png'), name: '黄浩然', submit: 8, adopt: 1 },\r\n        { avatar: require('@/assets/img/largeScreen/woman.png'), name: '周梦琪', submit: 7, adopt: 1 }\r\n      ],\r\n      categoryData: [\r\n        { name: '社会', value: 115 },\r\n        { name: '政治', value: 140 },\r\n        { name: '经济', value: 60 },\r\n        { name: '文化', value: 115 },\r\n        { name: '生态文明', value: 125 }\r\n      ],\r\n      wordCloudData: [\r\n        { name: '乡村振兴', value: 180 },\r\n        { name: '就业优先', value: 165 },\r\n        { name: '科技创新', value: 150 },\r\n        { name: '改革开放', value: 135 },\r\n        { name: '依法治国', value: 120 },\r\n        { name: '教育人才', value: 105 },\r\n        { name: '社会保障', value: 90 },\r\n        { name: '热词', value: 75 },\r\n        { name: '绿色发展', value: 60 },\r\n        { name: '数字中国', value: 45 },\r\n        { name: '共同富裕', value: 40 },\r\n        { name: '文化自信', value: 35 },\r\n        { name: '国家安全', value: 30 },\r\n        { name: '人民至上', value: 25 },\r\n        { name: '中国式现代化', value: 20 }\r\n      ]\r\n    })\r\n\r\n    return { ...toRefs(data) }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.PublicOpinion {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .header_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 15px 0 15px;\r\n\r\n    .header_left {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_left_line {\r\n        width: 3px;\r\n        height: 14px;\r\n        background: #007AFF;\r\n      }\r\n\r\n      .header_left_title {\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #222222;\r\n        margin-left: 8px;\r\n        font-family: Source Han Serif SC, Source Han Serif SC;\r\n      }\r\n    }\r\n\r\n    .header_right {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_right_text {\r\n        font-weight: 400;\r\n        font-size: 12px;\r\n        color: #999999;\r\n      }\r\n\r\n      .header_right_more {\r\n        font-size: 14px;\r\n        color: #0271E3;\r\n        border-radius: 14px;\r\n        border: 1px solid #0271E3;\r\n        padding: 3px 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .overall_situation_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .overall_situation_list {\r\n      height: 160px;\r\n    }\r\n  }\r\n\r\n  .adopt_situation_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .adopt_situation_list {\r\n      padding: 5px 18px 18px 18px;\r\n    }\r\n\r\n    .adopt_situation_distribution_text {\r\n      font-size: 15px;\r\n      color: #222222;\r\n      font-family: Source Han Serif SC, Source Han Serif SC;\r\n      padding-left: 18px;\r\n    }\r\n\r\n    .adopt_situation_distribution_charts {\r\n      width: 100%;\r\n      height: 260px;\r\n      margin-top: 10px;\r\n    }\r\n  }\r\n\r\n  .instructions_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .instructions_list {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 18px;\r\n\r\n      .instructions_item {\r\n        width: 93px;\r\n        height: 90px;\r\n        border-radius: 4px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        .instructions_item_value {\r\n          font-size: 19px;\r\n        }\r\n\r\n        .instructions_item_label {\r\n          font-size: 13px;\r\n          color: #666666;\r\n          margin-top: 14px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .report_adopt_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n  }\r\n\r\n  .submit_adopt_situation_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .submit_adopt_situation_list {\r\n      margin-top: 15px;\r\n\r\n      .submit_adopt_table {\r\n        width: 100%;\r\n        background: #fff;\r\n\r\n        .submit_adopt_table_header,\r\n        .submit_adopt_table_row {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 12px 15px;\r\n          border-bottom: 1px solid #f0f0f0;\r\n\r\n          &:last-child {\r\n            border-bottom: none;\r\n          }\r\n\r\n          .party-header-column {\r\n            flex: 2;\r\n            text-align: left;\r\n            font-size: 14px;\r\n            color: #999;\r\n          }\r\n\r\n          .count-header-column {\r\n            flex: 1;\r\n            text-align: center;\r\n            font-size: 14px;\r\n            color: #999;\r\n          }\r\n\r\n          .party-column {\r\n            flex: 2;\r\n            text-align: left;\r\n            font-size: 14px;\r\n            color: #333;\r\n          }\r\n\r\n          .count-column {\r\n            flex: 1;\r\n            text-align: center;\r\n            font-size: 14px;\r\n            color: #333;\r\n          }\r\n\r\n          .name-cell {\r\n            display: flex;\r\n            align-items: center;\r\n\r\n            .avatar {\r\n              width: 28px;\r\n              height: 28px;\r\n              border-radius: 50%;\r\n              margin-right: 8px;\r\n              object-fit: cover;\r\n              border: 1px solid #e0e0e0;\r\n            }\r\n\r\n            .name {\r\n              font-size: 14px;\r\n              color: #333;\r\n            }\r\n          }\r\n        }\r\n\r\n        .submit_adopt_table_header {\r\n          background: #F1F8FF;\r\n          font-weight: 600;\r\n          color: #222;\r\n          font-size: 14px;\r\n        }\r\n\r\n        .submit_adopt_table_row {\r\n          background: #fff;\r\n          color: #333;\r\n          font-size: 14px;\r\n\r\n          &.row-alt {\r\n            background: #F1F8FF;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .category_analysis_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .category_analysis_list {\r\n      padding: 18px;\r\n    }\r\n  }\r\n\r\n  .hot_words_analysis_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .hot_words_analysis_list {}\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC;AAAuB;;EAO3BA,KAAK,EAAC;AAAwB;;EAMhCA,KAAK,EAAC;AAAqB;;EAOzBA,KAAK,EAAC;AAAsB;;EAY5BA,KAAK,EAAC;AAAqC;;EAK7CA,KAAK,EAAC;AAAkB;;EAOtBA,KAAK,EAAC;AAAmB;;EAIrBA,KAAK,EAAC;AAAyB;;EAKrCA,KAAK,EAAC;AAAkB;;EAOtBA,KAAK,EAAC;AAAmB;;EAgB3BA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAAY;;EASlBA,KAAK,EAAC;AAA6B;;EACjCA,KAAK,EAAC;AAAoB;;EAQrBA,KAAK,EAAC;AAAwB;;;EAE5BA,KAAK,EAAC;AAAM;;EAEdA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAc;;EAM7BA,KAAK,EAAC;AAAuB;;EAO3BA,KAAK,EAAC;AAAwB;;EAMhCA,KAAK,EAAC;AAAwB;;EAO5BA,KAAK,EAAC;AAAyB;;;;;;;;uBAhIxCC,mBAAA,CAsIM,OAtINC,UAsIM,GArIJC,mBAAA,cAAiB,EACjBC,mBAAA,CAWM,OAXNC,UAWM,G,0BAVJD,mBAAA,CAKM;IALDJ,KAAK,EAAC;EAAY,IACrBI,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAAsC;IAAhCJ,KAAK,EAAC;EAAkB,IAC9BI,mBAAA,CAA+C;IAAzCJ,KAAK,EAAC;EAAmB,GAAC,UAAQ,E,wBAG5CI,mBAAA,CAGM,OAHNE,UAGM,GAFJC,YAAA,CACkCC,6CAAA;IADEC,EAAE,EAAC,sBAAsB;IAAE,aAAW,EAAEC,IAAA,CAAAC,UAAU;IACnF,eAAa,EAAED,IAAA,CAAAE;iEAGtBT,mBAAA,UAAa,EACbC,mBAAA,CAsBM,OAtBNS,UAsBM,G,0BArBJT,mBAAA,CAKM;IALDJ,KAAK,EAAC;EAAY,IACrBI,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAAsC;IAAhCJ,KAAK,EAAC;EAAkB,IAC9BI,mBAAA,CAA2C;IAArCJ,KAAK,EAAC;EAAmB,GAAC,MAAI,E,wBAGxCI,mBAAA,CAUM,OAVNU,UAUM,GATJP,YAAA,CAC6GQ,2BAAA;IAD3FC,KAAK,EAAC,MAAM;IAACC,IAAI,EAAC,SAAS;IAAEC,OAAO,EAAER,IAAA,CAAAS,eAAe,CAACC,aAAa;IAClFC,KAAK,EAAEX,IAAA,CAAAS,eAAe,CAACG,SAAS;IAAEC,KAAK,EAAC;iDAC3ChB,YAAA,CAE2EQ,2BAAA;IAFzDC,KAAK,EAAC,MAAM;IAACC,IAAI,EAAC,SAAS;IAAEC,OAAO,EAAER,IAAA,CAAAS,eAAe,CAACK,qBAAqB;IAC1FH,KAAK,EAAEX,IAAA,CAAAS,eAAe,CAACM,iBAAiB;IACzCF,KAAK,EAAC;iDACRhB,YAAA,CAC0EQ,2BAAA;IADxDC,KAAK,EAAC,MAAM;IAACC,IAAI,EAAC,SAAS;IAAEC,OAAO,EAAER,IAAA,CAAAgB,IAAI,CAACN,aAAa;IAAGC,KAAK,EAAEX,IAAA,CAAAgB,IAAI,CAACJ,SAAS;IAChGC,KAAK,EAAC;iDACRhB,YAAA,CAC2GQ,2BAAA;IADzFC,KAAK,EAAC,MAAM;IAACC,IAAI,EAAC,SAAS;IAAEC,OAAO,EAAER,IAAA,CAAAgB,IAAI,CAACF,qBAAqB;IAC/EH,KAAK,EAAEX,IAAA,CAAAgB,IAAI,CAACD,iBAAiB;IAAEF,KAAK,EAAC;6EAE1CnB,mBAAA,CAA2D;IAAtDJ,KAAK,EAAC;EAAmC,GAAC,QAAM,sBACrDI,mBAAA,CAEM,OAFNuB,UAEM,GADJpB,YAAA,CAA8GqB,mBAAA;IAApGnB,EAAE,EAAC,4BAA4B;IAAE,YAAU,EAAEC,IAAA,CAAAmB,0BAA0B;IAAGC,MAAM,EAAE;+CAGhG3B,mBAAA,UAAa,EACbC,mBAAA,CAcM,OAdN2B,UAcM,G,0BAbJ3B,mBAAA,CAKM;IALDJ,KAAK,EAAC;EAAY,IACrBI,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAAsC;IAAhCJ,KAAK,EAAC;EAAkB,IAC9BI,mBAAA,CAA2C;IAArCJ,KAAK,EAAC;EAAmB,GAAC,MAAI,E,wBAGxCI,mBAAA,CAMM,OANN4B,UAMM,I,kBALJ/B,mBAAA,CAIMgC,SAAA,QAAAC,WAAA,CAJiDxB,IAAA,CAAAyB,YAAY,GAA5BC,IAAI,EAAEC,KAAK;yBAAlDpC,mBAAA,CAIM;MAJDD,KAAK,EAAC,mBAAmB;MAAwCsC,GAAG,EAAED,KAAK;MAC7EE,KAAK,EAAAC,eAAA,gBAAiBJ,IAAI,CAACK,EAAE;QAC9BrC,mBAAA,CAA2F;MAAtFJ,KAAK,EAAC,yBAAyB;MAAEuC,KAAK,EAAAC,eAAA,WAAYJ,IAAI,CAACb,KAAK;wBAAOa,IAAI,CAACf,KAAK,yBAClFjB,mBAAA,CAA2D,OAA3DsC,UAA2D,EAAAC,gBAAA,CAAnBP,IAAI,CAACQ,KAAK,iB;sCAIxDzC,mBAAA,gBAAmB,EACnBC,mBAAA,CAqBM,OArBNyC,WAqBM,G,0BApBJzC,mBAAA,CAKM;IALDJ,KAAK,EAAC;EAAY,IACrBI,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAAsC;IAAhCJ,KAAK,EAAC;EAAkB,IAC9BI,mBAAA,CAAiD;IAA3CJ,KAAK,EAAC;EAAmB,GAAC,YAAU,E,wBAG9CI,mBAAA,CAaM,OAbN0C,WAaM,GAZJvC,YAAA,CAG+CwC,yBAAA;IAH/BtC,EAAE,EAAC,mBAAmB;IAAEuC,IAAI,EAAEtC,IAAA,CAAAuC,SAAS;IAAGC,MAAM,EAAE,gBAAgB;IAAG3B,KAAK,EAAE,C,0EAG3F;IAAEP,KAAK,EAAC,YAAY;IAACuB,KAAsB,EAAtB;MAAA;IAAA;qCACtBhC,YAAA,CAGwCwC,yBAAA;IAHxBtC,EAAE,EAAC,sBAAsB;IAAEuC,IAAI,EAAEtC,IAAA,CAAAyC,YAAY;IAAGD,MAAM,EAAE,gBAAgB;IAAG3B,KAAK,EAAE,C,0EAGjG;IAAEP,KAAK,EAAC,KAAK;IAACuB,KAAsB,EAAtB;MAAA;IAAA;qCACfhC,YAAA,CAG2CwC,yBAAA;IAH3BtC,EAAE,EAAC,oBAAoB;IAAEuC,IAAI,EAAEtC,IAAA,CAAA0C,UAAU;IAAGF,MAAM,EAAE,gBAAgB;IAAG3B,KAAK,EAAE,C,0EAG7F;IAAEP,KAAK,EAAC,QAAQ;IAACuB,KAAsB,EAAtB;MAAA;IAAA;yCAGtBpC,mBAAA,eAAkB,EAClBC,mBAAA,CA4BM,OA5BNiD,WA4BM,GA3BJjD,mBAAA,CAQM,OARNkD,WAQM,G,0BAPJlD,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAAsC;IAAhCJ,KAAK,EAAC;EAAkB,IAC9BI,mBAAA,CAAgD;IAA1CJ,KAAK,EAAC;EAAmB,GAAC,WAAS,E,sBAE3CI,mBAAA,CAEM;IAFDJ,KAAK,EAAC,cAAc;IAAEuD,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAE/C,IAAA,CAAAgD,QAAQ;gCACxCtD,mBAAA,CAA2C;IAArCJ,KAAK,EAAC;EAAmB,GAAC,MAAI,oB,MAGxCI,mBAAA,CAiBM,OAjBNuD,WAiBM,GAhBJvD,mBAAA,CAeM,OAfNwD,WAeM,G,0BAdJxD,mBAAA,CAIM;IAJDJ,KAAK,EAAC;EAA2B,IACpCI,mBAAA,CAA2C;IAArCJ,KAAK,EAAC;EAAqB,GAAC,IAAE,GACpCI,mBAAA,CAA6C;IAAvCJ,KAAK,EAAC;EAAqB,GAAC,MAAI,GACtCI,mBAAA,CAA6C;IAAvCJ,KAAK,EAAC;EAAqB,GAAC,MAAI,E,yCAExCC,mBAAA,CAQMgC,SAAA,QAAAC,WAAA,CARoDxB,IAAA,CAAAmD,wBAAwB,GAAtCzB,IAAI,EAAE0B,GAAG;yBAArD7D,mBAAA,CAQM;MARDD,KAAK,EAAA+D,eAAA,EAAC,wBAAwB;QAAA,WACZD,GAAG;MAAA;MAD2DxB,GAAG,EAAEF,IAAI,CAAC4B;QAE7F5D,mBAAA,CAGO,QAHP6D,WAGO,GAFL7D,mBAAA,CAAyC;MAAnC8D,GAAG,EAAE9B,IAAI,CAAC+B,MAAM;MAAEnE,KAAK,EAAC;0CAC9BI,mBAAA,CAAyC,QAAzCgE,WAAyC,EAAAzB,gBAAA,CAAnBP,IAAI,CAAC4B,IAAI,iB,GAEjC5D,mBAAA,CAAmD,QAAnDiE,WAAmD,EAAA1B,gBAAA,CAArBP,IAAI,CAACkC,MAAM,kBACzClE,mBAAA,CAAkD,QAAlDmE,WAAkD,EAAA5B,gBAAA,CAApBP,IAAI,CAACoC,KAAK,iB;wCAKhDrE,mBAAA,UAAa,EACbC,mBAAA,CAWM,OAXNqE,WAWM,G,0BAVJrE,mBAAA,CAKM;IALDJ,KAAK,EAAC;EAAY,IACrBI,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAAsC;IAAhCJ,KAAK,EAAC;EAAkB,IAC9BI,mBAAA,CAA2C;IAArCJ,KAAK,EAAC;EAAmB,GAAC,MAAI,E,wBAGxCI,mBAAA,CAGM,OAHNsE,WAGM,GAFJnE,YAAA,CACyBoE,mBAAA;IADflE,EAAE,EAAC,kBAAkB;IAAEuC,IAAI,EAAEtC,IAAA,CAAAkE,YAAY;IAAGrD,KAAK,EAAE,mCAAmC;IAC9FgB,KAAoB,EAApB;MAAA;IAAA;yCAGNpC,mBAAA,UAAa,EACbC,mBAAA,CAYM,OAZNyE,WAYM,G,4BAXJzE,mBAAA,CAKM;IALDJ,KAAK,EAAC;EAAY,IACrBI,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAAsC;IAAhCJ,KAAK,EAAC;EAAkB,IAC9BI,mBAAA,CAA2C;IAArCJ,KAAK,EAAC;EAAmB,GAAC,MAAI,E,wBAGxCI,mBAAA,CAIM,OAJN0E,WAIM,GAHJvE,YAAA,CAEyBwE,2BAAA;IAFPtE,EAAE,EAAC,WAAW;IAAEuE,QAAQ,EAAEtE,IAAA,CAAAuE,aAAa;IACtDC,SAAS,EAAE,qKAAqK;IAChLC,SAAS,EAAE", "ignoreList": []}]}