{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\Home.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\Home.vue", "mtime": 1753932846834}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["toRefs", "reactive", "MapQingdao", "horizontalBarEcharts", "pieEchartsLegend", "components", "setup", "data", "mapData", "name", "value", "circles", "cppccMemberNum", "standingCommitteeNum", "barList", "proposalStatsNum", "label", "color", "typeAnalysisList", "workDynamicsList", "title", "date", "socialStats", "total", "bg", "adopted", "adoptedColor", "meettingActivityList", "cardClass", "icon", "require", "label1", "value1", "value1Style", "label2", "value2", "value2Style", "discussionsList", "number", "unit", "desc", "hotTopics", "performanceStatistics", "meeting", "proposal", "opinion"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\Home.vue"], "sourcesContent": ["<template>\n  <div class=\"home_page\">\n    <div class=\"map_section\">\n      <MapQingdao :mapData=\"mapData\" />\n    </div>\n    <!-- 委员统计 -->\n    <div class=\"home_committee_statistics\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">委员统计</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">{{ circles }}</span>\n        </div>\n      </div>\n      <div class=\"committee_statistics_box\">\n        <div class=\"statistics_card card_blue\">\n          <div class=\"card_content\">\n            <div class=\"card_number\">{{ cppccMemberNum }}</div>\n            <div class=\"card_label\">政协委员(人)</div>\n          </div>\n        </div>\n        <div class=\"statistics_card card_yellow\">\n          <div class=\"card_content\">\n            <div class=\"card_number\">{{ standingCommitteeNum }}</div>\n            <div class=\"card_label\">政协常委(人)</div>\n          </div>\n        </div>\n      </div>\n      <div class=\"circles_box\">\n        <div class=\"circles_top_header\">\n          <span class=\"circles_top_header_title\">界别分布</span>\n          <span class=\"circles_top_header_more\" style=\"\">查看全部</span>\n        </div>\n        <horizontalBarEcharts id=\"circles\" :barList=\"barList\" colorStart=\"#FFFFFF\" colorEnd=\"#EF817C\"\n          style=\"height: 260px;\" />\n      </div>\n    </div>\n    <!-- 提案统计 -->\n    <div class=\"home_proposal_statistics\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">提案统计</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">{{ circles }}</span>\n        </div>\n      </div>\n      <div class=\"proposal_statistics_box\">\n        <div class=\"proposal_card\" v-for=\"(item, index) in proposalStatsNum\" :key=\"index\">\n          <div class=\"proposal_number\" :style=\"{ color: item.color }\">{{ item.value }}</div>\n          <div class=\"proposal_label\">{{ item.label }}</div>\n        </div>\n      </div>\n      <div class=\"proposal_type_analysis\">\n        <pieEchartsLegend id=\"typeAnalysisPie\" :dataList=\"typeAnalysisList\" title=\"类型分析\" />\n      </div>\n    </div>\n    <!-- 工作动态 -->\n    <div class=\"home_work_dynamics\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">工作动态</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">本年</span>\n        </div>\n      </div>\n      <div class=\"work_dynamics_list\">\n        <div class=\"work_dynamics_item\" v-for=\"(item, idx) in workDynamicsList\" :key=\"idx\">\n          <div class=\"work_dynamics_title\">{{ item.title }}</div>\n          <div class=\"work_dynamics_date\">{{ item.date }}</div>\n        </div>\n      </div>\n    </div>\n    <!-- 社情民意 -->\n    <div class=\"home_social\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">社情民意</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">本年</span>\n        </div>\n      </div>\n      <div class=\"social_box\">\n        <!-- 总数卡片 -->\n        <div class=\"social_card total_card\" :style=\"{ background: socialStats[0].bg }\">\n          <div class=\"total_card_number\" :style=\"{ color: socialStats[0].color }\">{{ socialStats[0].total }}</div>\n          <div class=\"total_card_label\">{{ socialStats[0].label }}</div>\n        </div>\n        <!-- 报送卡片 -->\n        <div class=\"social_card report_card\" v-for=\"(item, idx) in socialStats.slice(1)\" :key=\"idx\"\n          :style=\"{ background: item.bg }\">\n          <div class=\"report_row\">\n            <span class=\"report_label\">总数</span>\n            <span class=\"report_total\" :style=\"{ color: item.color }\">{{ item.total }}</span>\n          </div>\n          <div class=\"report_row\">\n            <span class=\"report_label\">采用</span>\n            <span class=\"report_adopted\" :style=\"{ color: item.adoptedColor }\">{{ item.adopted }}</span>\n          </div>\n          <div class=\"report_card_label\">{{ item.label }}</div>\n        </div>\n      </div>\n    </div>\n    <!-- 会议活动 -->\n    <div class=\"home_meetting_activity\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">会议活动</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">本年</span>\n        </div>\n      </div>\n      <div class=\"meetting_activity_box\">\n        <div v-for=\"(item, idx) in meettingActivityList\" :key=\"idx\" class=\"meetting_activity_card\"\n          :class=\"item.cardClass\">\n          <img :src=\"item.icon\" class=\"activity_card_iconimg\" />\n          <div class=\"meetting_activity_card_content\">\n            <div class=\"meetting_activity_card_label\">{{ item.label1 }}</div>\n            <div class=\"meetting_activity_card_value\" :style=\"item.value1Style\">{{ item.value1 }}</div>\n            <div class=\"meetting_activity_card_label\">{{ item.label2 }}</div>\n            <div class=\"meetting_activity_card_value\" :style=\"item.value2Style\">{{ item.value2 }}</div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <!-- 网络议政 -->\n    <div class=\"home_discussions\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">网络议政</span>\n        </div>\n      </div>\n      <div class=\"discussions_box\">\n        <div class=\"discussion_card\" v-for=\"(item, idx) in discussionsList\" :key=\"idx\"\n          :style=\"{ backgroundImage: `url(${item.bg})` }\">\n          <div class=\"discussion_card_number\">{{ item.number }}<span class=\"discussion_card_unit\">{{ item.unit }}</span>\n          </div>\n          <div class=\"discussion_card_label\">{{ item.label }}</div>\n        </div>\n      </div>\n      <div class=\"hot_topics\">\n        <div class=\"hot_topics_title\">最热话题</div>\n        <div class=\"hot_topics_list\">\n          <div class=\"hot_topic_item\" v-for=\"(topic, idx) in hotTopics\" :key=\"idx\">\n            <span class=\"hot_topic_index\" :class=\"'hot_topic_index_' + (idx + 1)\">{{ idx + 1 }}</span>\n            <span class=\"hot_topic_text\">{{ topic.title }}</span>\n            <span class=\"hot_topic_tag\" :class=\"'hot_topic_tag_' + (idx + 1)\">热</span>\n          </div>\n        </div>\n      </div>\n    </div>\n    <!-- 履职统计 -->\n    <div class=\"home_performance_statistics\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">履职统计</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">{{ circles }}</span>\n        </div>\n      </div>\n      <div class=\"performance_statistics_box\">\n        <div class=\"performance_table\">\n          <div class=\"performance_table_header\">\n            <span>姓名</span>\n            <span>会议活动</span>\n            <span>政协提案</span>\n            <span>社情民意</span>\n          </div>\n          <div class=\"performance_table_row\" v-for=\"(item, idx) in performanceStatistics\" :key=\"item.name\"\n            :class=\"{ 'row-alt': idx % 2 === 1 }\">\n            <span>{{ item.name }}</span>\n            <span>{{ item.meeting }}</span>\n            <span>{{ item.proposal }}</span>\n            <span>{{ item.opinion }}</span>\n          </div>\n          <div class=\"performance_table_footer\">\n            <button class=\"view-all-btn\">查看全部</button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n<script>\nimport { toRefs, reactive } from 'vue'\nimport MapQingdao from './echartsComponent/MapQingdao.vue'\nimport horizontalBarEcharts from './echartsComponent/horizontalBarEcharts.vue'\nimport pieEchartsLegend from './echartsComponent/pieEchartsLegend.vue'\n\nexport default {\n  components: { MapQingdao, horizontalBarEcharts, pieEchartsLegend },\n  setup () {\n    const data = reactive({\n      mapData: [\n        { name: '市南区', value: 80 },\n        { name: '市北区', value: 60 },\n        { name: '李沧区', value: 50 },\n        { name: '崂山区', value: 40 },\n        { name: '城阳区', value: 70 },\n        { name: '黄岛区', value: 90 },\n        { name: '即墨区', value: 30 },\n        { name: '胶州市', value: 55 },\n        { name: '平度市', value: 20 },\n        { name: '莱西市', value: 10 }\n      ],\n      circles: '十一届二次',\n      cppccMemberNum: '10095',\n      standingCommitteeNum: '8742',\n      barList: [\n        { name: '教育界', value: 35 },\n        { name: '医药卫生界', value: 15 },\n        { name: '经济界', value: 14 },\n        { name: '工商联界', value: 21 },\n        { name: '民革界', value: 15 },\n        { name: '特邀界', value: 21 },\n        { name: '妇联界', value: 8 },\n        { name: '工会界', value: 8 },\n        { name: '社会福利与社会保障界', value: 14 }\n      ],\n      proposalStatsNum: [\n        { value: 873, label: '提案总数', color: '#2386F9' },\n        { value: 456, label: '委员提案', color: '#2CA6F9' },\n        { value: 354, label: '界别提案', color: '#3AC86B' },\n        { value: 221, label: '组织提案', color: '#F96C9C' }\n      ],\n      typeAnalysisList: [\n        { name: '发改财政', value: 22.52, color: '#3DC3F0' },\n        { name: '民政市场', value: 18.33, color: '#4AC6A8' },\n        { name: '公安司法', value: 12.5, color: '#F9C846' },\n        { name: '区市政府', value: 11.34, color: '#6DD3A0' },\n        { name: '科技工信', value: 9.56, color: '#7B8DF9' },\n        { name: '教育文化', value: 8.09, color: '#F97C9C' },\n        { name: '派出机构', value: 4.21, color: '#F9A846' },\n        { name: '驻青单位', value: 3.71, color: '#F97C46' },\n        { name: '住建交通', value: 3.65, color: '#A97CF9' },\n        { name: '农村卫生', value: 3.21, color: '#4A9CF9' },\n        { name: '其他机构', value: 1.86, color: '#BFBFBF' },\n        { name: '党群其他', value: 1.02, color: '#F9C8C8' }\n      ],\n      workDynamicsList: [\n        { title: '市政协社会和法制工作办公室围绕市政协社会和法制工作办公室围绕', date: '2025-06-03' },\n        { title: '“与民同行 共创共赢”新格局下民市政协社会和法制工作办公室围绕', date: '2025-05-30' },\n        { title: '“惠民生·基层行”义诊活动温暖人心市政协社会和法制工作办公室围绕', date: '2025-05-30' },\n        { title: '市科技局面复市政协科技界别提案市政协社会和法制工作办公室围绕', date: '2025-05-30' },\n        { title: '孟庆斌到胶州市、崂山区调研项目时市政协社会和法制工作办公室围绕', date: '2025-05-29' }\n      ],\n      socialStats: [\n        { total: 1057, label: '总数', bg: '#EFF6FF', color: '#2386F9' },\n        { total: 345, adopted: 21, label: '委员报送', bg: '#FDF8F0', color: '#2386F9', adoptedColor: '#F9C846' },\n        { total: 547, adopted: 79, label: '单位报送', bg: '#F0FDF4', color: '#3AC86B', adoptedColor: '#F9C846' }\n      ],\n      meettingActivityList: [\n        {\n          cardClass: 'card_meeting',\n          icon: require('../../../assets/img/largeScreen/icon_meetting.png'),\n          label1: '会议次数',\n          value1: 201,\n          value1Style: { color: '#308FFF' },\n          label2: '会议人数',\n          value2: 2412,\n          value2Style: { color: '#308FFF' }\n        },\n        {\n          cardClass: 'card_activity',\n          icon: require('../../../assets/img/largeScreen/icon_acticity.png'),\n          label1: '活动次数',\n          value1: 310,\n          value1Style: { color: '#1FC6FF' },\n          label2: '活动人数',\n          value2: 4015,\n          value2Style: { color: '#1FC6FF' }\n        }\n      ],\n      discussionsList: [\n        {\n          bg: require('../../../assets/img/largeScreen/icon_release_bg.png'),\n          number: '72',\n          unit: '个',\n          label: '发布议题',\n          desc: ''\n        },\n        {\n          bg: require('../../../assets/img/largeScreen/icon_participate_bg.png'),\n          number: '39301',\n          unit: '次',\n          label: '累计参与人次',\n          desc: ''\n        },\n        {\n          bg: require('../../../assets/img/largeScreen/icon_seek_bg.png'),\n          number: '12308',\n          unit: '条',\n          label: '累计征求意见',\n          desc: ''\n        }\n      ],\n      hotTopics: [\n        { title: '推进黄河国家文化公园建设' },\n        { title: '持续推进黄河流域生态保护修复，助力…' },\n        { title: '全面加强新时代中小学劳动教育' }\n      ],\n      performanceStatistics: [\n        { name: '马平安', meeting: 515, proposal: 15, opinion: 0 },\n        { name: '马波', meeting: 400, proposal: 0, opinion: 12 },\n        { name: '王玉民', meeting: 490, proposal: 15, opinion: 0 },\n        { name: '王洋宝', meeting: 500, proposal: 0, opinion: 1 },\n        { name: '王忠', meeting: 420, proposal: 0, opinion: 2 },\n        { name: '刘彩霞', meeting: 512, proposal: 0, opinion: 1 },\n        { name: '刘军', meeting: 500, proposal: 20, opinion: 0 },\n        { name: '吴雪玲', meeting: 315, proposal: 15, opinion: 38 },\n        { name: '杨文比', meeting: 310, proposal: 60, opinion: 28 },\n        { name: '贾谊', meeting: 540, proposal: 9, opinion: 13 }\n      ]\n    })\n    return { ...toRefs(data) }\n  }\n}\n</script>\n<style lang=\"less\" scoped>\n.home_page {\n  width: 100%;\n  height: 100%;\n\n  .header_box {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 15px 15px 0 15px;\n\n    .header_left {\n      display: flex;\n      align-items: center;\n\n      .header_left_line {\n        width: 3px;\n        height: 14px;\n        background: #007AFF;\n      }\n\n      .header_left_title {\n        font-weight: bold;\n        font-size: 15px;\n        color: #222222;\n        margin-left: 8px;\n        font-family: Source Han Serif SC, Source Han Serif SC;\n      }\n    }\n\n    .header_right {\n      display: flex;\n      align-items: center;\n\n      .header_right_text {\n        font-weight: 400;\n        font-size: 12px;\n        color: #999999;\n      }\n    }\n  }\n\n  .map_section {\n    background: #fff;\n    border-radius: 8px;\n    padding: 10px;\n  }\n\n  .home_committee_statistics {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .committee_statistics_box {\n      display: flex;\n      gap: 15px;\n      padding: 20px 15px 10px 15px;\n\n      .statistics_card {\n        flex: 1;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: #f5faff;\n        position: relative;\n        height: 86px;\n\n        .card_content {\n          display: flex;\n          flex-direction: column;\n          align-items: flex-start;\n          justify-content: center;\n          margin-left: 55px;\n          margin-top: 5px;\n\n          .card_number {\n            font-size: 20px;\n            color: #4AA3FF;\n          }\n\n          .card_label {\n            font-size: 14px;\n            color: #666;\n            margin-top: 2px;\n          }\n        }\n      }\n\n      .card_blue {\n        background-image: url('../../../assets/img/largeScreen/icon_member_bg.png');\n        background-size: 100% 100%;\n        background-position: center;\n      }\n\n      .card_yellow {\n        background-image: url('../../../assets/img/largeScreen/icon_committee_bg.png');\n        background-size: 100% 100%;\n        background-position: center;\n\n        .card_number {\n          color: #E6B800 !important;\n        }\n      }\n    }\n\n    .circles_box {\n      border-radius: 6px;\n      margin: 10px 12px;\n\n      .circles_top_header {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n\n        .circles_top_header_title {\n          font-size: 14px;\n          color: #000;\n          font-family: Source Han Serif SC, Source Han Serif SC;\n        }\n\n        .circles_top_header_more {\n          font-size: 14px;\n          color: #0271E3;\n          border-radius: 14px;\n          border: 1px solid #0271E3;\n          padding: 3px 10px;\n        }\n      }\n    }\n  }\n\n  .home_proposal_statistics {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .proposal_statistics_box {\n      display: flex;\n      justify-content: space-between;\n      padding: 20px 15px 10px 15px;\n\n      .proposal_card {\n        flex: 1;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n\n        .proposal_number {\n          font-size: 24px;\n        }\n\n        .proposal_label {\n          font-size: 14px;\n          color: #999;\n          margin-top: 4px;\n        }\n      }\n    }\n\n    .proposal_type_analysis {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n    }\n  }\n\n  .home_work_dynamics {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .work_dynamics_list {\n      padding: 8px 15px;\n      background: #fff;\n\n      .work_dynamics_item {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        padding: 12px 0;\n        border-bottom: 1px solid #f0f0f0;\n\n        &:last-child {\n          border-bottom: none;\n        }\n\n        .work_dynamics_title {\n          flex: 1;\n          font-size: 14px;\n          color: #666666;\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n        }\n\n        .work_dynamics_date {\n          font-size: 14px;\n          color: #bdbdbd;\n          flex-shrink: 0;\n        }\n      }\n    }\n  }\n\n  .home_social {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .social_box {\n      display: flex;\n      gap: 16px;\n      padding: 20px 15px;\n\n      .social_card {\n        flex: 1;\n        width: 97px;\n        height: 94px;\n        border-radius: 10px;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        box-sizing: border-box;\n        box-shadow: none;\n        padding: 15px;\n        background-clip: padding-box;\n      }\n\n      .total_card {\n        justify-content: center;\n\n        .total_card_number {\n          font-size: 20px;\n          color: #3B91FB;\n          margin-bottom: 5px;\n        }\n\n        .total_card_label {\n          font-size: 14px;\n          color: #666666;\n        }\n      }\n\n      .report_card {\n        justify-content: flex-start;\n\n        .report_row {\n          width: 100%;\n          display: flex;\n          justify-content: space-between;\n          align-items: baseline;\n          margin-bottom: 2px;\n\n          .report_label {\n            font-size: 14px;\n            color: #999;\n            margin-right: 2px;\n          }\n\n          .report_total {\n            font-size: 15px;\n          }\n\n          .report_adopted {\n            font-size: 15px;\n          }\n        }\n\n        .report_card_label {\n          margin-top: 5px;\n          font-size: 15px;\n          color: #666;\n          font-family: Source Han Serif SC, Source Han Serif SC;\n        }\n      }\n    }\n  }\n\n  .home_meetting_activity {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .meetting_activity_box {\n      display: flex;\n      gap: 16px;\n      padding: 20px 15px;\n\n      .meetting_activity_card {\n        flex: 1;\n        display: flex;\n        align-items: flex-start;\n        box-sizing: border-box;\n        width: 157px;\n        height: 140px;\n        padding: 14px 20px;\n\n        .activity_card_iconimg {\n          width: 32px;\n          height: 32px;\n          margin-right: 15px;\n          margin-top: 4px;\n        }\n\n        .meetting_activity_card_content {\n          display: flex;\n          flex-direction: column;\n          justify-content: center;\n          flex: 1;\n\n          .meetting_activity_card_label {\n            font-size: 14px;\n            color: #999;\n            margin-bottom: 5px;\n          }\n\n          .meetting_activity_card_value {\n            font-size: 20px;\n            color: #308FFF;\n            margin-bottom: 8px;\n          }\n        }\n      }\n\n      .card_meeting {\n        background-image: url('../../../assets/img/largeScreen/icon_meetting_bg.png');\n        background-size: 100% 100%;\n        background-position: center;\n      }\n\n      .card_activity {\n        background-image: url('../../../assets/img/largeScreen/icon_activity_bg.png');\n        background-size: 100% 100%;\n        background-position: center;\n      }\n    }\n  }\n\n  .home_discussions {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .discussions_box {\n      display: flex;\n      gap: 10px;\n      padding: 20px 15px;\n      justify-content: flex-start;\n\n      .discussion_card {\n        flex: 1;\n        width: 103px;\n        height: 77px;\n        background-size: 100% 100%;\n        background-position: center;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        color: #fff;\n\n        .discussion_card_number {\n          font-size: 20px;\n          margin-bottom: 2px;\n\n          .discussion_card_unit {\n            font-size: 12px;\n            font-weight: normal;\n            margin-left: 2px;\n          }\n        }\n\n        .discussion_card_label {\n          font-size: 14px;\n          font-weight: 400;\n          margin-bottom: 2px;\n        }\n      }\n    }\n\n    .hot_topics {\n      padding: 0 15px 15px 15px;\n\n      .hot_topics_title {\n        font-size: 14px;\n        color: #000;\n        margin-bottom: 10px;\n        font-family: Source Han Serif SC, Source Han Serif SC;\n      }\n\n      .hot_topics_list {\n        .hot_topic_item {\n          display: flex;\n          align-items: center;\n          border-bottom: 1px solid #f0f0f0;\n          padding: 12px 0;\n\n          .hot_topic_index {\n            font-size: 14px;\n            margin-right: 10px;\n          }\n\n          .hot_topic_index_1 {\n            color: #FF4D4F;\n          }\n\n          .hot_topic_index_2 {\n            color: #FF9900;\n          }\n\n          .hot_topic_index_3 {\n            color: #FFD600;\n          }\n\n          .hot_topic_text {\n            flex: 1;\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            font-size: 14px;\n            color: #666;\n          }\n\n          .hot_topic_tag {\n            font-size: 14px;\n            color: #fff;\n            border-radius: 2px;\n            width: 22px;\n            height: 22px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n          }\n\n          .hot_topic_tag_1 {\n            background: #FB3030;\n          }\n\n          .hot_topic_tag_2 {\n            background: #FF833E;\n          }\n\n          .hot_topic_tag_3 {\n            background: #FFD978;\n          }\n        }\n      }\n    }\n  }\n\n  .home_performance_statistics {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .performance_statistics_box {\n      margin-top: 15px;\n\n      .performance_table {\n        width: 100%;\n        background: #fff;\n\n        .performance_table_header,\n        .performance_table_row {\n          display: flex;\n          align-items: center;\n          padding: 8px 0;\n\n          span {\n            flex: 1;\n            text-align: center;\n          }\n        }\n\n        .performance_table_header {\n          background: #F1F8FF;\n          font-weight: bold;\n          color: #222;\n          font-size: 14px;\n        }\n\n        .performance_table_row {\n          background: #fff;\n          color: #222;\n          font-size: 14px;\n\n          &.row-alt {\n            background: #F1F8FF;\n          }\n        }\n\n        .performance_table_footer {\n          display: flex;\n          justify-content: center;\n          padding: 10px 0;\n          background: #fff;\n\n          .view-all-btn {\n            border: 1px solid #0271e3;\n            color: #0271e3;\n            background: #fff;\n            border-radius: 16px;\n            padding: 4px 14px;\n            font-size: 14px;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"], "mappings": "AAoMA,SAASA,MAAM,EAAEC,QAAO,QAAS,KAAI;AACrC,OAAOC,UAAS,MAAO,mCAAkC;AACzD,OAAOC,oBAAmB,MAAO,6CAA4C;AAC7E,OAAOC,gBAAe,MAAO,yCAAwC;AAErE,eAAe;EACbC,UAAU,EAAE;IAAEH,UAAU;IAAEC,oBAAoB;IAAEC;EAAiB,CAAC;EAClEE,KAAIA,CAAA,EAAK;IACP,MAAMC,IAAG,GAAIN,QAAQ,CAAC;MACpBO,OAAO,EAAE,CACP;QAAEC,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAG,CAAC,EAC1B;QAAED,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAG,CAAC,EAC1B;QAAED,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAG,CAAC,EAC1B;QAAED,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAG,CAAC,EAC1B;QAAED,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAG,CAAC,EAC1B;QAAED,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAG,CAAC,EAC1B;QAAED,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAG,CAAC,EAC1B;QAAED,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAG,CAAC,EAC1B;QAAED,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAG,CAAC,EAC1B;QAAED,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAG,EAC1B;MACDC,OAAO,EAAE,OAAO;MAChBC,cAAc,EAAE,OAAO;MACvBC,oBAAoB,EAAE,MAAM;MAC5BC,OAAO,EAAE,CACP;QAAEL,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAG,CAAC,EAC1B;QAAED,IAAI,EAAE,OAAO;QAAEC,KAAK,EAAE;MAAG,CAAC,EAC5B;QAAED,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAG,CAAC,EAC1B;QAAED,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAG,CAAC,EAC3B;QAAED,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAG,CAAC,EAC1B;QAAED,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAG,CAAC,EAC1B;QAAED,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAE,CAAC,EACzB;QAAED,IAAI,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAE,CAAC,EACzB;QAAED,IAAI,EAAE,YAAY;QAAEC,KAAK,EAAE;MAAG,EACjC;MACDK,gBAAgB,EAAE,CAChB;QAAEL,KAAK,EAAE,GAAG;QAAEM,KAAK,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAU,CAAC,EAC/C;QAAEP,KAAK,EAAE,GAAG;QAAEM,KAAK,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAU,CAAC,EAC/C;QAAEP,KAAK,EAAE,GAAG;QAAEM,KAAK,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAU,CAAC,EAC/C;QAAEP,KAAK,EAAE,GAAG;QAAEM,KAAK,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAU,EAC/C;MACDC,gBAAgB,EAAE,CAChB;QAAET,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE,KAAK;QAAEO,KAAK,EAAE;MAAU,CAAC,EAChD;QAAER,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE,KAAK;QAAEO,KAAK,EAAE;MAAU,CAAC,EAChD;QAAER,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE,IAAI;QAAEO,KAAK,EAAE;MAAU,CAAC,EAC/C;QAAER,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE,KAAK;QAAEO,KAAK,EAAE;MAAU,CAAC,EAChD;QAAER,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE,IAAI;QAAEO,KAAK,EAAE;MAAU,CAAC,EAC/C;QAAER,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE,IAAI;QAAEO,KAAK,EAAE;MAAU,CAAC,EAC/C;QAAER,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE,IAAI;QAAEO,KAAK,EAAE;MAAU,CAAC,EAC/C;QAAER,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE,IAAI;QAAEO,KAAK,EAAE;MAAU,CAAC,EAC/C;QAAER,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE,IAAI;QAAEO,KAAK,EAAE;MAAU,CAAC,EAC/C;QAAER,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE,IAAI;QAAEO,KAAK,EAAE;MAAU,CAAC,EAC/C;QAAER,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE,IAAI;QAAEO,KAAK,EAAE;MAAU,CAAC,EAC/C;QAAER,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE,IAAI;QAAEO,KAAK,EAAE;MAAU,EAC/C;MACDE,gBAAgB,EAAE,CAChB;QAAEC,KAAK,EAAE,gCAAgC;QAAEC,IAAI,EAAE;MAAa,CAAC,EAC/D;QAAED,KAAK,EAAE,iCAAiC;QAAEC,IAAI,EAAE;MAAa,CAAC,EAChE;QAAED,KAAK,EAAE,kCAAkC;QAAEC,IAAI,EAAE;MAAa,CAAC,EACjE;QAAED,KAAK,EAAE,gCAAgC;QAAEC,IAAI,EAAE;MAAa,CAAC,EAC/D;QAAED,KAAK,EAAE,iCAAiC;QAAEC,IAAI,EAAE;MAAa,EAChE;MACDC,WAAW,EAAE,CACX;QAAEC,KAAK,EAAE,IAAI;QAAEP,KAAK,EAAE,IAAI;QAAEQ,EAAE,EAAE,SAAS;QAAEP,KAAK,EAAE;MAAU,CAAC,EAC7D;QAAEM,KAAK,EAAE,GAAG;QAAEE,OAAO,EAAE,EAAE;QAAET,KAAK,EAAE,MAAM;QAAEQ,EAAE,EAAE,SAAS;QAAEP,KAAK,EAAE,SAAS;QAAES,YAAY,EAAE;MAAU,CAAC,EACpG;QAAEH,KAAK,EAAE,GAAG;QAAEE,OAAO,EAAE,EAAE;QAAET,KAAK,EAAE,MAAM;QAAEQ,EAAE,EAAE,SAAS;QAAEP,KAAK,EAAE,SAAS;QAAES,YAAY,EAAE;MAAU,EACpG;MACDC,oBAAoB,EAAE,CACpB;QACEC,SAAS,EAAE,cAAc;QACzBC,IAAI,EAAEC,OAAO,CAAC,mDAAmD,CAAC;QAClEC,MAAM,EAAE,MAAM;QACdC,MAAM,EAAE,GAAG;QACXC,WAAW,EAAE;UAAEhB,KAAK,EAAE;QAAU,CAAC;QACjCiB,MAAM,EAAE,MAAM;QACdC,MAAM,EAAE,IAAI;QACZC,WAAW,EAAE;UAAEnB,KAAK,EAAE;QAAU;MAClC,CAAC,EACD;QACEW,SAAS,EAAE,eAAe;QAC1BC,IAAI,EAAEC,OAAO,CAAC,mDAAmD,CAAC;QAClEC,MAAM,EAAE,MAAM;QACdC,MAAM,EAAE,GAAG;QACXC,WAAW,EAAE;UAAEhB,KAAK,EAAE;QAAU,CAAC;QACjCiB,MAAM,EAAE,MAAM;QACdC,MAAM,EAAE,IAAI;QACZC,WAAW,EAAE;UAAEnB,KAAK,EAAE;QAAU;MAClC,EACD;MACDoB,eAAe,EAAE,CACf;QACEb,EAAE,EAAEM,OAAO,CAAC,qDAAqD,CAAC;QAClEQ,MAAM,EAAE,IAAI;QACZC,IAAI,EAAE,GAAG;QACTvB,KAAK,EAAE,MAAM;QACbwB,IAAI,EAAE;MACR,CAAC,EACD;QACEhB,EAAE,EAAEM,OAAO,CAAC,yDAAyD,CAAC;QACtEQ,MAAM,EAAE,OAAO;QACfC,IAAI,EAAE,GAAG;QACTvB,KAAK,EAAE,QAAQ;QACfwB,IAAI,EAAE;MACR,CAAC,EACD;QACEhB,EAAE,EAAEM,OAAO,CAAC,kDAAkD,CAAC;QAC/DQ,MAAM,EAAE,OAAO;QACfC,IAAI,EAAE,GAAG;QACTvB,KAAK,EAAE,QAAQ;QACfwB,IAAI,EAAE;MACR,EACD;MACDC,SAAS,EAAE,CACT;QAAErB,KAAK,EAAE;MAAe,CAAC,EACzB;QAAEA,KAAK,EAAE;MAAqB,CAAC,EAC/B;QAAEA,KAAK,EAAE;MAAiB,EAC3B;MACDsB,qBAAqB,EAAE,CACrB;QAAEjC,IAAI,EAAE,KAAK;QAAEkC,OAAO,EAAE,GAAG;QAAEC,QAAQ,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAC,EACvD;QAAEpC,IAAI,EAAE,IAAI;QAAEkC,OAAO,EAAE,GAAG;QAAEC,QAAQ,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAG,CAAC,EACtD;QAAEpC,IAAI,EAAE,KAAK;QAAEkC,OAAO,EAAE,GAAG;QAAEC,QAAQ,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAC,EACvD;QAAEpC,IAAI,EAAE,KAAK;QAAEkC,OAAO,EAAE,GAAG;QAAEC,QAAQ,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAC,EACtD;QAAEpC,IAAI,EAAE,IAAI;QAAEkC,OAAO,EAAE,GAAG;QAAEC,QAAQ,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAC,EACrD;QAAEpC,IAAI,EAAE,KAAK;QAAEkC,OAAO,EAAE,GAAG;QAAEC,QAAQ,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAC,EACtD;QAAEpC,IAAI,EAAE,IAAI;QAAEkC,OAAO,EAAE,GAAG;QAAEC,QAAQ,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAC,EACtD;QAAEpC,IAAI,EAAE,KAAK;QAAEkC,OAAO,EAAE,GAAG;QAAEC,QAAQ,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAG,CAAC,EACxD;QAAEpC,IAAI,EAAE,KAAK;QAAEkC,OAAO,EAAE,GAAG;QAAEC,QAAQ,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAG,CAAC,EACxD;QAAEpC,IAAI,EAAE,IAAI;QAAEkC,OAAO,EAAE,GAAG;QAAEC,QAAQ,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAG;IAEzD,CAAC;IACD,OAAO;MAAE,GAAG7C,MAAM,CAACO,IAAI;IAAE;EAC3B;AACF", "ignoreList": []}]}