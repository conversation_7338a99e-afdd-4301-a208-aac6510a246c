{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\MemberPerformance.vue?vue&type=template&id=c197b776&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\MemberPerformance.vue", "mtime": 1753943981713}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_Fragment", "_renderList", "_ctx", "performanceList", "item", "index", "key", "style", "_normalizeStyle", "background", "bgColor", "src", "icon", "alt", "label", "_hoisted_6", "_hoisted_7", "_toDisplayString", "color", "valueColor", "value", "_hoisted_8", "_hoisted_9", "_createVNode", "_component_<PERSON><PERSON><PERSON>", "id", "meetingTypeList", "radius", "center"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\MemberPerformance.vue"], "sourcesContent": ["<template>\r\n  <div class=\"MemberPerformance\">\r\n    <!-- 年度履职汇总 -->\r\n    <div class=\"performanceunit_summary_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">年度履职汇总</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"performanceunit_summary\">\r\n        <div class=\"performance_grid\">\r\n          <div v-for=\"(item, index) in performanceList\" :key=\"index\" class=\"performance_card\"\r\n            :style=\"{ background: item.bgColor }\">\r\n            <img :src=\"item.icon\" :alt=\"item.label\" class=\"icon_img\" />\r\n            <div class=\"card_content\">\r\n              <div class=\"card_label\">{{ item.label }}</div>\r\n              <div class=\"card_value\" :style=\"{ color: item.valueColor }\">{{ item.value }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 会议类型统计 -->\r\n    <div class=\"meeting_type_statistics_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">会议类型统计</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"meeting_type_statistics\">\r\n        <PieChart id=\"meetingType\" :chart-data=\"meetingTypeList\" :radius=\"['35%', '60%']\" :center=\"['50%', '30%']\" />\r\n      </div>\r\n    </div>\r\n    <!-- 活动类型统计 -->\r\n    <div class=\"activity_type_statistics_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">活动类型统计</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"activity_type_statistics\">\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { toRefs, reactive } from 'vue'\r\nimport PieChart from './echartsComponent/PieChart.vue'\r\nexport default {\r\n  name: 'MemberPerformance',\r\n  components: { PieChart },\r\n  setup () {\r\n    const data = reactive({\r\n      // 年度履职汇总数据\r\n      performanceList: [\r\n        {\r\n          label: '提交提案',\r\n          value: 1500,\r\n          icon: require('../../../assets/img/largeScreen/icon_proposal.png'),\r\n          bgColor: '#F1F5FF',\r\n          valueColor: '#3A61CD'\r\n        },\r\n        {\r\n          label: '提交社情民意',\r\n          value: 1057,\r\n          icon: require('../../../assets/img/largeScreen/icon_opinion.png'),\r\n          bgColor: '#DAF6F2',\r\n          valueColor: '#57BCAA'\r\n        },\r\n        {\r\n          label: '网络议政',\r\n          value: 215,\r\n          icon: require('../../../assets/img/largeScreen/icon_network.png'),\r\n          bgColor: '#E8F7FF',\r\n          valueColor: '#308FFF'\r\n        },\r\n        {\r\n          label: '参加会议',\r\n          value: 361,\r\n          icon: require('../../../assets/img/largeScreen/icon_meeting.png'),\r\n          bgColor: '#FDF8F0',\r\n          valueColor: '#EAB308'\r\n        },\r\n        {\r\n          label: '参加活动',\r\n          value: 104,\r\n          icon: require('../../../assets/img/largeScreen/icon_activity.png'),\r\n          bgColor: '#FDEFEF',\r\n          valueColor: '#FD7575'\r\n        },\r\n        {\r\n          label: '其他履职',\r\n          value: 241,\r\n          icon: require('../../../assets/img/largeScreen/icon_other.png'),\r\n          bgColor: '#E5F8FF',\r\n          valueColor: '#1FC6FF'\r\n        }\r\n      ],\r\n      meetingTypeList: [\r\n        { name: '其他会议', value: 28, percentage: '', color: '#4488EB' },\r\n        { name: '主席会议', value: 12, percentage: '', color: '#43DDBB' },\r\n        { name: '常委会议', value: 10, percentage: '', color: '#FF6665' },\r\n        { name: '全体会议', value: 2, percentage: '', color: '#ECE522' }\r\n      ]\r\n    })\r\n    return {\r\n      ...toRefs(data)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.MemberPerformance {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .header_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 15px 0 15px;\r\n\r\n    .header_left {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_left_line {\r\n        width: 3px;\r\n        height: 14px;\r\n        background: #007AFF;\r\n      }\r\n\r\n      .header_left_title {\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #222222;\r\n        margin-left: 8px;\r\n        font-family: Source Han Serif SC, Source Han Serif SC;\r\n      }\r\n    }\r\n\r\n    .header_right {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_right_text {\r\n        font-weight: 400;\r\n        font-size: 12px;\r\n        color: #999999;\r\n      }\r\n\r\n      .header_right_more {\r\n        font-size: 14px;\r\n        color: #0271E3;\r\n        border-radius: 14px;\r\n        border: 1px solid #0271E3;\r\n        padding: 3px 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .performanceunit_summary_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .performanceunit_summary {\r\n      padding: 12px;\r\n\r\n      .performance_grid {\r\n        display: grid;\r\n        grid-template-columns: 1fr 1fr;\r\n        gap: 10px;\r\n\r\n        .performance_card {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 16px 20px;\r\n          border-radius: 4px;\r\n\r\n          .icon_img {\r\n            width: 32px;\r\n            height: 32px;\r\n            object-fit: contain;\r\n            margin-right: 14px;\r\n          }\r\n\r\n          .card_content {\r\n            flex: 1;\r\n\r\n            .card_label {\r\n              font-size: 12px;\r\n              color: #999;\r\n              margin-bottom: 7px;\r\n            }\r\n\r\n            .card_value {\r\n              font-size: 20px;\r\n              color: #3A61CD;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .meeting_type_statistics_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .meeting_type_statistics {\r\n      width: 100%;\r\n      height: 260px;\r\n      margin-top: 20px;\r\n    }\r\n  }\r\n\r\n  .activity_type_statistics_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .activity_type_statistics {\r\n      padding: 15px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAA6B;;EAOjCA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAAkB;;;EAIpBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAY;;EAQ5BA,KAAK,EAAC;AAA6B;;EAOjCA,KAAK,EAAC;AAAyB;;;uBA9BxCC,mBAAA,CA6CM,OA7CNC,UA6CM,GA5CJC,mBAAA,YAAe,EACfC,mBAAA,CAmBM,OAnBNC,UAmBM,G,0BAlBJD,mBAAA,CAKM;IALDJ,KAAK,EAAC;EAAY,IACrBI,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAAsC;IAAhCJ,KAAK,EAAC;EAAkB,IAC9BI,mBAAA,CAA6C;IAAvCJ,KAAK,EAAC;EAAmB,GAAC,QAAM,E,wBAG1CI,mBAAA,CAWM,OAXNE,UAWM,GAVJF,mBAAA,CASM,OATNG,UASM,I,kBARJN,mBAAA,CAOMO,SAAA,QAAAC,WAAA,CAPuBC,IAAA,CAAAC,eAAe,GAA/BC,IAAI,EAAEC,KAAK;yBAAxBZ,mBAAA,CAOM;MAPyCa,GAAG,EAAED,KAAK;MAAEb,KAAK,EAAC,kBAAkB;MAChFe,KAAK,EAAAC,eAAA;QAAAC,UAAA,EAAgBL,IAAI,CAACM;MAAO;QAClCd,mBAAA,CAA2D;MAArDe,GAAG,EAAEP,IAAI,CAACQ,IAAI;MAAGC,GAAG,EAAET,IAAI,CAACU,KAAK;MAAEtB,KAAK,EAAC;yCAC9CI,mBAAA,CAGM,OAHNmB,UAGM,GAFJnB,mBAAA,CAA8C,OAA9CoB,UAA8C,EAAAC,gBAAA,CAAnBb,IAAI,CAACU,KAAK,kBACrClB,mBAAA,CAAkF;MAA7EJ,KAAK,EAAC,YAAY;MAAEe,KAAK,EAAAC,eAAA;QAAAU,KAAA,EAAWd,IAAI,CAACe;MAAU;wBAAOf,IAAI,CAACgB,KAAK,wB;wCAMnFzB,mBAAA,YAAe,EACfC,mBAAA,CAUM,OAVNyB,UAUM,G,0BATJzB,mBAAA,CAKM;IALDJ,KAAK,EAAC;EAAY,IACrBI,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAAsC;IAAhCJ,KAAK,EAAC;EAAkB,IAC9BI,mBAAA,CAA6C;IAAvCJ,KAAK,EAAC;EAAmB,GAAC,QAAM,E,wBAG1CI,mBAAA,CAEM,OAFN0B,UAEM,GADJC,YAAA,CAA6GC,mBAAA;IAAnGC,EAAE,EAAC,aAAa;IAAE,YAAU,EAAEvB,IAAA,CAAAwB,eAAe;IAAGC,MAAM,EAAE,cAAc;IAAGC,MAAM,EAAE;+CAG/FjC,mBAAA,YAAe,E", "ignoreList": []}]}