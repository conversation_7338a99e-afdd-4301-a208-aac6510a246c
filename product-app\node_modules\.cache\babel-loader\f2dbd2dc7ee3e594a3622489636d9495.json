{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\MemberPerformance.vue?vue&type=template&id=c197b776&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\MemberPerformance.vue", "mtime": 1753948038849}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_Fragment", "_renderList", "_ctx", "performanceList", "item", "index", "key", "style", "_normalizeStyle", "background", "bgColor", "src", "icon", "alt", "label", "_hoisted_6", "_hoisted_7", "_toDisplayString", "color", "valueColor", "value", "_hoisted_8", "_hoisted_9", "_createVNode", "_component_<PERSON><PERSON><PERSON>", "id", "meetingTypeList", "radius", "center", "_hoisted_10", "_hoisted_11", "_component_ActivityTypeChart", "data", "activityTypeList"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\MemberPerformance.vue"], "sourcesContent": ["<template>\r\n  <div class=\"MemberPerformance\">\r\n    <!-- 年度履职汇总 -->\r\n    <div class=\"performanceunit_summary_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">年度履职汇总</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"performanceunit_summary\">\r\n        <div class=\"performance_grid\">\r\n          <div v-for=\"(item, index) in performanceList\" :key=\"index\" class=\"performance_card\"\r\n            :style=\"{ background: item.bgColor }\">\r\n            <img :src=\"item.icon\" :alt=\"item.label\" class=\"icon_img\" />\r\n            <div class=\"card_content\">\r\n              <div class=\"card_label\">{{ item.label }}</div>\r\n              <div class=\"card_value\" :style=\"{ color: item.valueColor }\">{{ item.value }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 会议类型统计 -->\r\n    <div class=\"meeting_type_statistics_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">会议类型统计</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"meeting_type_statistics\">\r\n        <PieChart id=\"meetingType\" :chart-data=\"meetingTypeList\" :radius=\"['35%', '60%']\" :center=\"['50%', '30%']\" />\r\n      </div>\r\n    </div>\r\n    <!-- 活动类型统计 -->\r\n    <div class=\"activity_type_statistics_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">活动类型统计</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"activity_type_statistics\">\r\n        <ActivityTypeChart id=\"memberPerformance\" :data=\"activityTypeList\" style=\"height: 500px;\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { toRefs, reactive } from 'vue'\r\nimport PieChart from './echartsComponent/PieChart.vue'\r\nimport ActivityTypeChart from './echartsComponent/ActivityTypeChart.vue'\r\nexport default {\r\n  name: 'MemberPerformance',\r\n  components: { PieChart, ActivityTypeChart },\r\n  setup () {\r\n    const data = reactive({\r\n      // 年度履职汇总数据\r\n      performanceList: [\r\n        {\r\n          label: '提交提案',\r\n          value: 1500,\r\n          icon: require('../../../assets/img/largeScreen/icon_proposal.png'),\r\n          bgColor: '#F1F5FF',\r\n          valueColor: '#3A61CD'\r\n        },\r\n        {\r\n          label: '提交社情民意',\r\n          value: 1057,\r\n          icon: require('../../../assets/img/largeScreen/icon_opinion.png'),\r\n          bgColor: '#DAF6F2',\r\n          valueColor: '#57BCAA'\r\n        },\r\n        {\r\n          label: '网络议政',\r\n          value: 215,\r\n          icon: require('../../../assets/img/largeScreen/icon_network.png'),\r\n          bgColor: '#E8F7FF',\r\n          valueColor: '#308FFF'\r\n        },\r\n        {\r\n          label: '参加会议',\r\n          value: 361,\r\n          icon: require('../../../assets/img/largeScreen/icon_meeting.png'),\r\n          bgColor: '#FDF8F0',\r\n          valueColor: '#EAB308'\r\n        },\r\n        {\r\n          label: '参加活动',\r\n          value: 104,\r\n          icon: require('../../../assets/img/largeScreen/icon_activity.png'),\r\n          bgColor: '#FDEFEF',\r\n          valueColor: '#FD7575'\r\n        },\r\n        {\r\n          label: '其他履职',\r\n          value: 241,\r\n          icon: require('../../../assets/img/largeScreen/icon_other.png'),\r\n          bgColor: '#E5F8FF',\r\n          valueColor: '#1FC6FF'\r\n        }\r\n      ],\r\n      meetingTypeList: [\r\n        { name: '其他会议', value: 28, percentage: '', color: '#4488EB' },\r\n        { name: '主席会议', value: 12, percentage: '', color: '#43DDBB' },\r\n        { name: '常委会议', value: 10, percentage: '', color: '#FF6665' },\r\n        { name: '全体会议', value: 2, percentage: '', color: '#ECE522' }\r\n      ],\r\n      // 活动类型统计数据\r\n      activityTypeList: [\r\n        { name: '视察', value: 32 },\r\n        { name: '调研', value: 20 },\r\n        { name: '协商', value: 14 },\r\n        { name: '学习培训', value: 22 },\r\n        { name: '联系界别群众', value: 8 },\r\n        { name: '提案审查', value: 25 },\r\n        { name: '提案答办', value: 13 },\r\n        { name: '提案评议', value: 32 },\r\n        { name: '委员联络小组', value: 15 },\r\n        { name: '委员会客厅', value: 25 },\r\n        { name: '联系社会组织', value: 10 },\r\n        { name: '界别群众重点关切问题情况通报会', value: 20 },\r\n        { name: '社情民意座谈会', value: 16 },\r\n        { name: '接受媒体采访', value: 28 },\r\n        { name: '经政协推荐参加有关会议活动', value: 5 },\r\n        { name: '宣讲党的政策', value: 7 },\r\n        { name: '服务为民', value: 32 }\r\n      ]\r\n    })\r\n    return {\r\n      ...toRefs(data)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.MemberPerformance {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .header_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 15px 0 15px;\r\n\r\n    .header_left {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_left_line {\r\n        width: 3px;\r\n        height: 14px;\r\n        background: #007AFF;\r\n      }\r\n\r\n      .header_left_title {\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #222222;\r\n        margin-left: 8px;\r\n        font-family: Source Han Serif SC, Source Han Serif SC;\r\n      }\r\n    }\r\n\r\n    .header_right {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_right_text {\r\n        font-weight: 400;\r\n        font-size: 12px;\r\n        color: #999999;\r\n      }\r\n\r\n      .header_right_more {\r\n        font-size: 14px;\r\n        color: #0271E3;\r\n        border-radius: 14px;\r\n        border: 1px solid #0271E3;\r\n        padding: 3px 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .performanceunit_summary_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .performanceunit_summary {\r\n      padding: 12px;\r\n\r\n      .performance_grid {\r\n        display: grid;\r\n        grid-template-columns: 1fr 1fr;\r\n        gap: 10px;\r\n\r\n        .performance_card {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 16px 20px;\r\n          border-radius: 4px;\r\n\r\n          .icon_img {\r\n            width: 32px;\r\n            height: 32px;\r\n            object-fit: contain;\r\n            margin-right: 14px;\r\n          }\r\n\r\n          .card_content {\r\n            flex: 1;\r\n\r\n            .card_label {\r\n              font-size: 12px;\r\n              color: #999;\r\n              margin-bottom: 7px;\r\n            }\r\n\r\n            .card_value {\r\n              font-size: 20px;\r\n              color: #3A61CD;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .meeting_type_statistics_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .meeting_type_statistics {\r\n      width: 100%;\r\n      height: 260px;\r\n      margin-top: 20px;\r\n    }\r\n  }\r\n\r\n  .activity_type_statistics_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .activity_type_statistics {\r\n      padding: 15px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAA6B;;EAOjCA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAAkB;;;EAIpBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAY;;EAQ5BA,KAAK,EAAC;AAA6B;;EAOjCA,KAAK,EAAC;AAAyB;;EAKjCA,KAAK,EAAC;AAA8B;;EAOlCA,KAAK,EAAC;AAA0B;;;;uBA1CzCC,mBAAA,CA8CM,OA9CNC,UA8CM,GA7CJC,mBAAA,YAAe,EACfC,mBAAA,CAmBM,OAnBNC,UAmBM,G,0BAlBJD,mBAAA,CAKM;IALDJ,KAAK,EAAC;EAAY,IACrBI,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAAsC;IAAhCJ,KAAK,EAAC;EAAkB,IAC9BI,mBAAA,CAA6C;IAAvCJ,KAAK,EAAC;EAAmB,GAAC,QAAM,E,wBAG1CI,mBAAA,CAWM,OAXNE,UAWM,GAVJF,mBAAA,CASM,OATNG,UASM,I,kBARJN,mBAAA,CAOMO,SAAA,QAAAC,WAAA,CAPuBC,IAAA,CAAAC,eAAe,GAA/BC,IAAI,EAAEC,KAAK;yBAAxBZ,mBAAA,CAOM;MAPyCa,GAAG,EAAED,KAAK;MAAEb,KAAK,EAAC,kBAAkB;MAChFe,KAAK,EAAAC,eAAA;QAAAC,UAAA,EAAgBL,IAAI,CAACM;MAAO;QAClCd,mBAAA,CAA2D;MAArDe,GAAG,EAAEP,IAAI,CAACQ,IAAI;MAAGC,GAAG,EAAET,IAAI,CAACU,KAAK;MAAEtB,KAAK,EAAC;yCAC9CI,mBAAA,CAGM,OAHNmB,UAGM,GAFJnB,mBAAA,CAA8C,OAA9CoB,UAA8C,EAAAC,gBAAA,CAAnBb,IAAI,CAACU,KAAK,kBACrClB,mBAAA,CAAkF;MAA7EJ,KAAK,EAAC,YAAY;MAAEe,KAAK,EAAAC,eAAA;QAAAU,KAAA,EAAWd,IAAI,CAACe;MAAU;wBAAOf,IAAI,CAACgB,KAAK,wB;wCAMnFzB,mBAAA,YAAe,EACfC,mBAAA,CAUM,OAVNyB,UAUM,G,0BATJzB,mBAAA,CAKM;IALDJ,KAAK,EAAC;EAAY,IACrBI,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAAsC;IAAhCJ,KAAK,EAAC;EAAkB,IAC9BI,mBAAA,CAA6C;IAAvCJ,KAAK,EAAC;EAAmB,GAAC,QAAM,E,wBAG1CI,mBAAA,CAEM,OAFN0B,UAEM,GADJC,YAAA,CAA6GC,mBAAA;IAAnGC,EAAE,EAAC,aAAa;IAAE,YAAU,EAAEvB,IAAA,CAAAwB,eAAe;IAAGC,MAAM,EAAE,cAAc;IAAGC,MAAM,EAAE;+CAG/FjC,mBAAA,YAAe,EACfC,mBAAA,CAUM,OAVNiC,WAUM,G,0BATJjC,mBAAA,CAKM;IALDJ,KAAK,EAAC;EAAY,IACrBI,mBAAA,CAGM;IAHDJ,KAAK,EAAC;EAAa,IACtBI,mBAAA,CAAsC;IAAhCJ,KAAK,EAAC;EAAkB,IAC9BI,mBAAA,CAA6C;IAAvCJ,KAAK,EAAC;EAAmB,GAAC,QAAM,E,wBAG1CI,mBAAA,CAEM,OAFNkC,WAEM,GADJP,YAAA,CAA4FQ,4BAAA;IAAzEN,EAAE,EAAC,mBAAmB;IAAEO,IAAI,EAAE9B,IAAA,CAAA+B,gBAAgB;IAAE1B,KAAsB,EAAtB;MAAA;IAAA", "ignoreList": []}]}