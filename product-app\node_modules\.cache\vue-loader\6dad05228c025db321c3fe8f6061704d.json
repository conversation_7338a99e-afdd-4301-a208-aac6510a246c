{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\Home.vue?vue&type=template&id=10233720&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\Home.vue", "mtime": 1753932846834}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\Home.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAClC,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/E,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACpF,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChF,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAC5E,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClF,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7F,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACrD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5F,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;UAC/C,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7G,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3E,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3C,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/zy/xm/h5/qdzx_h5/product-app/src/views/SmartBrainLargeScreen/component/Home.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"home_page\">\n    <div class=\"map_section\">\n      <MapQingdao :mapData=\"mapData\" />\n    </div>\n    <!-- 委员统计 -->\n    <div class=\"home_committee_statistics\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">委员统计</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">{{ circles }}</span>\n        </div>\n      </div>\n      <div class=\"committee_statistics_box\">\n        <div class=\"statistics_card card_blue\">\n          <div class=\"card_content\">\n            <div class=\"card_number\">{{ cppccMemberNum }}</div>\n            <div class=\"card_label\">政协委员(人)</div>\n          </div>\n        </div>\n        <div class=\"statistics_card card_yellow\">\n          <div class=\"card_content\">\n            <div class=\"card_number\">{{ standingCommitteeNum }}</div>\n            <div class=\"card_label\">政协常委(人)</div>\n          </div>\n        </div>\n      </div>\n      <div class=\"circles_box\">\n        <div class=\"circles_top_header\">\n          <span class=\"circles_top_header_title\">界别分布</span>\n          <span class=\"circles_top_header_more\" style=\"\">查看全部</span>\n        </div>\n        <horizontalBarEcharts id=\"circles\" :barList=\"barList\" colorStart=\"#FFFFFF\" colorEnd=\"#EF817C\"\n          style=\"height: 260px;\" />\n      </div>\n    </div>\n    <!-- 提案统计 -->\n    <div class=\"home_proposal_statistics\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">提案统计</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">{{ circles }}</span>\n        </div>\n      </div>\n      <div class=\"proposal_statistics_box\">\n        <div class=\"proposal_card\" v-for=\"(item, index) in proposalStatsNum\" :key=\"index\">\n          <div class=\"proposal_number\" :style=\"{ color: item.color }\">{{ item.value }}</div>\n          <div class=\"proposal_label\">{{ item.label }}</div>\n        </div>\n      </div>\n      <div class=\"proposal_type_analysis\">\n        <pieEchartsLegend id=\"typeAnalysisPie\" :dataList=\"typeAnalysisList\" title=\"类型分析\" />\n      </div>\n    </div>\n    <!-- 工作动态 -->\n    <div class=\"home_work_dynamics\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">工作动态</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">本年</span>\n        </div>\n      </div>\n      <div class=\"work_dynamics_list\">\n        <div class=\"work_dynamics_item\" v-for=\"(item, idx) in workDynamicsList\" :key=\"idx\">\n          <div class=\"work_dynamics_title\">{{ item.title }}</div>\n          <div class=\"work_dynamics_date\">{{ item.date }}</div>\n        </div>\n      </div>\n    </div>\n    <!-- 社情民意 -->\n    <div class=\"home_social\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">社情民意</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">本年</span>\n        </div>\n      </div>\n      <div class=\"social_box\">\n        <!-- 总数卡片 -->\n        <div class=\"social_card total_card\" :style=\"{ background: socialStats[0].bg }\">\n          <div class=\"total_card_number\" :style=\"{ color: socialStats[0].color }\">{{ socialStats[0].total }}</div>\n          <div class=\"total_card_label\">{{ socialStats[0].label }}</div>\n        </div>\n        <!-- 报送卡片 -->\n        <div class=\"social_card report_card\" v-for=\"(item, idx) in socialStats.slice(1)\" :key=\"idx\"\n          :style=\"{ background: item.bg }\">\n          <div class=\"report_row\">\n            <span class=\"report_label\">总数</span>\n            <span class=\"report_total\" :style=\"{ color: item.color }\">{{ item.total }}</span>\n          </div>\n          <div class=\"report_row\">\n            <span class=\"report_label\">采用</span>\n            <span class=\"report_adopted\" :style=\"{ color: item.adoptedColor }\">{{ item.adopted }}</span>\n          </div>\n          <div class=\"report_card_label\">{{ item.label }}</div>\n        </div>\n      </div>\n    </div>\n    <!-- 会议活动 -->\n    <div class=\"home_meetting_activity\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">会议活动</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">本年</span>\n        </div>\n      </div>\n      <div class=\"meetting_activity_box\">\n        <div v-for=\"(item, idx) in meettingActivityList\" :key=\"idx\" class=\"meetting_activity_card\"\n          :class=\"item.cardClass\">\n          <img :src=\"item.icon\" class=\"activity_card_iconimg\" />\n          <div class=\"meetting_activity_card_content\">\n            <div class=\"meetting_activity_card_label\">{{ item.label1 }}</div>\n            <div class=\"meetting_activity_card_value\" :style=\"item.value1Style\">{{ item.value1 }}</div>\n            <div class=\"meetting_activity_card_label\">{{ item.label2 }}</div>\n            <div class=\"meetting_activity_card_value\" :style=\"item.value2Style\">{{ item.value2 }}</div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <!-- 网络议政 -->\n    <div class=\"home_discussions\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">网络议政</span>\n        </div>\n      </div>\n      <div class=\"discussions_box\">\n        <div class=\"discussion_card\" v-for=\"(item, idx) in discussionsList\" :key=\"idx\"\n          :style=\"{ backgroundImage: `url(${item.bg})` }\">\n          <div class=\"discussion_card_number\">{{ item.number }}<span class=\"discussion_card_unit\">{{ item.unit }}</span>\n          </div>\n          <div class=\"discussion_card_label\">{{ item.label }}</div>\n        </div>\n      </div>\n      <div class=\"hot_topics\">\n        <div class=\"hot_topics_title\">最热话题</div>\n        <div class=\"hot_topics_list\">\n          <div class=\"hot_topic_item\" v-for=\"(topic, idx) in hotTopics\" :key=\"idx\">\n            <span class=\"hot_topic_index\" :class=\"'hot_topic_index_' + (idx + 1)\">{{ idx + 1 }}</span>\n            <span class=\"hot_topic_text\">{{ topic.title }}</span>\n            <span class=\"hot_topic_tag\" :class=\"'hot_topic_tag_' + (idx + 1)\">热</span>\n          </div>\n        </div>\n      </div>\n    </div>\n    <!-- 履职统计 -->\n    <div class=\"home_performance_statistics\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">履职统计</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">{{ circles }}</span>\n        </div>\n      </div>\n      <div class=\"performance_statistics_box\">\n        <div class=\"performance_table\">\n          <div class=\"performance_table_header\">\n            <span>姓名</span>\n            <span>会议活动</span>\n            <span>政协提案</span>\n            <span>社情民意</span>\n          </div>\n          <div class=\"performance_table_row\" v-for=\"(item, idx) in performanceStatistics\" :key=\"item.name\"\n            :class=\"{ 'row-alt': idx % 2 === 1 }\">\n            <span>{{ item.name }}</span>\n            <span>{{ item.meeting }}</span>\n            <span>{{ item.proposal }}</span>\n            <span>{{ item.opinion }}</span>\n          </div>\n          <div class=\"performance_table_footer\">\n            <button class=\"view-all-btn\">查看全部</button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n<script>\nimport { toRefs, reactive } from 'vue'\nimport MapQingdao from './echartsComponent/MapQingdao.vue'\nimport horizontalBarEcharts from './echartsComponent/horizontalBarEcharts.vue'\nimport pieEchartsLegend from './echartsComponent/pieEchartsLegend.vue'\n\nexport default {\n  components: { MapQingdao, horizontalBarEcharts, pieEchartsLegend },\n  setup () {\n    const data = reactive({\n      mapData: [\n        { name: '市南区', value: 80 },\n        { name: '市北区', value: 60 },\n        { name: '李沧区', value: 50 },\n        { name: '崂山区', value: 40 },\n        { name: '城阳区', value: 70 },\n        { name: '黄岛区', value: 90 },\n        { name: '即墨区', value: 30 },\n        { name: '胶州市', value: 55 },\n        { name: '平度市', value: 20 },\n        { name: '莱西市', value: 10 }\n      ],\n      circles: '十一届二次',\n      cppccMemberNum: '10095',\n      standingCommitteeNum: '8742',\n      barList: [\n        { name: '教育界', value: 35 },\n        { name: '医药卫生界', value: 15 },\n        { name: '经济界', value: 14 },\n        { name: '工商联界', value: 21 },\n        { name: '民革界', value: 15 },\n        { name: '特邀界', value: 21 },\n        { name: '妇联界', value: 8 },\n        { name: '工会界', value: 8 },\n        { name: '社会福利与社会保障界', value: 14 }\n      ],\n      proposalStatsNum: [\n        { value: 873, label: '提案总数', color: '#2386F9' },\n        { value: 456, label: '委员提案', color: '#2CA6F9' },\n        { value: 354, label: '界别提案', color: '#3AC86B' },\n        { value: 221, label: '组织提案', color: '#F96C9C' }\n      ],\n      typeAnalysisList: [\n        { name: '发改财政', value: 22.52, color: '#3DC3F0' },\n        { name: '民政市场', value: 18.33, color: '#4AC6A8' },\n        { name: '公安司法', value: 12.5, color: '#F9C846' },\n        { name: '区市政府', value: 11.34, color: '#6DD3A0' },\n        { name: '科技工信', value: 9.56, color: '#7B8DF9' },\n        { name: '教育文化', value: 8.09, color: '#F97C9C' },\n        { name: '派出机构', value: 4.21, color: '#F9A846' },\n        { name: '驻青单位', value: 3.71, color: '#F97C46' },\n        { name: '住建交通', value: 3.65, color: '#A97CF9' },\n        { name: '农村卫生', value: 3.21, color: '#4A9CF9' },\n        { name: '其他机构', value: 1.86, color: '#BFBFBF' },\n        { name: '党群其他', value: 1.02, color: '#F9C8C8' }\n      ],\n      workDynamicsList: [\n        { title: '市政协社会和法制工作办公室围绕市政协社会和法制工作办公室围绕', date: '2025-06-03' },\n        { title: '“与民同行 共创共赢”新格局下民市政协社会和法制工作办公室围绕', date: '2025-05-30' },\n        { title: '“惠民生·基层行”义诊活动温暖人心市政协社会和法制工作办公室围绕', date: '2025-05-30' },\n        { title: '市科技局面复市政协科技界别提案市政协社会和法制工作办公室围绕', date: '2025-05-30' },\n        { title: '孟庆斌到胶州市、崂山区调研项目时市政协社会和法制工作办公室围绕', date: '2025-05-29' }\n      ],\n      socialStats: [\n        { total: 1057, label: '总数', bg: '#EFF6FF', color: '#2386F9' },\n        { total: 345, adopted: 21, label: '委员报送', bg: '#FDF8F0', color: '#2386F9', adoptedColor: '#F9C846' },\n        { total: 547, adopted: 79, label: '单位报送', bg: '#F0FDF4', color: '#3AC86B', adoptedColor: '#F9C846' }\n      ],\n      meettingActivityList: [\n        {\n          cardClass: 'card_meeting',\n          icon: require('../../../assets/img/largeScreen/icon_meetting.png'),\n          label1: '会议次数',\n          value1: 201,\n          value1Style: { color: '#308FFF' },\n          label2: '会议人数',\n          value2: 2412,\n          value2Style: { color: '#308FFF' }\n        },\n        {\n          cardClass: 'card_activity',\n          icon: require('../../../assets/img/largeScreen/icon_acticity.png'),\n          label1: '活动次数',\n          value1: 310,\n          value1Style: { color: '#1FC6FF' },\n          label2: '活动人数',\n          value2: 4015,\n          value2Style: { color: '#1FC6FF' }\n        }\n      ],\n      discussionsList: [\n        {\n          bg: require('../../../assets/img/largeScreen/icon_release_bg.png'),\n          number: '72',\n          unit: '个',\n          label: '发布议题',\n          desc: ''\n        },\n        {\n          bg: require('../../../assets/img/largeScreen/icon_participate_bg.png'),\n          number: '39301',\n          unit: '次',\n          label: '累计参与人次',\n          desc: ''\n        },\n        {\n          bg: require('../../../assets/img/largeScreen/icon_seek_bg.png'),\n          number: '12308',\n          unit: '条',\n          label: '累计征求意见',\n          desc: ''\n        }\n      ],\n      hotTopics: [\n        { title: '推进黄河国家文化公园建设' },\n        { title: '持续推进黄河流域生态保护修复，助力…' },\n        { title: '全面加强新时代中小学劳动教育' }\n      ],\n      performanceStatistics: [\n        { name: '马平安', meeting: 515, proposal: 15, opinion: 0 },\n        { name: '马波', meeting: 400, proposal: 0, opinion: 12 },\n        { name: '王玉民', meeting: 490, proposal: 15, opinion: 0 },\n        { name: '王洋宝', meeting: 500, proposal: 0, opinion: 1 },\n        { name: '王忠', meeting: 420, proposal: 0, opinion: 2 },\n        { name: '刘彩霞', meeting: 512, proposal: 0, opinion: 1 },\n        { name: '刘军', meeting: 500, proposal: 20, opinion: 0 },\n        { name: '吴雪玲', meeting: 315, proposal: 15, opinion: 38 },\n        { name: '杨文比', meeting: 310, proposal: 60, opinion: 28 },\n        { name: '贾谊', meeting: 540, proposal: 9, opinion: 13 }\n      ]\n    })\n    return { ...toRefs(data) }\n  }\n}\n</script>\n<style lang=\"less\" scoped>\n.home_page {\n  width: 100%;\n  height: 100%;\n\n  .header_box {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 15px 15px 0 15px;\n\n    .header_left {\n      display: flex;\n      align-items: center;\n\n      .header_left_line {\n        width: 3px;\n        height: 14px;\n        background: #007AFF;\n      }\n\n      .header_left_title {\n        font-weight: bold;\n        font-size: 15px;\n        color: #222222;\n        margin-left: 8px;\n        font-family: Source Han Serif SC, Source Han Serif SC;\n      }\n    }\n\n    .header_right {\n      display: flex;\n      align-items: center;\n\n      .header_right_text {\n        font-weight: 400;\n        font-size: 12px;\n        color: #999999;\n      }\n    }\n  }\n\n  .map_section {\n    background: #fff;\n    border-radius: 8px;\n    padding: 10px;\n  }\n\n  .home_committee_statistics {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .committee_statistics_box {\n      display: flex;\n      gap: 15px;\n      padding: 20px 15px 10px 15px;\n\n      .statistics_card {\n        flex: 1;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: #f5faff;\n        position: relative;\n        height: 86px;\n\n        .card_content {\n          display: flex;\n          flex-direction: column;\n          align-items: flex-start;\n          justify-content: center;\n          margin-left: 55px;\n          margin-top: 5px;\n\n          .card_number {\n            font-size: 20px;\n            color: #4AA3FF;\n          }\n\n          .card_label {\n            font-size: 14px;\n            color: #666;\n            margin-top: 2px;\n          }\n        }\n      }\n\n      .card_blue {\n        background-image: url('../../../assets/img/largeScreen/icon_member_bg.png');\n        background-size: 100% 100%;\n        background-position: center;\n      }\n\n      .card_yellow {\n        background-image: url('../../../assets/img/largeScreen/icon_committee_bg.png');\n        background-size: 100% 100%;\n        background-position: center;\n\n        .card_number {\n          color: #E6B800 !important;\n        }\n      }\n    }\n\n    .circles_box {\n      border-radius: 6px;\n      margin: 10px 12px;\n\n      .circles_top_header {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n\n        .circles_top_header_title {\n          font-size: 14px;\n          color: #000;\n          font-family: Source Han Serif SC, Source Han Serif SC;\n        }\n\n        .circles_top_header_more {\n          font-size: 14px;\n          color: #0271E3;\n          border-radius: 14px;\n          border: 1px solid #0271E3;\n          padding: 3px 10px;\n        }\n      }\n    }\n  }\n\n  .home_proposal_statistics {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .proposal_statistics_box {\n      display: flex;\n      justify-content: space-between;\n      padding: 20px 15px 10px 15px;\n\n      .proposal_card {\n        flex: 1;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n\n        .proposal_number {\n          font-size: 24px;\n        }\n\n        .proposal_label {\n          font-size: 14px;\n          color: #999;\n          margin-top: 4px;\n        }\n      }\n    }\n\n    .proposal_type_analysis {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n    }\n  }\n\n  .home_work_dynamics {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .work_dynamics_list {\n      padding: 8px 15px;\n      background: #fff;\n\n      .work_dynamics_item {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        padding: 12px 0;\n        border-bottom: 1px solid #f0f0f0;\n\n        &:last-child {\n          border-bottom: none;\n        }\n\n        .work_dynamics_title {\n          flex: 1;\n          font-size: 14px;\n          color: #666666;\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n        }\n\n        .work_dynamics_date {\n          font-size: 14px;\n          color: #bdbdbd;\n          flex-shrink: 0;\n        }\n      }\n    }\n  }\n\n  .home_social {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .social_box {\n      display: flex;\n      gap: 16px;\n      padding: 20px 15px;\n\n      .social_card {\n        flex: 1;\n        width: 97px;\n        height: 94px;\n        border-radius: 10px;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        box-sizing: border-box;\n        box-shadow: none;\n        padding: 15px;\n        background-clip: padding-box;\n      }\n\n      .total_card {\n        justify-content: center;\n\n        .total_card_number {\n          font-size: 20px;\n          color: #3B91FB;\n          margin-bottom: 5px;\n        }\n\n        .total_card_label {\n          font-size: 14px;\n          color: #666666;\n        }\n      }\n\n      .report_card {\n        justify-content: flex-start;\n\n        .report_row {\n          width: 100%;\n          display: flex;\n          justify-content: space-between;\n          align-items: baseline;\n          margin-bottom: 2px;\n\n          .report_label {\n            font-size: 14px;\n            color: #999;\n            margin-right: 2px;\n          }\n\n          .report_total {\n            font-size: 15px;\n          }\n\n          .report_adopted {\n            font-size: 15px;\n          }\n        }\n\n        .report_card_label {\n          margin-top: 5px;\n          font-size: 15px;\n          color: #666;\n          font-family: Source Han Serif SC, Source Han Serif SC;\n        }\n      }\n    }\n  }\n\n  .home_meetting_activity {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .meetting_activity_box {\n      display: flex;\n      gap: 16px;\n      padding: 20px 15px;\n\n      .meetting_activity_card {\n        flex: 1;\n        display: flex;\n        align-items: flex-start;\n        box-sizing: border-box;\n        width: 157px;\n        height: 140px;\n        padding: 14px 20px;\n\n        .activity_card_iconimg {\n          width: 32px;\n          height: 32px;\n          margin-right: 15px;\n          margin-top: 4px;\n        }\n\n        .meetting_activity_card_content {\n          display: flex;\n          flex-direction: column;\n          justify-content: center;\n          flex: 1;\n\n          .meetting_activity_card_label {\n            font-size: 14px;\n            color: #999;\n            margin-bottom: 5px;\n          }\n\n          .meetting_activity_card_value {\n            font-size: 20px;\n            color: #308FFF;\n            margin-bottom: 8px;\n          }\n        }\n      }\n\n      .card_meeting {\n        background-image: url('../../../assets/img/largeScreen/icon_meetting_bg.png');\n        background-size: 100% 100%;\n        background-position: center;\n      }\n\n      .card_activity {\n        background-image: url('../../../assets/img/largeScreen/icon_activity_bg.png');\n        background-size: 100% 100%;\n        background-position: center;\n      }\n    }\n  }\n\n  .home_discussions {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .discussions_box {\n      display: flex;\n      gap: 10px;\n      padding: 20px 15px;\n      justify-content: flex-start;\n\n      .discussion_card {\n        flex: 1;\n        width: 103px;\n        height: 77px;\n        background-size: 100% 100%;\n        background-position: center;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        color: #fff;\n\n        .discussion_card_number {\n          font-size: 20px;\n          margin-bottom: 2px;\n\n          .discussion_card_unit {\n            font-size: 12px;\n            font-weight: normal;\n            margin-left: 2px;\n          }\n        }\n\n        .discussion_card_label {\n          font-size: 14px;\n          font-weight: 400;\n          margin-bottom: 2px;\n        }\n      }\n    }\n\n    .hot_topics {\n      padding: 0 15px 15px 15px;\n\n      .hot_topics_title {\n        font-size: 14px;\n        color: #000;\n        margin-bottom: 10px;\n        font-family: Source Han Serif SC, Source Han Serif SC;\n      }\n\n      .hot_topics_list {\n        .hot_topic_item {\n          display: flex;\n          align-items: center;\n          border-bottom: 1px solid #f0f0f0;\n          padding: 12px 0;\n\n          .hot_topic_index {\n            font-size: 14px;\n            margin-right: 10px;\n          }\n\n          .hot_topic_index_1 {\n            color: #FF4D4F;\n          }\n\n          .hot_topic_index_2 {\n            color: #FF9900;\n          }\n\n          .hot_topic_index_3 {\n            color: #FFD600;\n          }\n\n          .hot_topic_text {\n            flex: 1;\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            font-size: 14px;\n            color: #666;\n          }\n\n          .hot_topic_tag {\n            font-size: 14px;\n            color: #fff;\n            border-radius: 2px;\n            width: 22px;\n            height: 22px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n          }\n\n          .hot_topic_tag_1 {\n            background: #FB3030;\n          }\n\n          .hot_topic_tag_2 {\n            background: #FF833E;\n          }\n\n          .hot_topic_tag_3 {\n            background: #FFD978;\n          }\n        }\n      }\n    }\n  }\n\n  .home_performance_statistics {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .performance_statistics_box {\n      margin-top: 15px;\n\n      .performance_table {\n        width: 100%;\n        background: #fff;\n\n        .performance_table_header,\n        .performance_table_row {\n          display: flex;\n          align-items: center;\n          padding: 8px 0;\n\n          span {\n            flex: 1;\n            text-align: center;\n          }\n        }\n\n        .performance_table_header {\n          background: #F1F8FF;\n          font-weight: bold;\n          color: #222;\n          font-size: 14px;\n        }\n\n        .performance_table_row {\n          background: #fff;\n          color: #222;\n          font-size: 14px;\n\n          &.row-alt {\n            background: #F1F8FF;\n          }\n        }\n\n        .performance_table_footer {\n          display: flex;\n          justify-content: center;\n          padding: 10px 0;\n          background: #fff;\n\n          .view-all-btn {\n            border: 1px solid #0271e3;\n            color: #0271e3;\n            background: #fff;\n            border-radius: 16px;\n            padding: 4px 14px;\n            font-size: 14px;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}