{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\MemberPerformance.vue?vue&type=style&index=0&id=c197b776&lang=less&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\MemberPerformance.vue", "mtime": 1753941208740}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouTWVtYmVyUGVyZm9ybWFuY2Ugew0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiAxMDAlOw0KDQogIC5oZWFkZXJfYm94IHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgIHBhZGRpbmc6IDE1cHggMTVweCAwIDE1cHg7DQoNCiAgICAuaGVhZGVyX2xlZnQgew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQoNCiAgICAgIC5oZWFkZXJfbGVmdF9saW5lIHsNCiAgICAgICAgd2lkdGg6IDNweDsNCiAgICAgICAgaGVpZ2h0OiAxNHB4Ow0KICAgICAgICBiYWNrZ3JvdW5kOiAjMDA3QUZGOw0KICAgICAgfQ0KDQogICAgICAuaGVhZGVyX2xlZnRfdGl0bGUgew0KICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgZm9udC1zaXplOiAxNXB4Ow0KICAgICAgICBjb2xvcjogIzIyMjIyMjsNCiAgICAgICAgbWFyZ2luLWxlZnQ6IDhweDsNCiAgICAgICAgZm9udC1mYW1pbHk6IFNvdXJjZSBIYW4gU2VyaWYgU0MsIFNvdXJjZSBIYW4gU2VyaWYgU0M7DQogICAgICB9DQogICAgfQ0KDQogICAgLmhlYWRlcl9yaWdodCB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCg0KICAgICAgLmhlYWRlcl9yaWdodF90ZXh0IHsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICBjb2xvcjogIzk5OTk5OTsNCiAgICAgIH0NCg0KICAgICAgLmhlYWRlcl9yaWdodF9tb3JlIHsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICBjb2xvcjogIzAyNzFFMzsNCiAgICAgICAgYm9yZGVyLXJhZGl1czogMTRweDsNCiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgIzAyNzFFMzsNCiAgICAgICAgcGFkZGluZzogM3B4IDEwcHg7DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLnBlcmZvcm1hbmNldW5pdF9zdW1tYXJ5X2JveCB7DQogICAgYmFja2dyb3VuZDogI0ZGRkZGRjsNCiAgICBib3JkZXItcmFkaXVzOiA2cHg7DQogICAgbWFyZ2luOiAxMnB4IDA7DQoNCiAgICAucGVyZm9ybWFuY2V1bml0X3N1bW1hcnkge30NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\MemberPerformance.vue"], "names": [], "mappings": ";AA8BA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACvD;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC5B;AACF", "file": "D:/zy/xm/h5/qdzx_h5/product-app/src/views/SmartBrainLargeScreen/component/MemberPerformance.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"MemberPerformance\">\r\n    <!-- 年度履职汇总 -->\r\n    <div class=\"performanceunit_summary_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">年度履职汇总</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"performanceunit_summary\">\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { toRefs, reactive } from 'vue'\r\nexport default {\r\n  name: 'MemberPerformance',\r\n  setup () {\r\n    const data = reactive({\r\n\r\n    })\r\n    return {\r\n      ...toRefs(data)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.MemberPerformance {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .header_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 15px 0 15px;\r\n\r\n    .header_left {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_left_line {\r\n        width: 3px;\r\n        height: 14px;\r\n        background: #007AFF;\r\n      }\r\n\r\n      .header_left_title {\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #222222;\r\n        margin-left: 8px;\r\n        font-family: Source Han Serif SC, Source Han Serif SC;\r\n      }\r\n    }\r\n\r\n    .header_right {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_right_text {\r\n        font-weight: 400;\r\n        font-size: 12px;\r\n        color: #999999;\r\n      }\r\n\r\n      .header_right_more {\r\n        font-size: 14px;\r\n        color: #0271E3;\r\n        border-radius: 14px;\r\n        border: 1px solid #0271E3;\r\n        padding: 3px 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .performanceunit_summary_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .performanceunit_summary {}\r\n  }\r\n}\r\n</style>\r\n"]}]}