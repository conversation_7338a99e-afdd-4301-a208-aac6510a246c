{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\MemberPerformance.vue?vue&type=style&index=0&id=c197b776&lang=less&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\MemberPerformance.vue", "mtime": 1753944413991}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\MemberPerformance.vue"], "names": [], "mappings": ";AAyIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACvD;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAET,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;UAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACpB;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;YAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACpB;;YAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB;UACF;QACF;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAClB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;EACF;AACF", "file": "D:/zy/xm/h5/qdzx_h5/product-app/src/views/SmartBrainLargeScreen/component/MemberPerformance.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"MemberPerformance\">\r\n    <!-- 年度履职汇总 -->\r\n    <div class=\"performanceunit_summary_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">年度履职汇总</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"performanceunit_summary\">\r\n        <div class=\"performance_grid\">\r\n          <div v-for=\"(item, index) in performanceList\" :key=\"index\" class=\"performance_card\"\r\n            :style=\"{ background: item.bgColor }\">\r\n            <img :src=\"item.icon\" :alt=\"item.label\" class=\"icon_img\" />\r\n            <div class=\"card_content\">\r\n              <div class=\"card_label\">{{ item.label }}</div>\r\n              <div class=\"card_value\" :style=\"{ color: item.valueColor }\">{{ item.value }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 会议类型统计 -->\r\n    <div class=\"meeting_type_statistics_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">会议类型统计</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"meeting_type_statistics\">\r\n        <PieChart id=\"meetingType\" :chart-data=\"meetingTypeList\" :radius=\"['35%', '60%']\" :center=\"['50%', '30%']\" />\r\n      </div>\r\n    </div>\r\n    <!-- 活动类型统计 -->\r\n    <div class=\"activity_type_statistics_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">活动类型统计</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"activity_type_statistics\">\r\n        <ActivityTypeChart :data=\"activityTypeList\" height=\"500px\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { toRefs, reactive } from 'vue'\r\nimport PieChart from './echartsComponent/PieChart.vue'\r\nimport ActivityTypeChart from './echartsComponent/ActivityTypeChart.vue'\r\nexport default {\r\n  name: 'MemberPerformance',\r\n  components: { PieChart, ActivityTypeChart },\r\n  setup () {\r\n    const data = reactive({\r\n      // 年度履职汇总数据\r\n      performanceList: [\r\n        {\r\n          label: '提交提案',\r\n          value: 1500,\r\n          icon: require('../../../assets/img/largeScreen/icon_proposal.png'),\r\n          bgColor: '#F1F5FF',\r\n          valueColor: '#3A61CD'\r\n        },\r\n        {\r\n          label: '提交社情民意',\r\n          value: 1057,\r\n          icon: require('../../../assets/img/largeScreen/icon_opinion.png'),\r\n          bgColor: '#DAF6F2',\r\n          valueColor: '#57BCAA'\r\n        },\r\n        {\r\n          label: '网络议政',\r\n          value: 215,\r\n          icon: require('../../../assets/img/largeScreen/icon_network.png'),\r\n          bgColor: '#E8F7FF',\r\n          valueColor: '#308FFF'\r\n        },\r\n        {\r\n          label: '参加会议',\r\n          value: 361,\r\n          icon: require('../../../assets/img/largeScreen/icon_meeting.png'),\r\n          bgColor: '#FDF8F0',\r\n          valueColor: '#EAB308'\r\n        },\r\n        {\r\n          label: '参加活动',\r\n          value: 104,\r\n          icon: require('../../../assets/img/largeScreen/icon_activity.png'),\r\n          bgColor: '#FDEFEF',\r\n          valueColor: '#FD7575'\r\n        },\r\n        {\r\n          label: '其他履职',\r\n          value: 241,\r\n          icon: require('../../../assets/img/largeScreen/icon_other.png'),\r\n          bgColor: '#E5F8FF',\r\n          valueColor: '#1FC6FF'\r\n        }\r\n      ],\r\n      meetingTypeList: [\r\n        { name: '其他会议', value: 28, percentage: '', color: '#4488EB' },\r\n        { name: '主席会议', value: 12, percentage: '', color: '#43DDBB' },\r\n        { name: '常委会议', value: 10, percentage: '', color: '#FF6665' },\r\n        { name: '全体会议', value: 2, percentage: '', color: '#ECE522' }\r\n      ],\r\n      // 活动类型统计数据\r\n      activityTypeList: [\r\n        { name: '视察', value: 32, color: '#FF9999' },\r\n        { name: '调研', value: 20, color: '#66B3FF' },\r\n        { name: '协商', value: 14, color: '#FF9999' },\r\n        { name: '学习培训', value: 22, color: '#66B3FF' },\r\n        { name: '联系界别群众', value: 8, color: '#FF9999' },\r\n        { name: '提案审查', value: 25, color: '#66B3FF' },\r\n        { name: '提案答办', value: 13, color: '#FF9999' },\r\n        { name: '提案评议', value: 32, color: '#66B3FF' },\r\n        { name: '委员联络小组', value: 15, color: '#FF9999' },\r\n        { name: '委员会客厅', value: 25, color: '#66B3FF' },\r\n        { name: '联系社会组织', value: 10, color: '#FF9999' },\r\n        { name: '界别群众重点关切问题情况通报会', value: 20, color: '#66B3FF' },\r\n        { name: '社情民意座谈会', value: 16, color: '#FF9999' },\r\n        { name: '接受媒体采访', value: 28, color: '#66B3FF' },\r\n        { name: '经政协推荐参加有关会议活动', value: 5, color: '#FF9999' },\r\n        { name: '宣讲党的政策', value: 7, color: '#66B3FF' },\r\n        { name: '服务为民', value: 32, color: '#FF9999' }\r\n      ]\r\n    })\r\n    return {\r\n      ...toRefs(data)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.MemberPerformance {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .header_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 15px 0 15px;\r\n\r\n    .header_left {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_left_line {\r\n        width: 3px;\r\n        height: 14px;\r\n        background: #007AFF;\r\n      }\r\n\r\n      .header_left_title {\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #222222;\r\n        margin-left: 8px;\r\n        font-family: Source Han Serif SC, Source Han Serif SC;\r\n      }\r\n    }\r\n\r\n    .header_right {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_right_text {\r\n        font-weight: 400;\r\n        font-size: 12px;\r\n        color: #999999;\r\n      }\r\n\r\n      .header_right_more {\r\n        font-size: 14px;\r\n        color: #0271E3;\r\n        border-radius: 14px;\r\n        border: 1px solid #0271E3;\r\n        padding: 3px 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .performanceunit_summary_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .performanceunit_summary {\r\n      padding: 12px;\r\n\r\n      .performance_grid {\r\n        display: grid;\r\n        grid-template-columns: 1fr 1fr;\r\n        gap: 10px;\r\n\r\n        .performance_card {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 16px 20px;\r\n          border-radius: 4px;\r\n\r\n          .icon_img {\r\n            width: 32px;\r\n            height: 32px;\r\n            object-fit: contain;\r\n            margin-right: 14px;\r\n          }\r\n\r\n          .card_content {\r\n            flex: 1;\r\n\r\n            .card_label {\r\n              font-size: 12px;\r\n              color: #999;\r\n              margin-bottom: 7px;\r\n            }\r\n\r\n            .card_value {\r\n              font-size: 20px;\r\n              color: #3A61CD;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .meeting_type_statistics_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .meeting_type_statistics {\r\n      width: 100%;\r\n      height: 260px;\r\n      margin-top: 20px;\r\n    }\r\n  }\r\n\r\n  .activity_type_statistics_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .activity_type_statistics {\r\n      padding: 15px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}