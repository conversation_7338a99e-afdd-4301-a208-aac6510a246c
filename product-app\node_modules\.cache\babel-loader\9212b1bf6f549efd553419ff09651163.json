{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\MemberPerformance.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\MemberPerformance.vue", "mtime": 1753948038849}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["toRefs", "reactive", "<PERSON><PERSON><PERSON>", "ActivityTypeChart", "name", "components", "setup", "data", "performanceList", "label", "value", "icon", "require", "bgColor", "valueColor", "meetingTypeList", "percentage", "color", "activityTypeList"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\MemberPerformance.vue"], "sourcesContent": ["<template>\r\n  <div class=\"MemberPerformance\">\r\n    <!-- 年度履职汇总 -->\r\n    <div class=\"performanceunit_summary_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">年度履职汇总</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"performanceunit_summary\">\r\n        <div class=\"performance_grid\">\r\n          <div v-for=\"(item, index) in performanceList\" :key=\"index\" class=\"performance_card\"\r\n            :style=\"{ background: item.bgColor }\">\r\n            <img :src=\"item.icon\" :alt=\"item.label\" class=\"icon_img\" />\r\n            <div class=\"card_content\">\r\n              <div class=\"card_label\">{{ item.label }}</div>\r\n              <div class=\"card_value\" :style=\"{ color: item.valueColor }\">{{ item.value }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 会议类型统计 -->\r\n    <div class=\"meeting_type_statistics_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">会议类型统计</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"meeting_type_statistics\">\r\n        <PieChart id=\"meetingType\" :chart-data=\"meetingTypeList\" :radius=\"['35%', '60%']\" :center=\"['50%', '30%']\" />\r\n      </div>\r\n    </div>\r\n    <!-- 活动类型统计 -->\r\n    <div class=\"activity_type_statistics_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">活动类型统计</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"activity_type_statistics\">\r\n        <ActivityTypeChart id=\"memberPerformance\" :data=\"activityTypeList\" style=\"height: 500px;\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { toRefs, reactive } from 'vue'\r\nimport PieChart from './echartsComponent/PieChart.vue'\r\nimport ActivityTypeChart from './echartsComponent/ActivityTypeChart.vue'\r\nexport default {\r\n  name: 'MemberPerformance',\r\n  components: { PieChart, ActivityTypeChart },\r\n  setup () {\r\n    const data = reactive({\r\n      // 年度履职汇总数据\r\n      performanceList: [\r\n        {\r\n          label: '提交提案',\r\n          value: 1500,\r\n          icon: require('../../../assets/img/largeScreen/icon_proposal.png'),\r\n          bgColor: '#F1F5FF',\r\n          valueColor: '#3A61CD'\r\n        },\r\n        {\r\n          label: '提交社情民意',\r\n          value: 1057,\r\n          icon: require('../../../assets/img/largeScreen/icon_opinion.png'),\r\n          bgColor: '#DAF6F2',\r\n          valueColor: '#57BCAA'\r\n        },\r\n        {\r\n          label: '网络议政',\r\n          value: 215,\r\n          icon: require('../../../assets/img/largeScreen/icon_network.png'),\r\n          bgColor: '#E8F7FF',\r\n          valueColor: '#308FFF'\r\n        },\r\n        {\r\n          label: '参加会议',\r\n          value: 361,\r\n          icon: require('../../../assets/img/largeScreen/icon_meeting.png'),\r\n          bgColor: '#FDF8F0',\r\n          valueColor: '#EAB308'\r\n        },\r\n        {\r\n          label: '参加活动',\r\n          value: 104,\r\n          icon: require('../../../assets/img/largeScreen/icon_activity.png'),\r\n          bgColor: '#FDEFEF',\r\n          valueColor: '#FD7575'\r\n        },\r\n        {\r\n          label: '其他履职',\r\n          value: 241,\r\n          icon: require('../../../assets/img/largeScreen/icon_other.png'),\r\n          bgColor: '#E5F8FF',\r\n          valueColor: '#1FC6FF'\r\n        }\r\n      ],\r\n      meetingTypeList: [\r\n        { name: '其他会议', value: 28, percentage: '', color: '#4488EB' },\r\n        { name: '主席会议', value: 12, percentage: '', color: '#43DDBB' },\r\n        { name: '常委会议', value: 10, percentage: '', color: '#FF6665' },\r\n        { name: '全体会议', value: 2, percentage: '', color: '#ECE522' }\r\n      ],\r\n      // 活动类型统计数据\r\n      activityTypeList: [\r\n        { name: '视察', value: 32 },\r\n        { name: '调研', value: 20 },\r\n        { name: '协商', value: 14 },\r\n        { name: '学习培训', value: 22 },\r\n        { name: '联系界别群众', value: 8 },\r\n        { name: '提案审查', value: 25 },\r\n        { name: '提案答办', value: 13 },\r\n        { name: '提案评议', value: 32 },\r\n        { name: '委员联络小组', value: 15 },\r\n        { name: '委员会客厅', value: 25 },\r\n        { name: '联系社会组织', value: 10 },\r\n        { name: '界别群众重点关切问题情况通报会', value: 20 },\r\n        { name: '社情民意座谈会', value: 16 },\r\n        { name: '接受媒体采访', value: 28 },\r\n        { name: '经政协推荐参加有关会议活动', value: 5 },\r\n        { name: '宣讲党的政策', value: 7 },\r\n        { name: '服务为民', value: 32 }\r\n      ]\r\n    })\r\n    return {\r\n      ...toRefs(data)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.MemberPerformance {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .header_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 15px 0 15px;\r\n\r\n    .header_left {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_left_line {\r\n        width: 3px;\r\n        height: 14px;\r\n        background: #007AFF;\r\n      }\r\n\r\n      .header_left_title {\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #222222;\r\n        margin-left: 8px;\r\n        font-family: Source Han Serif SC, Source Han Serif SC;\r\n      }\r\n    }\r\n\r\n    .header_right {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_right_text {\r\n        font-weight: 400;\r\n        font-size: 12px;\r\n        color: #999999;\r\n      }\r\n\r\n      .header_right_more {\r\n        font-size: 14px;\r\n        color: #0271E3;\r\n        border-radius: 14px;\r\n        border: 1px solid #0271E3;\r\n        padding: 3px 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .performanceunit_summary_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .performanceunit_summary {\r\n      padding: 12px;\r\n\r\n      .performance_grid {\r\n        display: grid;\r\n        grid-template-columns: 1fr 1fr;\r\n        gap: 10px;\r\n\r\n        .performance_card {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 16px 20px;\r\n          border-radius: 4px;\r\n\r\n          .icon_img {\r\n            width: 32px;\r\n            height: 32px;\r\n            object-fit: contain;\r\n            margin-right: 14px;\r\n          }\r\n\r\n          .card_content {\r\n            flex: 1;\r\n\r\n            .card_label {\r\n              font-size: 12px;\r\n              color: #999;\r\n              margin-bottom: 7px;\r\n            }\r\n\r\n            .card_value {\r\n              font-size: 20px;\r\n              color: #3A61CD;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .meeting_type_statistics_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .meeting_type_statistics {\r\n      width: 100%;\r\n      height: 260px;\r\n      margin-top: 20px;\r\n    }\r\n  }\r\n\r\n  .activity_type_statistics_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .activity_type_statistics {\r\n      padding: 15px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAkDA,SAASA,MAAM,EAAEC,QAAO,QAAS,KAAI;AACrC,OAAOC,QAAO,MAAO,iCAAgC;AACrD,OAAOC,iBAAgB,MAAO,0CAAyC;AACvE,eAAe;EACbC,IAAI,EAAE,mBAAmB;EACzBC,UAAU,EAAE;IAAEH,QAAQ;IAAEC;EAAkB,CAAC;EAC3CG,KAAIA,CAAA,EAAK;IACP,MAAMC,IAAG,GAAIN,QAAQ,CAAC;MACpB;MACAO,eAAe,EAAE,CACf;QACEC,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,IAAI;QACXC,IAAI,EAAEC,OAAO,CAAC,mDAAmD,CAAC;QAClEC,OAAO,EAAE,SAAS;QAClBC,UAAU,EAAE;MACd,CAAC,EACD;QACEL,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE,IAAI;QACXC,IAAI,EAAEC,OAAO,CAAC,kDAAkD,CAAC;QACjEC,OAAO,EAAE,SAAS;QAClBC,UAAU,EAAE;MACd,CAAC,EACD;QACEL,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,GAAG;QACVC,IAAI,EAAEC,OAAO,CAAC,kDAAkD,CAAC;QACjEC,OAAO,EAAE,SAAS;QAClBC,UAAU,EAAE;MACd,CAAC,EACD;QACEL,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,GAAG;QACVC,IAAI,EAAEC,OAAO,CAAC,kDAAkD,CAAC;QACjEC,OAAO,EAAE,SAAS;QAClBC,UAAU,EAAE;MACd,CAAC,EACD;QACEL,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,GAAG;QACVC,IAAI,EAAEC,OAAO,CAAC,mDAAmD,CAAC;QAClEC,OAAO,EAAE,SAAS;QAClBC,UAAU,EAAE;MACd,CAAC,EACD;QACEL,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,GAAG;QACVC,IAAI,EAAEC,OAAO,CAAC,gDAAgD,CAAC;QAC/DC,OAAO,EAAE,SAAS;QAClBC,UAAU,EAAE;MACd,EACD;MACDC,eAAe,EAAE,CACf;QAAEX,IAAI,EAAE,MAAM;QAAEM,KAAK,EAAE,EAAE;QAAEM,UAAU,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAU,CAAC,EAC7D;QAAEb,IAAI,EAAE,MAAM;QAAEM,KAAK,EAAE,EAAE;QAAEM,UAAU,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAU,CAAC,EAC7D;QAAEb,IAAI,EAAE,MAAM;QAAEM,KAAK,EAAE,EAAE;QAAEM,UAAU,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAU,CAAC,EAC7D;QAAEb,IAAI,EAAE,MAAM;QAAEM,KAAK,EAAE,CAAC;QAAEM,UAAU,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAU,EAC5D;MACD;MACAC,gBAAgB,EAAE,CAChB;QAAEd,IAAI,EAAE,IAAI;QAAEM,KAAK,EAAE;MAAG,CAAC,EACzB;QAAEN,IAAI,EAAE,IAAI;QAAEM,KAAK,EAAE;MAAG,CAAC,EACzB;QAAEN,IAAI,EAAE,IAAI;QAAEM,KAAK,EAAE;MAAG,CAAC,EACzB;QAAEN,IAAI,EAAE,MAAM;QAAEM,KAAK,EAAE;MAAG,CAAC,EAC3B;QAAEN,IAAI,EAAE,QAAQ;QAAEM,KAAK,EAAE;MAAE,CAAC,EAC5B;QAAEN,IAAI,EAAE,MAAM;QAAEM,KAAK,EAAE;MAAG,CAAC,EAC3B;QAAEN,IAAI,EAAE,MAAM;QAAEM,KAAK,EAAE;MAAG,CAAC,EAC3B;QAAEN,IAAI,EAAE,MAAM;QAAEM,KAAK,EAAE;MAAG,CAAC,EAC3B;QAAEN,IAAI,EAAE,QAAQ;QAAEM,KAAK,EAAE;MAAG,CAAC,EAC7B;QAAEN,IAAI,EAAE,OAAO;QAAEM,KAAK,EAAE;MAAG,CAAC,EAC5B;QAAEN,IAAI,EAAE,QAAQ;QAAEM,KAAK,EAAE;MAAG,CAAC,EAC7B;QAAEN,IAAI,EAAE,iBAAiB;QAAEM,KAAK,EAAE;MAAG,CAAC,EACtC;QAAEN,IAAI,EAAE,SAAS;QAAEM,KAAK,EAAE;MAAG,CAAC,EAC9B;QAAEN,IAAI,EAAE,QAAQ;QAAEM,KAAK,EAAE;MAAG,CAAC,EAC7B;QAAEN,IAAI,EAAE,eAAe;QAAEM,KAAK,EAAE;MAAE,CAAC,EACnC;QAAEN,IAAI,EAAE,QAAQ;QAAEM,KAAK,EAAE;MAAE,CAAC,EAC5B;QAAEN,IAAI,EAAE,MAAM;QAAEM,KAAK,EAAE;MAAG;IAE9B,CAAC;IACD,OAAO;MACL,GAAGV,MAAM,CAACO,IAAI;IAChB;EACF;AACF", "ignoreList": []}]}