{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\MemberPerformance.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\MemberPerformance.vue", "mtime": 1753943981713}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["toRefs", "reactive", "<PERSON><PERSON><PERSON>", "name", "components", "setup", "data", "performanceList", "label", "value", "icon", "require", "bgColor", "valueColor", "meetingTypeList", "percentage", "color"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\MemberPerformance.vue"], "sourcesContent": ["<template>\r\n  <div class=\"MemberPerformance\">\r\n    <!-- 年度履职汇总 -->\r\n    <div class=\"performanceunit_summary_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">年度履职汇总</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"performanceunit_summary\">\r\n        <div class=\"performance_grid\">\r\n          <div v-for=\"(item, index) in performanceList\" :key=\"index\" class=\"performance_card\"\r\n            :style=\"{ background: item.bgColor }\">\r\n            <img :src=\"item.icon\" :alt=\"item.label\" class=\"icon_img\" />\r\n            <div class=\"card_content\">\r\n              <div class=\"card_label\">{{ item.label }}</div>\r\n              <div class=\"card_value\" :style=\"{ color: item.valueColor }\">{{ item.value }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 会议类型统计 -->\r\n    <div class=\"meeting_type_statistics_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">会议类型统计</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"meeting_type_statistics\">\r\n        <PieChart id=\"meetingType\" :chart-data=\"meetingTypeList\" :radius=\"['35%', '60%']\" :center=\"['50%', '30%']\" />\r\n      </div>\r\n    </div>\r\n    <!-- 活动类型统计 -->\r\n    <div class=\"activity_type_statistics_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">活动类型统计</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"activity_type_statistics\">\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { toRefs, reactive } from 'vue'\r\nimport PieChart from './echartsComponent/PieChart.vue'\r\nexport default {\r\n  name: 'MemberPerformance',\r\n  components: { PieChart },\r\n  setup () {\r\n    const data = reactive({\r\n      // 年度履职汇总数据\r\n      performanceList: [\r\n        {\r\n          label: '提交提案',\r\n          value: 1500,\r\n          icon: require('../../../assets/img/largeScreen/icon_proposal.png'),\r\n          bgColor: '#F1F5FF',\r\n          valueColor: '#3A61CD'\r\n        },\r\n        {\r\n          label: '提交社情民意',\r\n          value: 1057,\r\n          icon: require('../../../assets/img/largeScreen/icon_opinion.png'),\r\n          bgColor: '#DAF6F2',\r\n          valueColor: '#57BCAA'\r\n        },\r\n        {\r\n          label: '网络议政',\r\n          value: 215,\r\n          icon: require('../../../assets/img/largeScreen/icon_network.png'),\r\n          bgColor: '#E8F7FF',\r\n          valueColor: '#308FFF'\r\n        },\r\n        {\r\n          label: '参加会议',\r\n          value: 361,\r\n          icon: require('../../../assets/img/largeScreen/icon_meeting.png'),\r\n          bgColor: '#FDF8F0',\r\n          valueColor: '#EAB308'\r\n        },\r\n        {\r\n          label: '参加活动',\r\n          value: 104,\r\n          icon: require('../../../assets/img/largeScreen/icon_activity.png'),\r\n          bgColor: '#FDEFEF',\r\n          valueColor: '#FD7575'\r\n        },\r\n        {\r\n          label: '其他履职',\r\n          value: 241,\r\n          icon: require('../../../assets/img/largeScreen/icon_other.png'),\r\n          bgColor: '#E5F8FF',\r\n          valueColor: '#1FC6FF'\r\n        }\r\n      ],\r\n      meetingTypeList: [\r\n        { name: '其他会议', value: 28, percentage: '', color: '#4488EB' },\r\n        { name: '主席会议', value: 12, percentage: '', color: '#43DDBB' },\r\n        { name: '常委会议', value: 10, percentage: '', color: '#FF6665' },\r\n        { name: '全体会议', value: 2, percentage: '', color: '#ECE522' }\r\n      ]\r\n    })\r\n    return {\r\n      ...toRefs(data)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.MemberPerformance {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .header_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 15px 0 15px;\r\n\r\n    .header_left {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_left_line {\r\n        width: 3px;\r\n        height: 14px;\r\n        background: #007AFF;\r\n      }\r\n\r\n      .header_left_title {\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #222222;\r\n        margin-left: 8px;\r\n        font-family: Source Han Serif SC, Source Han Serif SC;\r\n      }\r\n    }\r\n\r\n    .header_right {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_right_text {\r\n        font-weight: 400;\r\n        font-size: 12px;\r\n        color: #999999;\r\n      }\r\n\r\n      .header_right_more {\r\n        font-size: 14px;\r\n        color: #0271E3;\r\n        border-radius: 14px;\r\n        border: 1px solid #0271E3;\r\n        padding: 3px 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .performanceunit_summary_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .performanceunit_summary {\r\n      padding: 12px;\r\n\r\n      .performance_grid {\r\n        display: grid;\r\n        grid-template-columns: 1fr 1fr;\r\n        gap: 10px;\r\n\r\n        .performance_card {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 16px 20px;\r\n          border-radius: 4px;\r\n\r\n          .icon_img {\r\n            width: 32px;\r\n            height: 32px;\r\n            object-fit: contain;\r\n            margin-right: 14px;\r\n          }\r\n\r\n          .card_content {\r\n            flex: 1;\r\n\r\n            .card_label {\r\n              font-size: 12px;\r\n              color: #999;\r\n              margin-bottom: 7px;\r\n            }\r\n\r\n            .card_value {\r\n              font-size: 20px;\r\n              color: #3A61CD;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .meeting_type_statistics_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .meeting_type_statistics {\r\n      width: 100%;\r\n      height: 260px;\r\n      margin-top: 20px;\r\n    }\r\n  }\r\n\r\n  .activity_type_statistics_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .activity_type_statistics {\r\n      padding: 15px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAiDA,SAASA,MAAM,EAAEC,QAAO,QAAS,KAAI;AACrC,OAAOC,QAAO,MAAO,iCAAgC;AACrD,eAAe;EACbC,IAAI,EAAE,mBAAmB;EACzBC,UAAU,EAAE;IAAEF;EAAS,CAAC;EACxBG,KAAIA,CAAA,EAAK;IACP,MAAMC,IAAG,GAAIL,QAAQ,CAAC;MACpB;MACAM,eAAe,EAAE,CACf;QACEC,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,IAAI;QACXC,IAAI,EAAEC,OAAO,CAAC,mDAAmD,CAAC;QAClEC,OAAO,EAAE,SAAS;QAClBC,UAAU,EAAE;MACd,CAAC,EACD;QACEL,KAAK,EAAE,QAAQ;QACfC,KAAK,EAAE,IAAI;QACXC,IAAI,EAAEC,OAAO,CAAC,kDAAkD,CAAC;QACjEC,OAAO,EAAE,SAAS;QAClBC,UAAU,EAAE;MACd,CAAC,EACD;QACEL,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,GAAG;QACVC,IAAI,EAAEC,OAAO,CAAC,kDAAkD,CAAC;QACjEC,OAAO,EAAE,SAAS;QAClBC,UAAU,EAAE;MACd,CAAC,EACD;QACEL,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,GAAG;QACVC,IAAI,EAAEC,OAAO,CAAC,kDAAkD,CAAC;QACjEC,OAAO,EAAE,SAAS;QAClBC,UAAU,EAAE;MACd,CAAC,EACD;QACEL,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,GAAG;QACVC,IAAI,EAAEC,OAAO,CAAC,mDAAmD,CAAC;QAClEC,OAAO,EAAE,SAAS;QAClBC,UAAU,EAAE;MACd,CAAC,EACD;QACEL,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,GAAG;QACVC,IAAI,EAAEC,OAAO,CAAC,gDAAgD,CAAC;QAC/DC,OAAO,EAAE,SAAS;QAClBC,UAAU,EAAE;MACd,EACD;MACDC,eAAe,EAAE,CACf;QAAEX,IAAI,EAAE,MAAM;QAAEM,KAAK,EAAE,EAAE;QAAEM,UAAU,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAU,CAAC,EAC7D;QAAEb,IAAI,EAAE,MAAM;QAAEM,KAAK,EAAE,EAAE;QAAEM,UAAU,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAU,CAAC,EAC7D;QAAEb,IAAI,EAAE,MAAM;QAAEM,KAAK,EAAE,EAAE;QAAEM,UAAU,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAU,CAAC,EAC7D;QAAEb,IAAI,EAAE,MAAM;QAAEM,KAAK,EAAE,CAAC;QAAEM,UAAU,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAU;IAE/D,CAAC;IACD,OAAO;MACL,GAAGhB,MAAM,CAACM,IAAI;IAChB;EACF;AACF", "ignoreList": []}]}