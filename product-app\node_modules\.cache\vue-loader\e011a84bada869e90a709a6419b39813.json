{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue", "mtime": 1753946280671}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue"], "names": [], "mappings": ";AAKA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACvD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,EAAE;MACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;IAEvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;MAErB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEtC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,EAAE;UACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACnB,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;UAClC;QACF,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACN;YACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;cACrC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC/D;YACF,CAAC,CAAC,CAAC;YACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACL,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACd;gBACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;kBAC3B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACxC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;kBAC9C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;oBACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACzD;kBACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACZ,CAAC;gBACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACf,CAAC;cACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACZ;gBACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;cACf;YACF,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;YAC3B;UACF;QACF,CAAC;QACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5B;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChC;;IAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MAC5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ;IACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf;EACF;AACF", "file": "D:/zy/xm/h5/qdzx_h5/product-app/src/views/SmartBrainLargeScreen/component/echartsComponent/ActivityTypeChart.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"activity-type-chart\" :id=\"chartId\"></div>\n</template>\n\n<script>\nimport { ref, onMounted, onUnmounted, watch } from 'vue'\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'ActivityTypeChart',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    data: {\n      type: Array,\n      default: () => []\n    },\n    height: {\n      type: String,\n      default: '400px'\n    }\n  },\n  setup (props) {\n    const chartId = ref(`activity-chart-${props.id}`)\n    let chartInstance = null\n\n    const initChart = () => {\n      const container = document.getElementById(chartId.value)\n      if (!container) return\n\n      chartInstance = echarts.init(container)\n\n      const option = {\n        grid: {\n          left: '5%',\n          right: '10%',\n          top: '8%',\n          bottom: '2%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'value',\n          show: false,\n          max: function (value) {\n            return Math.ceil(value.max * 1.1)\n          }\n        },\n        yAxis: {\n          type: 'category',\n          data: props.data.map(item => item.name),\n          axisLine: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            show: false // 隐藏Y轴标签，因为我们要在柱状图上方显示\n          },\n          inverse: true // 反转Y轴，让第一项在顶部\n        },\n        series: [\n          {\n            type: 'bar',\n            data: props.data.map((item, index) => ({\n              value: item.value,\n              itemStyle: {\n                color: item.color || (index % 2 === 0 ? '#FF9999' : '#66B3FF')\n              }\n            })),\n            barHeight: 14,\n            label: [\n              // 在柱状图上方显示活动名称\n              {\n                show: true,\n                position: 'top',\n                color: '#666666',\n                fontSize: 11,\n                fontWeight: 400,\n                formatter: function (params) {\n                  const item = props.data[params.dataIndex]\n                  const name = item && item.name ? item.name : ''\n                  // 如果文字太长，进行换行处理\n                  if (name.length > 10) {\n                    return name.substring(0, 10) + '\\n' + name.substring(10)\n                  }\n                  return name\n                },\n                offset: [0, -8],\n                lineHeight: 14\n              },\n              // 在柱状图右侧显示数值\n              {\n                show: true,\n                position: 'right',\n                color: '#333333',\n                fontSize: 11,\n                fontWeight: 500,\n                formatter: '{c}',\n                offset: [8, 0]\n              }\n            ],\n            itemStyle: {\n              borderRadius: [0, 4, 4, 0]\n            }\n          }\n        ],\n        animation: true,\n        animationDuration: 1000,\n        animationEasing: 'cubicOut'\n      }\n\n      chartInstance.setOption(option)\n    }\n\n    const resizeChart = () => {\n      if (chartInstance) {\n        chartInstance.resize()\n      }\n    }\n\n    onMounted(() => {\n      initChart()\n      window.addEventListener('resize', resizeChart)\n    })\n\n    onUnmounted(() => {\n      if (chartInstance) {\n        chartInstance.dispose()\n      }\n      window.removeEventListener('resize', resizeChart)\n    })\n\n    watch(() => props.data, () => {\n      if (chartInstance) {\n        initChart()\n      }\n    }, { deep: true })\n\n    return {\n      chartContainer\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.activity-type-chart {\n  width: 100%;\n  height: v-bind(height);\n}\n</style>\n"]}]}