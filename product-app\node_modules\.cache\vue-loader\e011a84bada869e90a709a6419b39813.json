{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue", "mtime": 1753951703798}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue"], "names": [], "mappings": ";AAKA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACL,CAAC,CAAC,EAAE;MACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACf,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,EAAE;MACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAClB;EACF,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;IAEvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MACtB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC;MACA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MACf,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QAC/C,CAAC,CAAC,EAAE,EAAE,EAAE;QACR,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE;UACT,EAAE,EAAE;QACN;QACA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;UACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACd,CAAC,EAAE,CAAC;cACJ,CAAC,EAAE,CAAC;cACJ,CAAC,CAAC,EAAE,CAAC;cACL,CAAC,CAAC,EAAE,CAAC;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACV,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC/B,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAChC;YACF;UACF;QACF;QACA,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACb,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACd,CAAC,EAAE,CAAC;cACJ,CAAC,EAAE,CAAC;cACJ,CAAC,CAAC,EAAE,CAAC;cACL,CAAC,CAAC,EAAE,CAAC;cACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACV,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC/B,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAChC;YACF;UACF;QACF;QACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3D,CAAC,CAAC,CAAC,CAAC,CAAC;QACP;QACA,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC;QACA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;cAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACV;cACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC;UACF,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,EAAE;YACJ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACnB,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACd,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACL,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ;UACF,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACN;cACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC;YACD;cACE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;kBACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;kBACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACjB;cACF;YACF;UACF;QACF;QACA,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;;QAEpB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,QAAQ,CAAC;QACV,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,QAAQ,CAAC;QACV,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,QAAQ,CAAC;QACV,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACpB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,QAAQ;QACT,CAAC,MAAM,CAAC;QACR,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,QAAQ;QACT,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,UAAU,CAAC;QACZ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,UAAU,CAAC;QACZ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,UAAU,CAAC;QACZ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,QAAQ;QACT,CAAC,MAAM,CAAC;QACR,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,QAAQ;QACT,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAClD,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE;QAC1B,CAAC,MAAM,EAAE;QACT,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,QAAQ,CAAC,EAAE,CAAC;QACb,CAAC,QAAQ,CAAC,EAAE,CAAC;QACb,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;QACd,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;QACd,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrB,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1C,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACzC,CAAC,QAAQ;QACT,CAAC,MAAM;QACP,CAAC,MAAM,EAAE;QACT,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,QAAQ,CAAC,EAAE,CAAC;QACb,CAAC,QAAQ,CAAC,EAAE,CAAC;QACb,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;QACd,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;QACd,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrB,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1C,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACzC,CAAC,QAAQ;QACT,CAAC,MAAM;QACP,CAAC,EAAE;QACH,CAAC,UAAU,CAAC,CAAC,CAAC;QACd,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACvB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClB,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,YAAY;QACb,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC3B,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAC5C,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC9D,CAAC,cAAc,CAAC;QAChB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAC7B,CAAC,YAAY,CAAC;QACd,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,YAAY;QACb,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC3B,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC5B,CAAC,YAAY;QACb,CAAC,UAAU;QACX,CAAC,QAAQ;QACT,CAAC,MAAM;QACP,CAAC,IAAI;QACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC;IACH;;IAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAChB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MAC5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ;IACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACR;EACF;AACF", "file": "D:/zy/xm/h5/qdzx_h5/product-app/src/views/SmartBrainLargeScreen/component/echartsComponent/ActivityTypeChart.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"activity-type-chart\" :id=\"chartId\"></div>\n</template>\n\n<script>\nimport { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'ActivityTypeChart',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    data: {\n      type: Array,\n      default: () => []\n    }\n  },\n  setup (props) {\n    const chartId = ref(props.id)\n    let chartInstance = null\n\n    const initChart = () => {\n      var charts = {\n        cityList: props.data.map(v => v.name),\n        cityData: props.data.map(v => v.value)\n      }\n      var top10CityList = charts.cityList\n      var top10CityData = charts.cityData\n      const lineY = []\n      const lineT = []\n      for (var i = 0; i < charts.cityList.length; i++) {\n        var x = i\n        if (x > 1) {\n          x = 2\n        }\n        var data = {\n          name: charts.cityList[i],\n          value: top10CityData[i],\n          barGap: '-100%',\n          itemStyle: {\n            color: {\n              type: 'linear',\n              x: 0,\n              y: 0,\n              x2: 1,\n              y2: 0,\n              colorStops: [\n                { offset: 0, color: '#FFFFFF' },\n                { offset: 1, color: '#559FFF' }\n              ]\n            }\n          }\n        }\n        var data1 = {\n          value: top10CityData[i],\n          label: {\n            show: true,\n            position: 'right',\n            color: '#999',\n            fontSize: 14,\n            distance: 10\n          },\n          itemStyle: {\n            color: {\n              type: 'linear',\n              x: 0,\n              y: 0,\n              x2: 1,\n              y2: 0,\n              colorStops: [\n                { offset: 0, color: '#FFFFFF' },\n                { offset: 1, color: '#EF817C' }\n              ]\n            }\n          }\n        }\n        lineY.push(data)\n        lineT.push(data1)\n        console.log('lineT===>', lineT)\n        console.log('lineT===>', lineT)\n      }\n      nextTick(() => {\n        const dom = document.getElementById(chartId.value)\n        if (!dom) {\n          console.error('Chart DOM element not found:', chartId.value)\n          return\n        }\n        if (!chartInstance) {\n          chartInstance = echarts.init(dom)\n        }\n        const option = {\n          tooltip: {\n            trigger: 'item',\n            formatter: (p) => {\n              if (p.seriesName === 'total') {\n                return ''\n              }\n              return `${p.name}<br/>${p.value}`\n            }\n          },\n          grid: {\n            left: '0%',\n            right: '20%',\n            top: '2%',\n            bottom: '2%',\n            containLabel: true\n          },\n          yAxis: {\n            type: 'category',\n            inverse: true,\n            axisTick: {\n              show: false\n            },\n            axisLine: {\n              show: false\n            },\n            axisLabel: {\n              show: false,\n              inside: false\n            },\n            data: top10CityList\n          },\n          xAxis: {\n            type: 'value',\n            axisTick: {\n              show: false\n            },\n            axisLine: {\n              show: false\n            },\n            splitLine: {\n              show: false\n            },\n            axisLabel: {\n              show: false\n            }\n          },\n          series: [\n            {\n              name: 'total',\n              type: 'bar',\n              barGap: '-100%',\n              barWidth: 10,\n              data: lineT,\n              legendHoverLink: false\n            },\n            {\n              name: 'bar',\n              type: 'bar',\n              barWidth: 10,\n              data: lineY,\n              label: {\n                normal: {\n                  show: true,\n                  fontSize: '12px',\n                  color: '#999',\n                  position: [0, '-20px'],\n                  formatter: '{b}'\n                }\n              }\n            }\n          ]\n        }\n        //   const option = {\n\n        //     xAxis: {\n        //       type: 'value',\n        //       axisTick: {\n        //         show: false\n        //       },\n        //       axisLine: {\n        //         show: false\n        //       },\n        //       splitLine: {\n        //         show: false\n        //       },\n        //       axisLabel: {\n        //         show: false\n        //       }\n        //     },\n        //     yAxis: [\n        //       {\n        //         type: 'category',\n        //         inverse: true,\n        //         axisTick: {\n        //           show: false\n        //         },\n        //         axisLine: {\n        //           show: false\n        //         },\n        //         axisLabel: {\n        //           show: false // 隐藏Y轴标签，名称将显示在柱状图上方\n        //         },\n        //         data: props.data.map(item => item.name)\n        //       }\n        //     ],\n        //     series: [\n        //       {\n        //         type: 'bar',\n        //         data: props.data.map((item, index) => ({\n        //           value: item.value,\n        // itemStyle: {\n        //   color: index % 2 === 0\n        //     ? {\n        //       type: 'linear',\n        //       x: 0,\n        //       y: 0,\n        //       x2: 1,\n        //       y2: 0,\n        //       colorStops: [\n        //         { offset: 0, color: '#FFFFFF' },\n        //         { offset: 1, color: '#EF817C' }\n        //       ]\n        //     }\n        //     : {\n        //       type: 'linear',\n        //       x: 0,\n        //       y: 0,\n        //       x2: 1,\n        //       y2: 0,\n        //       colorStops: [\n        //         { offset: 0, color: '#FFFFFF' },\n        //         { offset: 1, color: '#559FFF' }\n        //       ]\n        //     }\n        // }\n        //         })),\n        //         barWidth: 10,\n        //         label: [\n        //           // 在柱状图上方显示活动名称\n        //           {\n        //             show: true,\n        //             position: 'top',\n        //             color: '#666666',\n        //             fontSize: 12,\n        //             fontWeight: 'normal',\n        //             formatter: function (params) {\n        //               // 直接从Y轴数据中获取名称\n        //               return props.data[params.dataIndex].name || ''\n        //             },\n        //             offset: [0, -8]\n        //           },\n        //           // 在柱状图右侧显示数值\n        //           {\n        //             show: true,\n        //             position: 'right',\n        //             color: '#666666',\n        //             fontSize: 12,\n        //             fontWeight: 'normal',\n        //             formatter: '{c}',\n        //             offset: [8, 0]\n        //           }\n        //         ]\n        //       }\n        //     ]\n        //   }\n        chartInstance.setOption(option)\n      })\n    }\n\n    const resizeChart = () => {\n      if (chartInstance) {\n        chartInstance.resize()\n      }\n    }\n\n    onMounted(() => {\n      initChart()\n      window.addEventListener('resize', resizeChart)\n    })\n\n    onUnmounted(() => {\n      if (chartInstance) {\n        chartInstance.dispose()\n      }\n      window.removeEventListener('resize', resizeChart)\n    })\n\n    watch(() => props.data, () => {\n      if (chartInstance) {\n        initChart()\n      }\n    }, { deep: true })\n\n    return {\n      chartId\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.activity-type-chart {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"]}]}