{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue?vue&type=style&index=0&id=6b17b738&lang=less&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue", "mtime": 1753940910433}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue"], "names": [], "mappings": ";AAuUA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACvD;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACnB;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACpB;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACpB;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;YAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB;;YAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB;;YAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB;UACF;QACF;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACjB;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;;YAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACpB;;YAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACjB;UACF;QACF;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAET,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;UAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;YAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB;;YAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACd,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB;;YAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB;UACF;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB;QACF;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7B;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;MAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;;QAE1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACpB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;UAErB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;YAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrB;;YAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;cACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB;UACF;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACT,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAChB;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UAChB;QACF;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACf;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACrB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChB;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7B;EACF;AACF", "file": "D:/zy/xm/h5/qdzx_h5/product-app/src/views/SmartBrainLargeScreen/component/ProposalWork.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"ProposalWork\">\r\n    <!-- 提案整体情况 -->\r\n    <div class=\"proposal_overall_situation_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">提案整体情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"proposal_overall_situation\">\r\n        <div class=\"statistics_row\">\r\n          <div class=\"statistics_card\" style=\"background: #E8F7FF;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_total.png\" alt=\"提案总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">提案总件数</div>\r\n            <div class=\"card_value proposal_total_text\">{{ proposalTotal }}</div>\r\n          </div>\r\n          <div class=\"statistics_card\" style=\"background: #F1F5FF;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_register_num.png\" alt=\"立案总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">立案总件数</div>\r\n            <div class=\"card_value register_text\">{{ registerTotal }}</div>\r\n          </div>\r\n          <div class=\"statistics_card\" style=\"background: #DAF6F2;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_reply_num.png\" alt=\"答复总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">答复总件数</div>\r\n            <div class=\"card_value reply_text\">{{ replyTotal }}</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"progress_row\">\r\n          <div class=\"progress_card\">\r\n            <div class=\"progress_content\">\r\n              <div class=\"progress_circle blue_progress\">\r\n                <svg width=\"50\" height=\"50\" viewBox=\"0 0 80 80\">\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"rgba(58,147,255,0.2)\" stroke-width=\"8\" />\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"#3A61CD\" stroke-width=\"8\" stroke-dasharray=\"188.4\"\r\n                    :stroke-dashoffset=\"registerCircleOffset\" stroke-linecap=\"round\" transform=\"rotate(-90 40 40)\" />\r\n                </svg>\r\n              </div>\r\n              <div class=\"progress_info\">\r\n                <div class=\"progress_label\">立案率</div>\r\n                <span class=\"progress_value\" style=\"color: #3A61CD;\">{{ registerRate }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"progress_card\">\r\n            <div class=\"progress_content\">\r\n              <div class=\"progress_circle green_progress\">\r\n                <svg width=\"50\" height=\"50\" viewBox=\"0 0 80 80\">\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"rgba(58,147,255,0.2)\" stroke-width=\"8\" />\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"#57BCAA\" stroke-width=\"8\" stroke-dasharray=\"188.4\"\r\n                    :stroke-dashoffset=\"replyCircleOffset\" stroke-linecap=\"round\" transform=\"rotate(-90 40 40)\" />\r\n                </svg>\r\n              </div>\r\n              <div class=\"progress_info\">\r\n                <div class=\"progress_label\">答复率</div>\r\n                <span class=\"progress_value\" style=\"color: #57BCAA;\">{{ replyRate }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 提交情况 -->\r\n    <div class=\"submit_status_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">提交情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"submit_status\">\r\n        <div class=\"submit_statistics_row\">\r\n          <div class=\"submit_card\" style=\"background: #E8F4FF;\">\r\n            <div class=\"submit_value committee_text\">{{ committeeProposal }}</div>\r\n            <div class=\"submit_label\">委员提案</div>\r\n          </div>\r\n          <div class=\"submit_card\" style=\"background: #FFF8E1;\">\r\n            <div class=\"submit_value boundary_text\">{{ boundaryProposal }}</div>\r\n            <div class=\"submit_label\">界别提案</div>\r\n          </div>\r\n          <div class=\"submit_card\" style=\"background: #E8F5E8;\">\r\n            <div class=\"submit_value organization_text\">{{ organizationProposal }}</div>\r\n            <div class=\"submit_label\">组织提案</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 类型分布 -->\r\n    <div class=\"proposal_type_analysis_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">类型分布</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"proposal_type_analysis\">\r\n        <pieEchartsLegend id=\"typeAnalysisPie\" :dataList=\"typeAnalysisList\" title=\"类型分析\" />\r\n      </div>\r\n    </div>\r\n    <!-- 答复类型 -->\r\n    <div class=\"reply_type_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">答复类型</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"reply_type\">\r\n        <ProgressBarChart title=\"面复\" :desc=\"`占总件数${replyType.face}%`\" :percent=\"replyType.face\"\r\n          :value=\"replyType.faceNum\" color=\"linear-gradient(90deg, rgba(58,147,255,0.3) 0%, #3A93FF 100%)\" />\r\n        <ProgressBarChart title=\"函复\" :desc=\"`占提交数${replyType.letter}%`\" :percent=\"replyType.letter\"\r\n          :value=\"replyType.letterNum\" color=\"linear-gradient(90deg, rgba(255,115,141,0.3) 0%, #FF738D 100%)\" />\r\n      </div>\r\n    </div>\r\n    <!-- 办理单位统计（前十） -->\r\n    <div class=\"handle_unit_statistics_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">办理单位统计（前十）</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"handle_unit_statistics\">\r\n        <!-- 表头 -->\r\n        <div class=\"ranking_header\">\r\n          <div class=\"header_rank\">排名</div>\r\n          <div class=\"header_unit\">单位名称</div>\r\n          <div class=\"header_count\">办理数量</div>\r\n        </div>\r\n\r\n        <!-- 排名列表 -->\r\n        <div class=\"ranking_list\">\r\n          <div v-for=\"(item, index) in unitRankingList\" :key=\"index\" class=\"ranking_item\"\r\n            :class=\"{ 'top_three': index < 3 }\">\r\n            <div class=\"rank_number\">\r\n              <!-- 前三名显示图片，其他显示数字 -->\r\n              <img v-if=\"index < 3\" :src=\"getRankIcon(index + 1)\" :alt=\"`第${index + 1}名`\" class=\"rank_icon\" />\r\n              <span v-else class=\"rank_text\">{{ index + 1 }}</span>\r\n            </div>\r\n            <div class=\"unit_name\">{{ item.name }}</div>\r\n            <div class=\"handle_count\">{{ item.count }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 各专委会提案数 -->\r\n    <div class=\"committee_proposal_num_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">各专委会提案数</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"committee_proposal_num\">\r\n        <horizontalBarEcharts id=\"committeeProposalNum\" :barList=\"barList\" colorStart=\"#FFFFFF\" colorEnd=\"#EF817C\"\r\n          style=\"height: 260px;\" />\r\n      </div>\r\n    </div>\r\n    <!-- 重点提案 -->\r\n    <div class=\"key_proposal_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">重点提案</span>\r\n        </div>\r\n        <div class=\"header_right\" @click=\"openMore('notice')\">\r\n          <span class=\"header_right_more\">查看全部</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"key_proposal_list\">\r\n        <div class=\"key_proposal_item\" v-for=\"(item, idx) in keyProposalList\" :key=\"idx\">\r\n          <div class=\"key_proposal_item_title\">{{ item.title }}</div>\r\n          <div class=\"key_proposal_item_date\">{{ item.name }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 热词分析 -->\r\n    <div class=\"hot_word_analysis_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">热词分析</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"hot_word_analysis_list\">\r\n        <wordCloudEcharts id=\"wordcloud\" :wordList=\"wordCloudData\"\r\n          :colorList=\"['#1890FF', '#FF6B35', '#52C41A', '#722ED1', '#1890FF', '#FF69B4', '#52C41A', '#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']\"\r\n          :sizeRange=\"[2, 10]\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { toRefs, reactive, computed } from 'vue'\r\nimport pieEchartsLegend from './echartsComponent/pieEchartsLegend.vue'\r\nimport ProgressBarChart from './echartsComponent/ProgressBarChart.vue'\r\nimport horizontalBarEcharts from './echartsComponent/horizontalBarEcharts.vue'\r\nimport wordCloudEcharts from './echartsComponent/wordCloudEcharts.vue'\r\nexport default {\r\n  components: { pieEchartsLegend, ProgressBarChart, horizontalBarEcharts, wordCloudEcharts },\r\n  name: 'ProposalWork',\r\n  setup () {\r\n    const data = reactive({\r\n      // 提案统计数据\r\n      proposalTotal: 1500, // 提案总件数\r\n      registerTotal: 600, // 立案总件数\r\n      replyTotal: 600, // 答复总件数\r\n\r\n      // 提交情况数据\r\n      committeeProposal: 456, // 委员提案\r\n      boundaryProposal: 354, // 界别提案\r\n      organizationProposal: 221, // 组织提案\r\n\r\n      // 计算属性相关数据\r\n      circleRadius: 30, // 圆形进度条半径\r\n      circleStrokeWidth: 8, // 圆形进度条线宽\r\n      typeAnalysisList: [\r\n        { name: '发改财政', value: 22.52, color: '#3DC3F0' },\r\n        { name: '民政市场', value: 18.33, color: '#4AC6A8' },\r\n        { name: '公安司法', value: 12.5, color: '#F9C846' },\r\n        { name: '区市政府', value: 11.34, color: '#6DD3A0' },\r\n        { name: '科技工信', value: 9.56, color: '#7B8DF9' },\r\n        { name: '教育文化', value: 8.09, color: '#F97C9C' },\r\n        { name: '派出机构', value: 4.21, color: '#F9A846' },\r\n        { name: '驻青单位', value: 3.71, color: '#F97C46' },\r\n        { name: '住建交通', value: 3.65, color: '#A97CF9' },\r\n        { name: '农村卫生', value: 3.21, color: '#4A9CF9' },\r\n        { name: '其他机构', value: 1.86, color: '#BFBFBF' },\r\n        { name: '党群其他', value: 1.02, color: '#F9C8C8' }\r\n      ],\r\n      replyType: { face: 60, faceNum: 360, letter: 40, letterNum: 240 },\r\n\r\n      // 办理单位统计数据（前十）\r\n      unitRankingList: [\r\n        { name: '市教育局', count: 89 },\r\n        { name: '市民政局', count: 75 },\r\n        { name: '市劳动局', count: 70 },\r\n        { name: '市农业农村委', count: 63 },\r\n        { name: '市交通运输管理局', count: 60 },\r\n        { name: '市经济信息委', count: 50 },\r\n        { name: '市发改委', count: 46 },\r\n        { name: '市教委', count: 46 },\r\n        { name: '市林业局', count: 44 },\r\n        { name: '市规划自然资源局', count: 41 }\r\n      ],\r\n      barList: [\r\n        { name: '教育界', value: 35 },\r\n        { name: '医药卫生界', value: 15 },\r\n        { name: '经济界', value: 14 },\r\n        { name: '工商联界', value: 21 },\r\n        { name: '民革界', value: 15 },\r\n        { name: '特邀界', value: 21 },\r\n        { name: '妇联界', value: 8 },\r\n        { name: '工会界', value: 8 },\r\n        { name: '社会福利与社会保障界', value: 14 }\r\n      ],\r\n      keyProposalList: [\r\n        { id: '1', title: '关于强化社区快递物流体系基础设施建...', name: '赵国胜' },\r\n        { id: '2', title: '关于预制菜不进学校食堂的提案', name: '李毅' },\r\n        { id: '3', title: '关于上下班高峰期道路拥堵的提案', name: '王洪妮' },\r\n        { id: '4', title: '关于商讨每周工作4天振消费的提案', name: '张万强' },\r\n        { id: '5', title: '关于加强垃圾分类末端处理的提案', name: '王洪妮' }\r\n      ],\r\n      wordCloudData: [\r\n        { name: '乡村振兴', value: 180 },\r\n        { name: '就业优先', value: 165 },\r\n        { name: '科技创新', value: 150 },\r\n        { name: '改革开放', value: 135 },\r\n        { name: '依法治国', value: 120 },\r\n        { name: '教育人才', value: 105 },\r\n        { name: '社会保障', value: 90 },\r\n        { name: '热词', value: 75 },\r\n        { name: '绿色发展', value: 60 },\r\n        { name: '数字中国', value: 45 },\r\n        { name: '共同富裕', value: 40 }\r\n      ]\r\n    })\r\n\r\n    // 计算立案率\r\n    const registerRate = computed(() => {\r\n      if (data.proposalTotal === 0) return '0%'\r\n      return Math.round((data.registerTotal / data.proposalTotal) * 100) + '%'\r\n    })\r\n\r\n    // 计算答复率\r\n    const replyRate = computed(() => {\r\n      if (data.registerTotal === 0) return '0%'\r\n      return Math.round((data.replyTotal / data.registerTotal) * 100) + '%'\r\n    })\r\n\r\n    // 计算立案率圆形进度条偏移量\r\n    const registerCircleOffset = computed(() => {\r\n      const circumference = 2 * Math.PI * data.circleRadius\r\n      const percentage = data.proposalTotal === 0 ? 0 : (data.registerTotal / data.proposalTotal)\r\n      return circumference - (circumference * percentage)\r\n    })\r\n\r\n    // 计算答复率圆形进度条偏移量\r\n    const replyCircleOffset = computed(() => {\r\n      const circumference = 2 * Math.PI * data.circleRadius\r\n      const percentage = data.registerTotal === 0 ? 0 : (data.replyTotal / data.registerTotal)\r\n      return circumference - (circumference * percentage)\r\n    })\r\n\r\n    // 获取排名图标\r\n    const getRankIcon = (rank) => {\r\n      // 这里先返回占位图片路径，您可以后续替换为实际的图片路径\r\n      const iconMap = {\r\n        1: require('../../../assets/img/largeScreen/icon_rank_one.png'), // 第一名图标\r\n        2: require('../../../assets/img/largeScreen/icon_rank_two.png'), // 第二名图标\r\n        3: require('../../../assets/img/largeScreen/icon_rank_three.png') // 第三名图标\r\n      }\r\n      return iconMap[rank] || ''\r\n    }\r\n\r\n    return {\r\n      ...toRefs(data),\r\n      registerRate,\r\n      replyRate,\r\n      registerCircleOffset,\r\n      replyCircleOffset,\r\n      getRankIcon\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.ProposalWork {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .header_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 15px 0 15px;\r\n\r\n    .header_left {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_left_line {\r\n        width: 3px;\r\n        height: 14px;\r\n        background: #007AFF;\r\n      }\r\n\r\n      .header_left_title {\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #222222;\r\n        margin-left: 8px;\r\n        font-family: Source Han Serif SC, Source Han Serif SC;\r\n      }\r\n    }\r\n\r\n    .header_right {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_right_text {\r\n        font-weight: 400;\r\n        font-size: 12px;\r\n        color: #999999;\r\n      }\r\n\r\n      .header_right_more {\r\n        font-size: 14px;\r\n        color: #0271E3;\r\n        border-radius: 14px;\r\n        border: 1px solid #0271E3;\r\n        padding: 3px 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .proposal_overall_situation_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .proposal_overall_situation {\r\n      padding: 12px;\r\n\r\n      .statistics_row {\r\n        display: flex;\r\n        gap: 10px;\r\n        margin-bottom: 10px;\r\n\r\n        .statistics_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 17px 8px;\r\n          text-align: center;\r\n\r\n          .card_icon {\r\n            width: 32px;\r\n            height: 32px;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .card_label {\r\n            font-size: 12px;\r\n            color: #999;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .card_value {\r\n            font-size: 20px;\r\n\r\n            &.proposal_total_text {\r\n              color: #308FFF;\r\n            }\r\n\r\n            &.register_text {\r\n              color: #3A61CD;\r\n            }\r\n\r\n            &.reply_text {\r\n              color: #57BCAA;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .progress_row {\r\n        display: flex;\r\n        gap: 10px;\r\n        justify-content: space-between;\r\n\r\n        .progress_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 10px 16px;\r\n          background: #E8F7FF;\r\n\r\n          .progress_content {\r\n            display: flex;\r\n            align-items: center;\r\n          }\r\n\r\n          .progress_circle {\r\n            margin-right: 15px;\r\n            margin-top: 5px;\r\n          }\r\n\r\n          .progress_info {\r\n\r\n            .progress_label {\r\n              font-size: 12px;\r\n              color: #999999;\r\n              margin-bottom: 5px;\r\n            }\r\n\r\n            .progress_value {\r\n              font-size: 20px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .submit_status_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .submit_status {\r\n      padding: 12px;\r\n\r\n      .submit_statistics_row {\r\n        display: flex;\r\n        gap: 10px;\r\n\r\n        .submit_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 24px 16px;\r\n          text-align: center;\r\n\r\n          .submit_value {\r\n            font-size: 20px;\r\n            margin-bottom: 10px;\r\n\r\n            &.committee_text {\r\n              color: #3B91FB;\r\n            }\r\n\r\n            &.boundary_text {\r\n              color: #EAB308;\r\n            }\r\n\r\n            &.organization_text {\r\n              color: #43DDBB;\r\n            }\r\n          }\r\n\r\n          .submit_label {\r\n            font-size: 14px;\r\n            color: #666666;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .proposal_type_analysis_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .proposal_type_analysis {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n    }\r\n  }\r\n\r\n  .reply_type_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .reply_type {\r\n      padding: 5px 18px 18px 18px;\r\n    }\r\n  }\r\n\r\n  .handle_unit_statistics_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .handle_unit_statistics {\r\n      padding: 10px 0;\r\n\r\n      .ranking_header {\r\n        display: flex;\r\n        align-items: center;\r\n        height: 36px;\r\n        padding: 0 18px 0 5px;\r\n        background: #F1F8FF;\r\n        border-radius: 4px 4px 0 0;\r\n\r\n        .header_rank {\r\n          width: 60px;\r\n          font-size: 14px;\r\n          color: #999999;\r\n          text-align: center;\r\n        }\r\n\r\n        .header_unit {\r\n          flex: 1;\r\n          font-size: 14px;\r\n          color: #999999;\r\n          text-align: left;\r\n          padding-left: 10px;\r\n        }\r\n\r\n        .header_count {\r\n          width: 80px;\r\n          font-size: 14px;\r\n          color: #999999;\r\n          text-align: right;\r\n        }\r\n      }\r\n\r\n      .ranking_list {\r\n        .ranking_item {\r\n          display: flex;\r\n          align-items: center;\r\n          height: 36px;\r\n          padding: 0 26px 0 5px;\r\n\r\n          // 斑马纹效果：奇数行白色，偶数行浅蓝色\r\n          &:nth-child(odd) {\r\n            background: #FFFFFF;\r\n          }\r\n\r\n          &:nth-child(even) {\r\n            background: #F1F8FF;\r\n          }\r\n\r\n          .rank_number {\r\n            width: 60px;\r\n            display: flex;\r\n            justify-content: center;\r\n            align-items: center;\r\n            height: 100%;\r\n\r\n            .rank_icon {\r\n              width: 24px;\r\n              height: 25px;\r\n              object-fit: contain;\r\n            }\r\n\r\n            .rank_text {\r\n              font-size: 14px;\r\n              color: #999999;\r\n            }\r\n          }\r\n\r\n          .unit_name {\r\n            flex: 1;\r\n            font-size: 14px;\r\n            color: #333333;\r\n            padding-left: 10px;\r\n            line-height: 1;\r\n          }\r\n\r\n          .handle_count {\r\n            width: 80px;\r\n            font-size: 14px;\r\n            color: #333333;\r\n            text-align: right;\r\n            line-height: 1;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .committee_proposal_num_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .committee_proposal_num {\r\n      padding: 12px;\r\n    }\r\n  }\r\n\r\n  .key_proposal_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .key_proposal_list {\r\n      padding: 8px 15px;\r\n      background: #fff;\r\n\r\n      .key_proposal_item {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        padding: 12px 0;\r\n        border-bottom: 1px solid #f0f0f0;\r\n\r\n        &:last-child {\r\n          border-bottom: none;\r\n        }\r\n\r\n        .key_proposal_item_title {\r\n          flex: 1;\r\n          font-size: 14px;\r\n          color: #666666;\r\n          white-space: nowrap;\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n        }\r\n\r\n        .key_proposal_item_date {\r\n          font-size: 14px;\r\n          color: #666;\r\n          flex-shrink: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .hot_word_analysis_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .hot_word_analysis_list {\r\n      padding: 5px 15px 15px 15px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}