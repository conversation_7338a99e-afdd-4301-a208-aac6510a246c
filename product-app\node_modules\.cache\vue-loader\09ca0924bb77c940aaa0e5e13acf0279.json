{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue?vue&type=style&index=0&id=5c6a5fca&lang=less&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue", "mtime": 1753944475520}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5hY3Rpdml0eS10eXBlLWNoYXJ0IHsKICB3aWR0aDogMTAwJTsKICBoZWlnaHQ6IHYtYmluZChoZWlnaHQpOwp9Cg=="}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue"], "names": [], "mappings": ";AAoIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxB", "file": "D:/zy/xm/h5/qdzx_h5/product-app/src/views/SmartBrainLargeScreen/component/echartsComponent/ActivityTypeChart.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"activity-type-chart\" ref=\"chartContainer\"></div>\n</template>\n\n<script>\nimport { ref, onMounted, onUnmounted, watch } from 'vue'\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'ActivityTypeChart',\n  props: {\n    data: {\n      type: Array,\n      default: () => []\n    },\n    height: {\n      type: String,\n      default: '400px'\n    }\n  },\n  setup (props) {\n    const chartContainer = ref(null)\n    let chartInstance = null\n\n    const initChart = () => {\n      if (!chartContainer.value) return\n\n      chartInstance = echarts.init(chartContainer.value)\n\n      const option = {\n        grid: {\n          left: '25%',\n          right: '15%',\n          top: '2%',\n          bottom: '2%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'value',\n          show: false,\n          max: function (value) {\n            return Math.ceil(value.max * 1.1)\n          }\n        },\n        yAxis: {\n          type: 'category',\n          data: props.data.map(item => item.name),\n          axisLine: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            color: '#666666',\n            fontSize: 11,\n            interval: 0,\n            lineHeight: 14,\n            formatter: function (value) {\n              // 如果文字太长，进行换行处理\n              if (value.length > 10) {\n                return value.substring(0, 10) + '\\n' + value.substring(10)\n              }\n              return value\n            }\n          },\n          inverse: true // 反转Y轴，让第一项在顶部\n        },\n        series: [\n          {\n            type: 'bar',\n            data: props.data.map((item, index) => ({\n              value: item.value,\n              itemStyle: {\n                color: item.color || (index % 2 === 0 ? '#FF9999' : '#66B3FF')\n              }\n            })),\n            barHeight: 14,\n            label: {\n              show: true,\n              position: 'right',\n              color: '#333333',\n              fontSize: 11,\n              fontWeight: 500,\n              formatter: '{c}',\n              offset: [8, 0]\n            },\n            itemStyle: {\n              borderRadius: [0, 4, 4, 0]\n            }\n          }\n        ],\n        animation: true,\n        animationDuration: 1000,\n        animationEasing: 'cubicOut'\n      }\n\n      chartInstance.setOption(option)\n    }\n\n    const resizeChart = () => {\n      if (chartInstance) {\n        chartInstance.resize()\n      }\n    }\n\n    onMounted(() => {\n      initChart()\n      window.addEventListener('resize', resizeChart)\n    })\n\n    onUnmounted(() => {\n      if (chartInstance) {\n        chartInstance.dispose()\n      }\n      window.removeEventListener('resize', resizeChart)\n    })\n\n    watch(() => props.data, () => {\n      if (chartInstance) {\n        initChart()\n      }\n    }, { deep: true })\n\n    return {\n      chartContainer\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.activity-type-chart {\n  width: 100%;\n  height: v-bind(height);\n}\n</style>\n"]}]}