{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\Home.vue?vue&type=template&id=10233720&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\Home.vue", "mtime": 1753932846834}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_MapQingdao", "mapData", "_ctx", "_createCommentVNode", "_hoisted_3", "_hoisted_4", "onClick", "_cache", "$event", "openMore", "_hoisted_5", "_toDisplayString", "circles", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "cppccMemberNum", "_hoisted_10", "_hoisted_11", "_hoisted_12", "standingCommitteeNum", "_hoisted_13", "style", "_component_horizontalBarEcharts", "id", "barList", "colorStart", "colorEnd", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_Fragment", "_renderList", "proposalStatsNum", "item", "index", "key", "_normalizeStyle", "color", "value", "_hoisted_18", "label", "_hoisted_19", "_component_pieEchartsLegend", "dataList", "typeAnalysisList", "title", "_hoisted_20", "_hoisted_21", "_hoisted_22", "workDynamicsList", "idx", "_hoisted_23", "_hoisted_24", "date", "_hoisted_25", "_hoisted_26", "_hoisted_27", "background", "socialStats", "bg", "total", "_hoisted_28", "slice", "_hoisted_29", "_hoisted_30", "adoptedColor", "adopted", "_hoisted_31", "_hoisted_32", "_hoisted_33", "_hoisted_34", "meettingActivityList", "_normalizeClass", "cardClass", "src", "icon", "_hoisted_36", "_hoisted_37", "label1", "value1Style", "value1", "_hoisted_38", "label2", "value2Style", "value2", "_hoisted_39", "_hoisted_40", "discussionsList", "backgroundImage", "_hoisted_41", "number", "_hoisted_42", "unit", "_hoisted_43", "_hoisted_44", "_hoisted_45", "hotTopics", "topic", "_hoisted_46", "_hoisted_47", "_hoisted_48", "_hoisted_49", "_hoisted_50", "_hoisted_51", "performanceStatistics", "name", "meeting", "proposal", "opinion"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\Home.vue"], "sourcesContent": ["<template>\n  <div class=\"home_page\">\n    <div class=\"map_section\">\n      <MapQingdao :mapData=\"mapData\" />\n    </div>\n    <!-- 委员统计 -->\n    <div class=\"home_committee_statistics\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">委员统计</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">{{ circles }}</span>\n        </div>\n      </div>\n      <div class=\"committee_statistics_box\">\n        <div class=\"statistics_card card_blue\">\n          <div class=\"card_content\">\n            <div class=\"card_number\">{{ cppccMemberNum }}</div>\n            <div class=\"card_label\">政协委员(人)</div>\n          </div>\n        </div>\n        <div class=\"statistics_card card_yellow\">\n          <div class=\"card_content\">\n            <div class=\"card_number\">{{ standingCommitteeNum }}</div>\n            <div class=\"card_label\">政协常委(人)</div>\n          </div>\n        </div>\n      </div>\n      <div class=\"circles_box\">\n        <div class=\"circles_top_header\">\n          <span class=\"circles_top_header_title\">界别分布</span>\n          <span class=\"circles_top_header_more\" style=\"\">查看全部</span>\n        </div>\n        <horizontalBarEcharts id=\"circles\" :barList=\"barList\" colorStart=\"#FFFFFF\" colorEnd=\"#EF817C\"\n          style=\"height: 260px;\" />\n      </div>\n    </div>\n    <!-- 提案统计 -->\n    <div class=\"home_proposal_statistics\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">提案统计</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">{{ circles }}</span>\n        </div>\n      </div>\n      <div class=\"proposal_statistics_box\">\n        <div class=\"proposal_card\" v-for=\"(item, index) in proposalStatsNum\" :key=\"index\">\n          <div class=\"proposal_number\" :style=\"{ color: item.color }\">{{ item.value }}</div>\n          <div class=\"proposal_label\">{{ item.label }}</div>\n        </div>\n      </div>\n      <div class=\"proposal_type_analysis\">\n        <pieEchartsLegend id=\"typeAnalysisPie\" :dataList=\"typeAnalysisList\" title=\"类型分析\" />\n      </div>\n    </div>\n    <!-- 工作动态 -->\n    <div class=\"home_work_dynamics\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">工作动态</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">本年</span>\n        </div>\n      </div>\n      <div class=\"work_dynamics_list\">\n        <div class=\"work_dynamics_item\" v-for=\"(item, idx) in workDynamicsList\" :key=\"idx\">\n          <div class=\"work_dynamics_title\">{{ item.title }}</div>\n          <div class=\"work_dynamics_date\">{{ item.date }}</div>\n        </div>\n      </div>\n    </div>\n    <!-- 社情民意 -->\n    <div class=\"home_social\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">社情民意</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">本年</span>\n        </div>\n      </div>\n      <div class=\"social_box\">\n        <!-- 总数卡片 -->\n        <div class=\"social_card total_card\" :style=\"{ background: socialStats[0].bg }\">\n          <div class=\"total_card_number\" :style=\"{ color: socialStats[0].color }\">{{ socialStats[0].total }}</div>\n          <div class=\"total_card_label\">{{ socialStats[0].label }}</div>\n        </div>\n        <!-- 报送卡片 -->\n        <div class=\"social_card report_card\" v-for=\"(item, idx) in socialStats.slice(1)\" :key=\"idx\"\n          :style=\"{ background: item.bg }\">\n          <div class=\"report_row\">\n            <span class=\"report_label\">总数</span>\n            <span class=\"report_total\" :style=\"{ color: item.color }\">{{ item.total }}</span>\n          </div>\n          <div class=\"report_row\">\n            <span class=\"report_label\">采用</span>\n            <span class=\"report_adopted\" :style=\"{ color: item.adoptedColor }\">{{ item.adopted }}</span>\n          </div>\n          <div class=\"report_card_label\">{{ item.label }}</div>\n        </div>\n      </div>\n    </div>\n    <!-- 会议活动 -->\n    <div class=\"home_meetting_activity\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">会议活动</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">本年</span>\n        </div>\n      </div>\n      <div class=\"meetting_activity_box\">\n        <div v-for=\"(item, idx) in meettingActivityList\" :key=\"idx\" class=\"meetting_activity_card\"\n          :class=\"item.cardClass\">\n          <img :src=\"item.icon\" class=\"activity_card_iconimg\" />\n          <div class=\"meetting_activity_card_content\">\n            <div class=\"meetting_activity_card_label\">{{ item.label1 }}</div>\n            <div class=\"meetting_activity_card_value\" :style=\"item.value1Style\">{{ item.value1 }}</div>\n            <div class=\"meetting_activity_card_label\">{{ item.label2 }}</div>\n            <div class=\"meetting_activity_card_value\" :style=\"item.value2Style\">{{ item.value2 }}</div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <!-- 网络议政 -->\n    <div class=\"home_discussions\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">网络议政</span>\n        </div>\n      </div>\n      <div class=\"discussions_box\">\n        <div class=\"discussion_card\" v-for=\"(item, idx) in discussionsList\" :key=\"idx\"\n          :style=\"{ backgroundImage: `url(${item.bg})` }\">\n          <div class=\"discussion_card_number\">{{ item.number }}<span class=\"discussion_card_unit\">{{ item.unit }}</span>\n          </div>\n          <div class=\"discussion_card_label\">{{ item.label }}</div>\n        </div>\n      </div>\n      <div class=\"hot_topics\">\n        <div class=\"hot_topics_title\">最热话题</div>\n        <div class=\"hot_topics_list\">\n          <div class=\"hot_topic_item\" v-for=\"(topic, idx) in hotTopics\" :key=\"idx\">\n            <span class=\"hot_topic_index\" :class=\"'hot_topic_index_' + (idx + 1)\">{{ idx + 1 }}</span>\n            <span class=\"hot_topic_text\">{{ topic.title }}</span>\n            <span class=\"hot_topic_tag\" :class=\"'hot_topic_tag_' + (idx + 1)\">热</span>\n          </div>\n        </div>\n      </div>\n    </div>\n    <!-- 履职统计 -->\n    <div class=\"home_performance_statistics\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">履职统计</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">{{ circles }}</span>\n        </div>\n      </div>\n      <div class=\"performance_statistics_box\">\n        <div class=\"performance_table\">\n          <div class=\"performance_table_header\">\n            <span>姓名</span>\n            <span>会议活动</span>\n            <span>政协提案</span>\n            <span>社情民意</span>\n          </div>\n          <div class=\"performance_table_row\" v-for=\"(item, idx) in performanceStatistics\" :key=\"item.name\"\n            :class=\"{ 'row-alt': idx % 2 === 1 }\">\n            <span>{{ item.name }}</span>\n            <span>{{ item.meeting }}</span>\n            <span>{{ item.proposal }}</span>\n            <span>{{ item.opinion }}</span>\n          </div>\n          <div class=\"performance_table_footer\">\n            <button class=\"view-all-btn\">查看全部</button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n<script>\nimport { toRefs, reactive } from 'vue'\nimport MapQingdao from './echartsComponent/MapQingdao.vue'\nimport horizontalBarEcharts from './echartsComponent/horizontalBarEcharts.vue'\nimport pieEchartsLegend from './echartsComponent/pieEchartsLegend.vue'\n\nexport default {\n  components: { MapQingdao, horizontalBarEcharts, pieEchartsLegend },\n  setup () {\n    const data = reactive({\n      mapData: [\n        { name: '市南区', value: 80 },\n        { name: '市北区', value: 60 },\n        { name: '李沧区', value: 50 },\n        { name: '崂山区', value: 40 },\n        { name: '城阳区', value: 70 },\n        { name: '黄岛区', value: 90 },\n        { name: '即墨区', value: 30 },\n        { name: '胶州市', value: 55 },\n        { name: '平度市', value: 20 },\n        { name: '莱西市', value: 10 }\n      ],\n      circles: '十一届二次',\n      cppccMemberNum: '10095',\n      standingCommitteeNum: '8742',\n      barList: [\n        { name: '教育界', value: 35 },\n        { name: '医药卫生界', value: 15 },\n        { name: '经济界', value: 14 },\n        { name: '工商联界', value: 21 },\n        { name: '民革界', value: 15 },\n        { name: '特邀界', value: 21 },\n        { name: '妇联界', value: 8 },\n        { name: '工会界', value: 8 },\n        { name: '社会福利与社会保障界', value: 14 }\n      ],\n      proposalStatsNum: [\n        { value: 873, label: '提案总数', color: '#2386F9' },\n        { value: 456, label: '委员提案', color: '#2CA6F9' },\n        { value: 354, label: '界别提案', color: '#3AC86B' },\n        { value: 221, label: '组织提案', color: '#F96C9C' }\n      ],\n      typeAnalysisList: [\n        { name: '发改财政', value: 22.52, color: '#3DC3F0' },\n        { name: '民政市场', value: 18.33, color: '#4AC6A8' },\n        { name: '公安司法', value: 12.5, color: '#F9C846' },\n        { name: '区市政府', value: 11.34, color: '#6DD3A0' },\n        { name: '科技工信', value: 9.56, color: '#7B8DF9' },\n        { name: '教育文化', value: 8.09, color: '#F97C9C' },\n        { name: '派出机构', value: 4.21, color: '#F9A846' },\n        { name: '驻青单位', value: 3.71, color: '#F97C46' },\n        { name: '住建交通', value: 3.65, color: '#A97CF9' },\n        { name: '农村卫生', value: 3.21, color: '#4A9CF9' },\n        { name: '其他机构', value: 1.86, color: '#BFBFBF' },\n        { name: '党群其他', value: 1.02, color: '#F9C8C8' }\n      ],\n      workDynamicsList: [\n        { title: '市政协社会和法制工作办公室围绕市政协社会和法制工作办公室围绕', date: '2025-06-03' },\n        { title: '“与民同行 共创共赢”新格局下民市政协社会和法制工作办公室围绕', date: '2025-05-30' },\n        { title: '“惠民生·基层行”义诊活动温暖人心市政协社会和法制工作办公室围绕', date: '2025-05-30' },\n        { title: '市科技局面复市政协科技界别提案市政协社会和法制工作办公室围绕', date: '2025-05-30' },\n        { title: '孟庆斌到胶州市、崂山区调研项目时市政协社会和法制工作办公室围绕', date: '2025-05-29' }\n      ],\n      socialStats: [\n        { total: 1057, label: '总数', bg: '#EFF6FF', color: '#2386F9' },\n        { total: 345, adopted: 21, label: '委员报送', bg: '#FDF8F0', color: '#2386F9', adoptedColor: '#F9C846' },\n        { total: 547, adopted: 79, label: '单位报送', bg: '#F0FDF4', color: '#3AC86B', adoptedColor: '#F9C846' }\n      ],\n      meettingActivityList: [\n        {\n          cardClass: 'card_meeting',\n          icon: require('../../../assets/img/largeScreen/icon_meetting.png'),\n          label1: '会议次数',\n          value1: 201,\n          value1Style: { color: '#308FFF' },\n          label2: '会议人数',\n          value2: 2412,\n          value2Style: { color: '#308FFF' }\n        },\n        {\n          cardClass: 'card_activity',\n          icon: require('../../../assets/img/largeScreen/icon_acticity.png'),\n          label1: '活动次数',\n          value1: 310,\n          value1Style: { color: '#1FC6FF' },\n          label2: '活动人数',\n          value2: 4015,\n          value2Style: { color: '#1FC6FF' }\n        }\n      ],\n      discussionsList: [\n        {\n          bg: require('../../../assets/img/largeScreen/icon_release_bg.png'),\n          number: '72',\n          unit: '个',\n          label: '发布议题',\n          desc: ''\n        },\n        {\n          bg: require('../../../assets/img/largeScreen/icon_participate_bg.png'),\n          number: '39301',\n          unit: '次',\n          label: '累计参与人次',\n          desc: ''\n        },\n        {\n          bg: require('../../../assets/img/largeScreen/icon_seek_bg.png'),\n          number: '12308',\n          unit: '条',\n          label: '累计征求意见',\n          desc: ''\n        }\n      ],\n      hotTopics: [\n        { title: '推进黄河国家文化公园建设' },\n        { title: '持续推进黄河流域生态保护修复，助力…' },\n        { title: '全面加强新时代中小学劳动教育' }\n      ],\n      performanceStatistics: [\n        { name: '马平安', meeting: 515, proposal: 15, opinion: 0 },\n        { name: '马波', meeting: 400, proposal: 0, opinion: 12 },\n        { name: '王玉民', meeting: 490, proposal: 15, opinion: 0 },\n        { name: '王洋宝', meeting: 500, proposal: 0, opinion: 1 },\n        { name: '王忠', meeting: 420, proposal: 0, opinion: 2 },\n        { name: '刘彩霞', meeting: 512, proposal: 0, opinion: 1 },\n        { name: '刘军', meeting: 500, proposal: 20, opinion: 0 },\n        { name: '吴雪玲', meeting: 315, proposal: 15, opinion: 38 },\n        { name: '杨文比', meeting: 310, proposal: 60, opinion: 28 },\n        { name: '贾谊', meeting: 540, proposal: 9, opinion: 13 }\n      ]\n    })\n    return { ...toRefs(data) }\n  }\n}\n</script>\n<style lang=\"less\" scoped>\n.home_page {\n  width: 100%;\n  height: 100%;\n\n  .header_box {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 15px 15px 0 15px;\n\n    .header_left {\n      display: flex;\n      align-items: center;\n\n      .header_left_line {\n        width: 3px;\n        height: 14px;\n        background: #007AFF;\n      }\n\n      .header_left_title {\n        font-weight: bold;\n        font-size: 15px;\n        color: #222222;\n        margin-left: 8px;\n        font-family: Source Han Serif SC, Source Han Serif SC;\n      }\n    }\n\n    .header_right {\n      display: flex;\n      align-items: center;\n\n      .header_right_text {\n        font-weight: 400;\n        font-size: 12px;\n        color: #999999;\n      }\n    }\n  }\n\n  .map_section {\n    background: #fff;\n    border-radius: 8px;\n    padding: 10px;\n  }\n\n  .home_committee_statistics {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .committee_statistics_box {\n      display: flex;\n      gap: 15px;\n      padding: 20px 15px 10px 15px;\n\n      .statistics_card {\n        flex: 1;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: #f5faff;\n        position: relative;\n        height: 86px;\n\n        .card_content {\n          display: flex;\n          flex-direction: column;\n          align-items: flex-start;\n          justify-content: center;\n          margin-left: 55px;\n          margin-top: 5px;\n\n          .card_number {\n            font-size: 20px;\n            color: #4AA3FF;\n          }\n\n          .card_label {\n            font-size: 14px;\n            color: #666;\n            margin-top: 2px;\n          }\n        }\n      }\n\n      .card_blue {\n        background-image: url('../../../assets/img/largeScreen/icon_member_bg.png');\n        background-size: 100% 100%;\n        background-position: center;\n      }\n\n      .card_yellow {\n        background-image: url('../../../assets/img/largeScreen/icon_committee_bg.png');\n        background-size: 100% 100%;\n        background-position: center;\n\n        .card_number {\n          color: #E6B800 !important;\n        }\n      }\n    }\n\n    .circles_box {\n      border-radius: 6px;\n      margin: 10px 12px;\n\n      .circles_top_header {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n\n        .circles_top_header_title {\n          font-size: 14px;\n          color: #000;\n          font-family: Source Han Serif SC, Source Han Serif SC;\n        }\n\n        .circles_top_header_more {\n          font-size: 14px;\n          color: #0271E3;\n          border-radius: 14px;\n          border: 1px solid #0271E3;\n          padding: 3px 10px;\n        }\n      }\n    }\n  }\n\n  .home_proposal_statistics {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .proposal_statistics_box {\n      display: flex;\n      justify-content: space-between;\n      padding: 20px 15px 10px 15px;\n\n      .proposal_card {\n        flex: 1;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n\n        .proposal_number {\n          font-size: 24px;\n        }\n\n        .proposal_label {\n          font-size: 14px;\n          color: #999;\n          margin-top: 4px;\n        }\n      }\n    }\n\n    .proposal_type_analysis {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n    }\n  }\n\n  .home_work_dynamics {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .work_dynamics_list {\n      padding: 8px 15px;\n      background: #fff;\n\n      .work_dynamics_item {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        padding: 12px 0;\n        border-bottom: 1px solid #f0f0f0;\n\n        &:last-child {\n          border-bottom: none;\n        }\n\n        .work_dynamics_title {\n          flex: 1;\n          font-size: 14px;\n          color: #666666;\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n        }\n\n        .work_dynamics_date {\n          font-size: 14px;\n          color: #bdbdbd;\n          flex-shrink: 0;\n        }\n      }\n    }\n  }\n\n  .home_social {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .social_box {\n      display: flex;\n      gap: 16px;\n      padding: 20px 15px;\n\n      .social_card {\n        flex: 1;\n        width: 97px;\n        height: 94px;\n        border-radius: 10px;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        box-sizing: border-box;\n        box-shadow: none;\n        padding: 15px;\n        background-clip: padding-box;\n      }\n\n      .total_card {\n        justify-content: center;\n\n        .total_card_number {\n          font-size: 20px;\n          color: #3B91FB;\n          margin-bottom: 5px;\n        }\n\n        .total_card_label {\n          font-size: 14px;\n          color: #666666;\n        }\n      }\n\n      .report_card {\n        justify-content: flex-start;\n\n        .report_row {\n          width: 100%;\n          display: flex;\n          justify-content: space-between;\n          align-items: baseline;\n          margin-bottom: 2px;\n\n          .report_label {\n            font-size: 14px;\n            color: #999;\n            margin-right: 2px;\n          }\n\n          .report_total {\n            font-size: 15px;\n          }\n\n          .report_adopted {\n            font-size: 15px;\n          }\n        }\n\n        .report_card_label {\n          margin-top: 5px;\n          font-size: 15px;\n          color: #666;\n          font-family: Source Han Serif SC, Source Han Serif SC;\n        }\n      }\n    }\n  }\n\n  .home_meetting_activity {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .meetting_activity_box {\n      display: flex;\n      gap: 16px;\n      padding: 20px 15px;\n\n      .meetting_activity_card {\n        flex: 1;\n        display: flex;\n        align-items: flex-start;\n        box-sizing: border-box;\n        width: 157px;\n        height: 140px;\n        padding: 14px 20px;\n\n        .activity_card_iconimg {\n          width: 32px;\n          height: 32px;\n          margin-right: 15px;\n          margin-top: 4px;\n        }\n\n        .meetting_activity_card_content {\n          display: flex;\n          flex-direction: column;\n          justify-content: center;\n          flex: 1;\n\n          .meetting_activity_card_label {\n            font-size: 14px;\n            color: #999;\n            margin-bottom: 5px;\n          }\n\n          .meetting_activity_card_value {\n            font-size: 20px;\n            color: #308FFF;\n            margin-bottom: 8px;\n          }\n        }\n      }\n\n      .card_meeting {\n        background-image: url('../../../assets/img/largeScreen/icon_meetting_bg.png');\n        background-size: 100% 100%;\n        background-position: center;\n      }\n\n      .card_activity {\n        background-image: url('../../../assets/img/largeScreen/icon_activity_bg.png');\n        background-size: 100% 100%;\n        background-position: center;\n      }\n    }\n  }\n\n  .home_discussions {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .discussions_box {\n      display: flex;\n      gap: 10px;\n      padding: 20px 15px;\n      justify-content: flex-start;\n\n      .discussion_card {\n        flex: 1;\n        width: 103px;\n        height: 77px;\n        background-size: 100% 100%;\n        background-position: center;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        color: #fff;\n\n        .discussion_card_number {\n          font-size: 20px;\n          margin-bottom: 2px;\n\n          .discussion_card_unit {\n            font-size: 12px;\n            font-weight: normal;\n            margin-left: 2px;\n          }\n        }\n\n        .discussion_card_label {\n          font-size: 14px;\n          font-weight: 400;\n          margin-bottom: 2px;\n        }\n      }\n    }\n\n    .hot_topics {\n      padding: 0 15px 15px 15px;\n\n      .hot_topics_title {\n        font-size: 14px;\n        color: #000;\n        margin-bottom: 10px;\n        font-family: Source Han Serif SC, Source Han Serif SC;\n      }\n\n      .hot_topics_list {\n        .hot_topic_item {\n          display: flex;\n          align-items: center;\n          border-bottom: 1px solid #f0f0f0;\n          padding: 12px 0;\n\n          .hot_topic_index {\n            font-size: 14px;\n            margin-right: 10px;\n          }\n\n          .hot_topic_index_1 {\n            color: #FF4D4F;\n          }\n\n          .hot_topic_index_2 {\n            color: #FF9900;\n          }\n\n          .hot_topic_index_3 {\n            color: #FFD600;\n          }\n\n          .hot_topic_text {\n            flex: 1;\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            font-size: 14px;\n            color: #666;\n          }\n\n          .hot_topic_tag {\n            font-size: 14px;\n            color: #fff;\n            border-radius: 2px;\n            width: 22px;\n            height: 22px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n          }\n\n          .hot_topic_tag_1 {\n            background: #FB3030;\n          }\n\n          .hot_topic_tag_2 {\n            background: #FF833E;\n          }\n\n          .hot_topic_tag_3 {\n            background: #FFD978;\n          }\n        }\n      }\n    }\n  }\n\n  .home_performance_statistics {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .performance_statistics_box {\n      margin-top: 15px;\n\n      .performance_table {\n        width: 100%;\n        background: #fff;\n\n        .performance_table_header,\n        .performance_table_row {\n          display: flex;\n          align-items: center;\n          padding: 8px 0;\n\n          span {\n            flex: 1;\n            text-align: center;\n          }\n        }\n\n        .performance_table_header {\n          background: #F1F8FF;\n          font-weight: bold;\n          color: #222;\n          font-size: 14px;\n        }\n\n        .performance_table_row {\n          background: #fff;\n          color: #222;\n          font-size: 14px;\n\n          &.row-alt {\n            background: #F1F8FF;\n          }\n        }\n\n        .performance_table_footer {\n          display: flex;\n          justify-content: center;\n          padding: 10px 0;\n          background: #fff;\n\n          .view-all-btn {\n            border: 1px solid #0271e3;\n            color: #0271e3;\n            background: #fff;\n            border-radius: 16px;\n            padding: 4px 14px;\n            font-size: 14px;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAAY;;EAMbA,KAAK,EAAC;AAAmB;;EAG9BA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAa;;EAIvBA,KAAK,EAAC;AAA6B;;EACjCA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAa;;EAKzBA,KAAK,EAAC;AAAa;;EAUrBA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAAY;;EAMbA,KAAK,EAAC;AAAmB;;EAG9BA,KAAK,EAAC;AAAyB;;EAG3BA,KAAK,EAAC;AAAgB;;EAG1BA,KAAK,EAAC;AAAwB;;EAKhCA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAY;;EASlBA,KAAK,EAAC;AAAoB;;EAEtBA,KAAK,EAAC;AAAqB;;EAC3BA,KAAK,EAAC;AAAoB;;EAKhCA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAY;;EASlBA,KAAK,EAAC;AAAY;;EAIdA,KAAK,EAAC;AAAkB;;EAKxBA,KAAK,EAAC;AAAY;;EAIlBA,KAAK,EAAC;AAAY;;EAIlBA,KAAK,EAAC;AAAmB;;EAK/BA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAAY;;EASlBA,KAAK,EAAC;AAAuB;;;EAIzBA,KAAK,EAAC;AAAgC;;EACpCA,KAAK,EAAC;AAA8B;;EAEpCA,KAAK,EAAC;AAA8B;;EAO5CA,KAAK,EAAC;AAAkB;;EAOtBA,KAAK,EAAC;AAAiB;;EAGnBA,KAAK,EAAC;AAAwB;;EAAwBA,KAAK,EAAC;AAAsB;;EAElFA,KAAK,EAAC;AAAuB;;EAGjCA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAiB;;EAGlBA,KAAK,EAAC;AAAgB;;EAO/BA,KAAK,EAAC;AAA6B;;EACjCA,KAAK,EAAC;AAAY;;EAMbA,KAAK,EAAC;AAAmB;;EAG9BA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAAmB;;;;;uBA5KpCC,mBAAA,CAgMM,OAhMNC,UAgMM,GA/LJC,mBAAA,CAEM,OAFNC,UAEM,GADJC,YAAA,CAAiCC,qBAAA;IAApBC,OAAO,EAAEC,IAAA,CAAAD;EAAO,qC,GAE/BE,mBAAA,UAAa,EACbN,mBAAA,CAgCM,OAhCNO,UAgCM,GA/BJP,mBAAA,CAQM,OARNQ,UAQM,G,0BAPJR,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAa,IACtBG,mBAAA,CAAsC;IAAhCH,KAAK,EAAC;EAAkB,IAC9BG,mBAAA,CAA2C;IAArCH,KAAK,EAAC;EAAmB,GAAC,MAAI,E,sBAEtCG,mBAAA,CAEM;IAFDH,KAAK,EAAC,cAAc;IAAEY,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEN,IAAA,CAAAO,QAAQ;MACxCZ,mBAAA,CAAoD,QAApDa,UAAoD,EAAAC,gBAAA,CAAjBT,IAAA,CAAAU,OAAO,iB,KAG9Cf,mBAAA,CAaM,OAbNgB,UAaM,GAZJhB,mBAAA,CAKM,OALNiB,UAKM,GAJJjB,mBAAA,CAGM,OAHNkB,UAGM,GAFJlB,mBAAA,CAAmD,OAAnDmB,UAAmD,EAAAL,gBAAA,CAAvBT,IAAA,CAAAe,cAAc,kB,0BAC1CpB,mBAAA,CAAqC;IAAhCH,KAAK,EAAC;EAAY,GAAC,SAAO,qB,KAGnCG,mBAAA,CAKM,OALNqB,WAKM,GAJJrB,mBAAA,CAGM,OAHNsB,WAGM,GAFJtB,mBAAA,CAAyD,OAAzDuB,WAAyD,EAAAT,gBAAA,CAA7BT,IAAA,CAAAmB,oBAAoB,kB,0BAChDxB,mBAAA,CAAqC;IAAhCH,KAAK,EAAC;EAAY,GAAC,SAAO,qB,OAIrCG,mBAAA,CAOM,OAPNyB,WAOM,G,0BANJzB,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAoB,IAC7BG,mBAAA,CAAkD;IAA5CH,KAAK,EAAC;EAA0B,GAAC,MAAI,GAC3CG,mBAAA,CAA0D;IAApDH,KAAK,EAAC,yBAAyB;IAAC6B,KAAQ,EAAR;KAAS,MAAI,E,sBAErDxB,YAAA,CAC2ByB,+BAAA;IADLC,EAAE,EAAC,SAAS;IAAEC,OAAO,EAAExB,IAAA,CAAAwB,OAAO;IAAEC,UAAU,EAAC,SAAS;IAACC,QAAQ,EAAC,SAAS;IAC3FL,KAAsB,EAAtB;MAAA;IAAA;4CAGNpB,mBAAA,UAAa,EACbN,mBAAA,CAmBM,OAnBNgC,WAmBM,GAlBJhC,mBAAA,CAQM,OARNiC,WAQM,G,4BAPJjC,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAa,IACtBG,mBAAA,CAAsC;IAAhCH,KAAK,EAAC;EAAkB,IAC9BG,mBAAA,CAA2C;IAArCH,KAAK,EAAC;EAAmB,GAAC,MAAI,E,sBAEtCG,mBAAA,CAEM;IAFDH,KAAK,EAAC,cAAc;IAAEY,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEN,IAAA,CAAAO,QAAQ;MACxCZ,mBAAA,CAAoD,QAApDkC,WAAoD,EAAApB,gBAAA,CAAjBT,IAAA,CAAAU,OAAO,iB,KAG9Cf,mBAAA,CAKM,OALNmC,WAKM,I,kBAJJrC,mBAAA,CAGMsC,SAAA,QAAAC,WAAA,CAH6ChC,IAAA,CAAAiC,gBAAgB,GAAhCC,IAAI,EAAEC,KAAK;yBAA9C1C,mBAAA,CAGM;MAHDD,KAAK,EAAC,eAAe;MAA4C4C,GAAG,EAAED;QACzExC,mBAAA,CAAkF;MAA7EH,KAAK,EAAC,iBAAiB;MAAE6B,KAAK,EAAAgB,eAAA;QAAAC,KAAA,EAAWJ,IAAI,CAACI;MAAK;wBAAOJ,IAAI,CAACK,KAAK,yBACzE5C,mBAAA,CAAkD,OAAlD6C,WAAkD,EAAA/B,gBAAA,CAAnByB,IAAI,CAACO,KAAK,iB;oCAG7C9C,mBAAA,CAEM,OAFN+C,WAEM,GADJ7C,YAAA,CAAmF8C,2BAAA;IAAjEpB,EAAE,EAAC,iBAAiB;IAAEqB,QAAQ,EAAE5C,IAAA,CAAA6C,gBAAgB;IAAEC,KAAK,EAAC;6CAG9E7C,mBAAA,UAAa,EACbN,mBAAA,CAgBM,OAhBNoD,WAgBM,GAfJpD,mBAAA,CAQM,OARNqD,WAQM,G,4BAPJrD,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAa,IACtBG,mBAAA,CAAsC;IAAhCH,KAAK,EAAC;EAAkB,IAC9BG,mBAAA,CAA2C;IAArCH,KAAK,EAAC;EAAmB,GAAC,MAAI,E,sBAEtCG,mBAAA,CAEM;IAFDH,KAAK,EAAC,cAAc;IAAEY,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEN,IAAA,CAAAO,QAAQ;kCACxCZ,mBAAA,CAAyC;IAAnCH,KAAK,EAAC;EAAmB,GAAC,IAAE,oB,MAGtCG,mBAAA,CAKM,OALNsD,WAKM,I,kBAJJxD,mBAAA,CAGMsC,SAAA,QAAAC,WAAA,CAHgDhC,IAAA,CAAAkD,gBAAgB,GAA9BhB,IAAI,EAAEiB,GAAG;yBAAjD1D,mBAAA,CAGM;MAHDD,KAAK,EAAC,oBAAoB;MAA0C4C,GAAG,EAAEe;QAC5ExD,mBAAA,CAAuD,OAAvDyD,WAAuD,EAAA3C,gBAAA,CAAnByB,IAAI,CAACY,KAAK,kBAC9CnD,mBAAA,CAAqD,OAArD0D,WAAqD,EAAA5C,gBAAA,CAAlByB,IAAI,CAACoB,IAAI,iB;sCAIlDrD,mBAAA,UAAa,EACbN,mBAAA,CA8BM,OA9BN4D,WA8BM,GA7BJ5D,mBAAA,CAQM,OARN6D,WAQM,G,4BAPJ7D,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAa,IACtBG,mBAAA,CAAsC;IAAhCH,KAAK,EAAC;EAAkB,IAC9BG,mBAAA,CAA2C;IAArCH,KAAK,EAAC;EAAmB,GAAC,MAAI,E,sBAEtCG,mBAAA,CAEM;IAFDH,KAAK,EAAC,cAAc;IAAEY,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEN,IAAA,CAAAO,QAAQ;kCACxCZ,mBAAA,CAAyC;IAAnCH,KAAK,EAAC;EAAmB,GAAC,IAAE,oB,MAGtCG,mBAAA,CAmBM,OAnBN8D,WAmBM,GAlBJxD,mBAAA,UAAa,EACbN,mBAAA,CAGM;IAHDH,KAAK,EAAC,wBAAwB;IAAE6B,KAAK,EAAAgB,eAAA;MAAAqB,UAAA,EAAgB1D,IAAA,CAAA2D,WAAW,IAAIC;IAAE;MACzEjE,mBAAA,CAAwG;IAAnGH,KAAK,EAAC,mBAAmB;IAAE6B,KAAK,EAAAgB,eAAA;MAAAC,KAAA,EAAWtC,IAAA,CAAA2D,WAAW,IAAIrB;IAAK;sBAAOtC,IAAA,CAAA2D,WAAW,IAAIE,KAAK,yBAC/FlE,mBAAA,CAA8D,OAA9DmE,WAA8D,EAAArD,gBAAA,CAA7BT,IAAA,CAAA2D,WAAW,IAAIlB,KAAK,iB,kBAEvDxC,mBAAA,UAAa,G,kBACbR,mBAAA,CAWMsC,SAAA,QAAAC,WAAA,CAXqDhC,IAAA,CAAA2D,WAAW,CAACI,KAAK,MAA/B7B,IAAI,EAAEiB,GAAG;yBAAtD1D,mBAAA,CAWM;MAXDD,KAAK,EAAC,yBAAyB;MAA8C4C,GAAG,EAAEe,GAAG;MACvF9B,KAAK,EAAAgB,eAAA;QAAAqB,UAAA,EAAgBxB,IAAI,CAAC0B;MAAE;QAC7BjE,mBAAA,CAGM,OAHNqE,WAGM,G,4BAFJrE,mBAAA,CAAoC;MAA9BH,KAAK,EAAC;IAAc,GAAC,IAAE,sBAC7BG,mBAAA,CAAiF;MAA3EH,KAAK,EAAC,cAAc;MAAE6B,KAAK,EAAAgB,eAAA;QAAAC,KAAA,EAAWJ,IAAI,CAACI;MAAK;wBAAOJ,IAAI,CAAC2B,KAAK,wB,GAEzElE,mBAAA,CAGM,OAHNsE,WAGM,G,4BAFJtE,mBAAA,CAAoC;MAA9BH,KAAK,EAAC;IAAc,GAAC,IAAE,sBAC7BG,mBAAA,CAA4F;MAAtFH,KAAK,EAAC,gBAAgB;MAAE6B,KAAK,EAAAgB,eAAA;QAAAC,KAAA,EAAWJ,IAAI,CAACgC;MAAY;wBAAOhC,IAAI,CAACiC,OAAO,wB,GAEpFxE,mBAAA,CAAqD,OAArDyE,WAAqD,EAAA3D,gBAAA,CAAnByB,IAAI,CAACO,KAAK,iB;sCAIlDxC,mBAAA,UAAa,EACbN,mBAAA,CAsBM,OAtBN0E,WAsBM,GArBJ1E,mBAAA,CAQM,OARN2E,WAQM,G,4BAPJ3E,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAa,IACtBG,mBAAA,CAAsC;IAAhCH,KAAK,EAAC;EAAkB,IAC9BG,mBAAA,CAA2C;IAArCH,KAAK,EAAC;EAAmB,GAAC,MAAI,E,sBAEtCG,mBAAA,CAEM;IAFDH,KAAK,EAAC,cAAc;IAAEY,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEN,IAAA,CAAAO,QAAQ;kCACxCZ,mBAAA,CAAyC;IAAnCH,KAAK,EAAC;EAAmB,GAAC,IAAE,oB,MAGtCG,mBAAA,CAWM,OAXN4E,WAWM,I,kBAVJ9E,mBAAA,CASMsC,SAAA,QAAAC,WAAA,CATqBhC,IAAA,CAAAwE,oBAAoB,GAAlCtC,IAAI,EAAEiB,GAAG;yBAAtB1D,mBAAA,CASM;MAT4C2C,GAAG,EAAEe,GAAG;MAAE3D,KAAK,EAAAiF,eAAA,EAAC,wBAAwB,EAChFvC,IAAI,CAACwC,SAAS;QACtB/E,mBAAA,CAAsD;MAAhDgF,GAAG,EAAEzC,IAAI,CAAC0C,IAAI;MAAEpF,KAAK,EAAC;0CAC5BG,mBAAA,CAKM,OALNkF,WAKM,GAJJlF,mBAAA,CAAiE,OAAjEmF,WAAiE,EAAArE,gBAAA,CAApByB,IAAI,CAAC6C,MAAM,kBACxDpF,mBAAA,CAA2F;MAAtFH,KAAK,EAAC,8BAA8B;MAAE6B,KAAK,EAAAgB,eAAA,CAAEH,IAAI,CAAC8C,WAAW;wBAAK9C,IAAI,CAAC+C,MAAM,yBAClFtF,mBAAA,CAAiE,OAAjEuF,WAAiE,EAAAzE,gBAAA,CAApByB,IAAI,CAACiD,MAAM,kBACxDxF,mBAAA,CAA2F;MAAtFH,KAAK,EAAC,8BAA8B;MAAE6B,KAAK,EAAAgB,eAAA,CAAEH,IAAI,CAACkD,WAAW;wBAAKlD,IAAI,CAACmD,MAAM,wB;sCAK1FpF,mBAAA,UAAa,EACbN,mBAAA,CAyBM,OAzBN2F,WAyBM,G,4BAxBJ3F,mBAAA,CAKM;IALDH,KAAK,EAAC;EAAY,IACrBG,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAa,IACtBG,mBAAA,CAAsC;IAAhCH,KAAK,EAAC;EAAkB,IAC9BG,mBAAA,CAA2C;IAArCH,KAAK,EAAC;EAAmB,GAAC,MAAI,E,wBAGxCG,mBAAA,CAOM,OAPN4F,WAOM,I,kBANJ9F,mBAAA,CAKMsC,SAAA,QAAAC,WAAA,CAL6ChC,IAAA,CAAAwF,eAAe,GAA7BtD,IAAI,EAAEiB,GAAG;yBAA9C1D,mBAAA,CAKM;MALDD,KAAK,EAAC,iBAAiB;MAAyC4C,GAAG,EAAEe,GAAG;MAC1E9B,KAAK,EAAAgB,eAAA;QAAAoD,eAAA,SAA4BvD,IAAI,CAAC0B,EAAE;MAAA;QACzCjE,mBAAA,CACM,OADN+F,WACM,G,kCADiCxD,IAAI,CAACyD,MAAM,kBAAGhG,mBAAA,CAAyD,QAAzDiG,WAAyD,EAAAnF,gBAAA,CAAnByB,IAAI,CAAC2D,IAAI,iB,GAEpGlG,mBAAA,CAAyD,OAAzDmG,WAAyD,EAAArF,gBAAA,CAAnByB,IAAI,CAACO,KAAK,iB;oCAGpD9C,mBAAA,CASM,OATNoG,WASM,G,4BARJpG,mBAAA,CAAwC;IAAnCH,KAAK,EAAC;EAAkB,GAAC,MAAI,sBAClCG,mBAAA,CAMM,OANNqG,WAMM,I,kBALJvG,mBAAA,CAIMsC,SAAA,QAAAC,WAAA,CAJ6ChC,IAAA,CAAAiG,SAAS,GAAxBC,KAAK,EAAE/C,GAAG;yBAA9C1D,mBAAA,CAIM;MAJDD,KAAK,EAAC,gBAAgB;MAAoC4C,GAAG,EAAEe;QAClExD,mBAAA,CAA0F;MAApFH,KAAK,EAAAiF,eAAA,EAAC,iBAAiB,wBAA+BtB,GAAG;wBAAUA,GAAG,6BAC5ExD,mBAAA,CAAqD,QAArDwG,WAAqD,EAAA1F,gBAAA,CAArByF,KAAK,CAACpD,KAAK,kBAC3CnD,mBAAA,CAA0E;MAApEH,KAAK,EAAAiF,eAAA,EAAC,eAAe,sBAA6BtB,GAAG;OAAO,GAAC,iB;wCAK3ElD,mBAAA,UAAa,EACbN,mBAAA,CA8BM,OA9BNyG,WA8BM,GA7BJzG,mBAAA,CAQM,OARN0G,WAQM,G,4BAPJ1G,mBAAA,CAGM;IAHDH,KAAK,EAAC;EAAa,IACtBG,mBAAA,CAAsC;IAAhCH,KAAK,EAAC;EAAkB,IAC9BG,mBAAA,CAA2C;IAArCH,KAAK,EAAC;EAAmB,GAAC,MAAI,E,sBAEtCG,mBAAA,CAEM;IAFDH,KAAK,EAAC,cAAc;IAAEY,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEN,IAAA,CAAAO,QAAQ;MACxCZ,mBAAA,CAAoD,QAApD2G,WAAoD,EAAA7F,gBAAA,CAAjBT,IAAA,CAAAU,OAAO,iB,KAG9Cf,mBAAA,CAmBM,OAnBN4G,WAmBM,GAlBJ5G,mBAAA,CAiBM,OAjBN6G,WAiBM,G,4BAhBJ7G,mBAAA,CAKM;IALDH,KAAK,EAAC;EAA0B,IACnCG,mBAAA,CAAe,cAAT,IAAE,GACRA,mBAAA,CAAiB,cAAX,MAAI,GACVA,mBAAA,CAAiB,cAAX,MAAI,GACVA,mBAAA,CAAiB,cAAX,MAAI,E,yCAEZF,mBAAA,CAMMsC,SAAA,QAAAC,WAAA,CANmDhC,IAAA,CAAAyG,qBAAqB,GAAnCvE,IAAI,EAAEiB,GAAG;yBAApD1D,mBAAA,CAMM;MANDD,KAAK,EAAAiF,eAAA,EAAC,uBAAuB;QAAA,WACXtB,GAAG;MAAA;MADuDf,GAAG,EAAEF,IAAI,CAACwE;QAEzF/G,mBAAA,CAA4B,cAAAc,gBAAA,CAAnByB,IAAI,CAACwE,IAAI,kBAClB/G,mBAAA,CAA+B,cAAAc,gBAAA,CAAtByB,IAAI,CAACyE,OAAO,kBACrBhH,mBAAA,CAAgC,cAAAc,gBAAA,CAAvByB,IAAI,CAAC0E,QAAQ,kBACtBjH,mBAAA,CAA+B,cAAAc,gBAAA,CAAtByB,IAAI,CAAC2E,OAAO,iB;8DAEvBlH,mBAAA,CAEM;IAFDH,KAAK,EAAC;EAA0B,IACnCG,mBAAA,CAA0C;IAAlCH,KAAK,EAAC;EAAc,GAAC,MAAI,E", "ignoreList": []}]}