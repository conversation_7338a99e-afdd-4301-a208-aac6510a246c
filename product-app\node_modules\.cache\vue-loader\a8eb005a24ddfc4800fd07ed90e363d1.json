{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\Home.vue?vue&type=style&index=0&id=10233720&lang=less&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\Home.vue", "mtime": 1753932846834}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\Home.vue"], "names": [], "mappings": ";AA2UA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrB;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACvD;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACf;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;UAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACjB;QACF;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B;MACF;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACvD;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAE5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjB;MACF;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACrB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACnB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChB;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;UAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACnB;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACjB;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACjB;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACvD;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;;UAEP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACpB;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACpB;QACF;MACF;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAE3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;UAElB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAClB;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACpB;MACF;IACF;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACvD;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;UAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACpB;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB;;UAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB;QACF;MACF;IACF;EACF;;EAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;;UAEd,CAAC,CAAC,CAAC,EAAE;YACH,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACjB;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;UAEf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB;QACF;;QAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;UAEhB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACjB;QACF;MACF;IACF;EACF;AACF", "file": "D:/zy/xm/h5/qdzx_h5/product-app/src/views/SmartBrainLargeScreen/component/Home.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"home_page\">\n    <div class=\"map_section\">\n      <MapQingdao :mapData=\"mapData\" />\n    </div>\n    <!-- 委员统计 -->\n    <div class=\"home_committee_statistics\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">委员统计</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">{{ circles }}</span>\n        </div>\n      </div>\n      <div class=\"committee_statistics_box\">\n        <div class=\"statistics_card card_blue\">\n          <div class=\"card_content\">\n            <div class=\"card_number\">{{ cppccMemberNum }}</div>\n            <div class=\"card_label\">政协委员(人)</div>\n          </div>\n        </div>\n        <div class=\"statistics_card card_yellow\">\n          <div class=\"card_content\">\n            <div class=\"card_number\">{{ standingCommitteeNum }}</div>\n            <div class=\"card_label\">政协常委(人)</div>\n          </div>\n        </div>\n      </div>\n      <div class=\"circles_box\">\n        <div class=\"circles_top_header\">\n          <span class=\"circles_top_header_title\">界别分布</span>\n          <span class=\"circles_top_header_more\" style=\"\">查看全部</span>\n        </div>\n        <horizontalBarEcharts id=\"circles\" :barList=\"barList\" colorStart=\"#FFFFFF\" colorEnd=\"#EF817C\"\n          style=\"height: 260px;\" />\n      </div>\n    </div>\n    <!-- 提案统计 -->\n    <div class=\"home_proposal_statistics\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">提案统计</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">{{ circles }}</span>\n        </div>\n      </div>\n      <div class=\"proposal_statistics_box\">\n        <div class=\"proposal_card\" v-for=\"(item, index) in proposalStatsNum\" :key=\"index\">\n          <div class=\"proposal_number\" :style=\"{ color: item.color }\">{{ item.value }}</div>\n          <div class=\"proposal_label\">{{ item.label }}</div>\n        </div>\n      </div>\n      <div class=\"proposal_type_analysis\">\n        <pieEchartsLegend id=\"typeAnalysisPie\" :dataList=\"typeAnalysisList\" title=\"类型分析\" />\n      </div>\n    </div>\n    <!-- 工作动态 -->\n    <div class=\"home_work_dynamics\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">工作动态</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">本年</span>\n        </div>\n      </div>\n      <div class=\"work_dynamics_list\">\n        <div class=\"work_dynamics_item\" v-for=\"(item, idx) in workDynamicsList\" :key=\"idx\">\n          <div class=\"work_dynamics_title\">{{ item.title }}</div>\n          <div class=\"work_dynamics_date\">{{ item.date }}</div>\n        </div>\n      </div>\n    </div>\n    <!-- 社情民意 -->\n    <div class=\"home_social\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">社情民意</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">本年</span>\n        </div>\n      </div>\n      <div class=\"social_box\">\n        <!-- 总数卡片 -->\n        <div class=\"social_card total_card\" :style=\"{ background: socialStats[0].bg }\">\n          <div class=\"total_card_number\" :style=\"{ color: socialStats[0].color }\">{{ socialStats[0].total }}</div>\n          <div class=\"total_card_label\">{{ socialStats[0].label }}</div>\n        </div>\n        <!-- 报送卡片 -->\n        <div class=\"social_card report_card\" v-for=\"(item, idx) in socialStats.slice(1)\" :key=\"idx\"\n          :style=\"{ background: item.bg }\">\n          <div class=\"report_row\">\n            <span class=\"report_label\">总数</span>\n            <span class=\"report_total\" :style=\"{ color: item.color }\">{{ item.total }}</span>\n          </div>\n          <div class=\"report_row\">\n            <span class=\"report_label\">采用</span>\n            <span class=\"report_adopted\" :style=\"{ color: item.adoptedColor }\">{{ item.adopted }}</span>\n          </div>\n          <div class=\"report_card_label\">{{ item.label }}</div>\n        </div>\n      </div>\n    </div>\n    <!-- 会议活动 -->\n    <div class=\"home_meetting_activity\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">会议活动</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">本年</span>\n        </div>\n      </div>\n      <div class=\"meetting_activity_box\">\n        <div v-for=\"(item, idx) in meettingActivityList\" :key=\"idx\" class=\"meetting_activity_card\"\n          :class=\"item.cardClass\">\n          <img :src=\"item.icon\" class=\"activity_card_iconimg\" />\n          <div class=\"meetting_activity_card_content\">\n            <div class=\"meetting_activity_card_label\">{{ item.label1 }}</div>\n            <div class=\"meetting_activity_card_value\" :style=\"item.value1Style\">{{ item.value1 }}</div>\n            <div class=\"meetting_activity_card_label\">{{ item.label2 }}</div>\n            <div class=\"meetting_activity_card_value\" :style=\"item.value2Style\">{{ item.value2 }}</div>\n          </div>\n        </div>\n      </div>\n    </div>\n    <!-- 网络议政 -->\n    <div class=\"home_discussions\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">网络议政</span>\n        </div>\n      </div>\n      <div class=\"discussions_box\">\n        <div class=\"discussion_card\" v-for=\"(item, idx) in discussionsList\" :key=\"idx\"\n          :style=\"{ backgroundImage: `url(${item.bg})` }\">\n          <div class=\"discussion_card_number\">{{ item.number }}<span class=\"discussion_card_unit\">{{ item.unit }}</span>\n          </div>\n          <div class=\"discussion_card_label\">{{ item.label }}</div>\n        </div>\n      </div>\n      <div class=\"hot_topics\">\n        <div class=\"hot_topics_title\">最热话题</div>\n        <div class=\"hot_topics_list\">\n          <div class=\"hot_topic_item\" v-for=\"(topic, idx) in hotTopics\" :key=\"idx\">\n            <span class=\"hot_topic_index\" :class=\"'hot_topic_index_' + (idx + 1)\">{{ idx + 1 }}</span>\n            <span class=\"hot_topic_text\">{{ topic.title }}</span>\n            <span class=\"hot_topic_tag\" :class=\"'hot_topic_tag_' + (idx + 1)\">热</span>\n          </div>\n        </div>\n      </div>\n    </div>\n    <!-- 履职统计 -->\n    <div class=\"home_performance_statistics\">\n      <div class=\"header_box\">\n        <div class=\"header_left\">\n          <span class=\"header_left_line\"></span>\n          <span class=\"header_left_title\">履职统计</span>\n        </div>\n        <div class=\"header_right\" @click=\"openMore('notice')\">\n          <span class=\"header_right_text\">{{ circles }}</span>\n        </div>\n      </div>\n      <div class=\"performance_statistics_box\">\n        <div class=\"performance_table\">\n          <div class=\"performance_table_header\">\n            <span>姓名</span>\n            <span>会议活动</span>\n            <span>政协提案</span>\n            <span>社情民意</span>\n          </div>\n          <div class=\"performance_table_row\" v-for=\"(item, idx) in performanceStatistics\" :key=\"item.name\"\n            :class=\"{ 'row-alt': idx % 2 === 1 }\">\n            <span>{{ item.name }}</span>\n            <span>{{ item.meeting }}</span>\n            <span>{{ item.proposal }}</span>\n            <span>{{ item.opinion }}</span>\n          </div>\n          <div class=\"performance_table_footer\">\n            <button class=\"view-all-btn\">查看全部</button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n<script>\nimport { toRefs, reactive } from 'vue'\nimport MapQingdao from './echartsComponent/MapQingdao.vue'\nimport horizontalBarEcharts from './echartsComponent/horizontalBarEcharts.vue'\nimport pieEchartsLegend from './echartsComponent/pieEchartsLegend.vue'\n\nexport default {\n  components: { MapQingdao, horizontalBarEcharts, pieEchartsLegend },\n  setup () {\n    const data = reactive({\n      mapData: [\n        { name: '市南区', value: 80 },\n        { name: '市北区', value: 60 },\n        { name: '李沧区', value: 50 },\n        { name: '崂山区', value: 40 },\n        { name: '城阳区', value: 70 },\n        { name: '黄岛区', value: 90 },\n        { name: '即墨区', value: 30 },\n        { name: '胶州市', value: 55 },\n        { name: '平度市', value: 20 },\n        { name: '莱西市', value: 10 }\n      ],\n      circles: '十一届二次',\n      cppccMemberNum: '10095',\n      standingCommitteeNum: '8742',\n      barList: [\n        { name: '教育界', value: 35 },\n        { name: '医药卫生界', value: 15 },\n        { name: '经济界', value: 14 },\n        { name: '工商联界', value: 21 },\n        { name: '民革界', value: 15 },\n        { name: '特邀界', value: 21 },\n        { name: '妇联界', value: 8 },\n        { name: '工会界', value: 8 },\n        { name: '社会福利与社会保障界', value: 14 }\n      ],\n      proposalStatsNum: [\n        { value: 873, label: '提案总数', color: '#2386F9' },\n        { value: 456, label: '委员提案', color: '#2CA6F9' },\n        { value: 354, label: '界别提案', color: '#3AC86B' },\n        { value: 221, label: '组织提案', color: '#F96C9C' }\n      ],\n      typeAnalysisList: [\n        { name: '发改财政', value: 22.52, color: '#3DC3F0' },\n        { name: '民政市场', value: 18.33, color: '#4AC6A8' },\n        { name: '公安司法', value: 12.5, color: '#F9C846' },\n        { name: '区市政府', value: 11.34, color: '#6DD3A0' },\n        { name: '科技工信', value: 9.56, color: '#7B8DF9' },\n        { name: '教育文化', value: 8.09, color: '#F97C9C' },\n        { name: '派出机构', value: 4.21, color: '#F9A846' },\n        { name: '驻青单位', value: 3.71, color: '#F97C46' },\n        { name: '住建交通', value: 3.65, color: '#A97CF9' },\n        { name: '农村卫生', value: 3.21, color: '#4A9CF9' },\n        { name: '其他机构', value: 1.86, color: '#BFBFBF' },\n        { name: '党群其他', value: 1.02, color: '#F9C8C8' }\n      ],\n      workDynamicsList: [\n        { title: '市政协社会和法制工作办公室围绕市政协社会和法制工作办公室围绕', date: '2025-06-03' },\n        { title: '“与民同行 共创共赢”新格局下民市政协社会和法制工作办公室围绕', date: '2025-05-30' },\n        { title: '“惠民生·基层行”义诊活动温暖人心市政协社会和法制工作办公室围绕', date: '2025-05-30' },\n        { title: '市科技局面复市政协科技界别提案市政协社会和法制工作办公室围绕', date: '2025-05-30' },\n        { title: '孟庆斌到胶州市、崂山区调研项目时市政协社会和法制工作办公室围绕', date: '2025-05-29' }\n      ],\n      socialStats: [\n        { total: 1057, label: '总数', bg: '#EFF6FF', color: '#2386F9' },\n        { total: 345, adopted: 21, label: '委员报送', bg: '#FDF8F0', color: '#2386F9', adoptedColor: '#F9C846' },\n        { total: 547, adopted: 79, label: '单位报送', bg: '#F0FDF4', color: '#3AC86B', adoptedColor: '#F9C846' }\n      ],\n      meettingActivityList: [\n        {\n          cardClass: 'card_meeting',\n          icon: require('../../../assets/img/largeScreen/icon_meetting.png'),\n          label1: '会议次数',\n          value1: 201,\n          value1Style: { color: '#308FFF' },\n          label2: '会议人数',\n          value2: 2412,\n          value2Style: { color: '#308FFF' }\n        },\n        {\n          cardClass: 'card_activity',\n          icon: require('../../../assets/img/largeScreen/icon_acticity.png'),\n          label1: '活动次数',\n          value1: 310,\n          value1Style: { color: '#1FC6FF' },\n          label2: '活动人数',\n          value2: 4015,\n          value2Style: { color: '#1FC6FF' }\n        }\n      ],\n      discussionsList: [\n        {\n          bg: require('../../../assets/img/largeScreen/icon_release_bg.png'),\n          number: '72',\n          unit: '个',\n          label: '发布议题',\n          desc: ''\n        },\n        {\n          bg: require('../../../assets/img/largeScreen/icon_participate_bg.png'),\n          number: '39301',\n          unit: '次',\n          label: '累计参与人次',\n          desc: ''\n        },\n        {\n          bg: require('../../../assets/img/largeScreen/icon_seek_bg.png'),\n          number: '12308',\n          unit: '条',\n          label: '累计征求意见',\n          desc: ''\n        }\n      ],\n      hotTopics: [\n        { title: '推进黄河国家文化公园建设' },\n        { title: '持续推进黄河流域生态保护修复，助力…' },\n        { title: '全面加强新时代中小学劳动教育' }\n      ],\n      performanceStatistics: [\n        { name: '马平安', meeting: 515, proposal: 15, opinion: 0 },\n        { name: '马波', meeting: 400, proposal: 0, opinion: 12 },\n        { name: '王玉民', meeting: 490, proposal: 15, opinion: 0 },\n        { name: '王洋宝', meeting: 500, proposal: 0, opinion: 1 },\n        { name: '王忠', meeting: 420, proposal: 0, opinion: 2 },\n        { name: '刘彩霞', meeting: 512, proposal: 0, opinion: 1 },\n        { name: '刘军', meeting: 500, proposal: 20, opinion: 0 },\n        { name: '吴雪玲', meeting: 315, proposal: 15, opinion: 38 },\n        { name: '杨文比', meeting: 310, proposal: 60, opinion: 28 },\n        { name: '贾谊', meeting: 540, proposal: 9, opinion: 13 }\n      ]\n    })\n    return { ...toRefs(data) }\n  }\n}\n</script>\n<style lang=\"less\" scoped>\n.home_page {\n  width: 100%;\n  height: 100%;\n\n  .header_box {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 15px 15px 0 15px;\n\n    .header_left {\n      display: flex;\n      align-items: center;\n\n      .header_left_line {\n        width: 3px;\n        height: 14px;\n        background: #007AFF;\n      }\n\n      .header_left_title {\n        font-weight: bold;\n        font-size: 15px;\n        color: #222222;\n        margin-left: 8px;\n        font-family: Source Han Serif SC, Source Han Serif SC;\n      }\n    }\n\n    .header_right {\n      display: flex;\n      align-items: center;\n\n      .header_right_text {\n        font-weight: 400;\n        font-size: 12px;\n        color: #999999;\n      }\n    }\n  }\n\n  .map_section {\n    background: #fff;\n    border-radius: 8px;\n    padding: 10px;\n  }\n\n  .home_committee_statistics {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .committee_statistics_box {\n      display: flex;\n      gap: 15px;\n      padding: 20px 15px 10px 15px;\n\n      .statistics_card {\n        flex: 1;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: #f5faff;\n        position: relative;\n        height: 86px;\n\n        .card_content {\n          display: flex;\n          flex-direction: column;\n          align-items: flex-start;\n          justify-content: center;\n          margin-left: 55px;\n          margin-top: 5px;\n\n          .card_number {\n            font-size: 20px;\n            color: #4AA3FF;\n          }\n\n          .card_label {\n            font-size: 14px;\n            color: #666;\n            margin-top: 2px;\n          }\n        }\n      }\n\n      .card_blue {\n        background-image: url('../../../assets/img/largeScreen/icon_member_bg.png');\n        background-size: 100% 100%;\n        background-position: center;\n      }\n\n      .card_yellow {\n        background-image: url('../../../assets/img/largeScreen/icon_committee_bg.png');\n        background-size: 100% 100%;\n        background-position: center;\n\n        .card_number {\n          color: #E6B800 !important;\n        }\n      }\n    }\n\n    .circles_box {\n      border-radius: 6px;\n      margin: 10px 12px;\n\n      .circles_top_header {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n\n        .circles_top_header_title {\n          font-size: 14px;\n          color: #000;\n          font-family: Source Han Serif SC, Source Han Serif SC;\n        }\n\n        .circles_top_header_more {\n          font-size: 14px;\n          color: #0271E3;\n          border-radius: 14px;\n          border: 1px solid #0271E3;\n          padding: 3px 10px;\n        }\n      }\n    }\n  }\n\n  .home_proposal_statistics {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .proposal_statistics_box {\n      display: flex;\n      justify-content: space-between;\n      padding: 20px 15px 10px 15px;\n\n      .proposal_card {\n        flex: 1;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n\n        .proposal_number {\n          font-size: 24px;\n        }\n\n        .proposal_label {\n          font-size: 14px;\n          color: #999;\n          margin-top: 4px;\n        }\n      }\n    }\n\n    .proposal_type_analysis {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n    }\n  }\n\n  .home_work_dynamics {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .work_dynamics_list {\n      padding: 8px 15px;\n      background: #fff;\n\n      .work_dynamics_item {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        padding: 12px 0;\n        border-bottom: 1px solid #f0f0f0;\n\n        &:last-child {\n          border-bottom: none;\n        }\n\n        .work_dynamics_title {\n          flex: 1;\n          font-size: 14px;\n          color: #666666;\n          white-space: nowrap;\n          overflow: hidden;\n          text-overflow: ellipsis;\n        }\n\n        .work_dynamics_date {\n          font-size: 14px;\n          color: #bdbdbd;\n          flex-shrink: 0;\n        }\n      }\n    }\n  }\n\n  .home_social {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .social_box {\n      display: flex;\n      gap: 16px;\n      padding: 20px 15px;\n\n      .social_card {\n        flex: 1;\n        width: 97px;\n        height: 94px;\n        border-radius: 10px;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        box-sizing: border-box;\n        box-shadow: none;\n        padding: 15px;\n        background-clip: padding-box;\n      }\n\n      .total_card {\n        justify-content: center;\n\n        .total_card_number {\n          font-size: 20px;\n          color: #3B91FB;\n          margin-bottom: 5px;\n        }\n\n        .total_card_label {\n          font-size: 14px;\n          color: #666666;\n        }\n      }\n\n      .report_card {\n        justify-content: flex-start;\n\n        .report_row {\n          width: 100%;\n          display: flex;\n          justify-content: space-between;\n          align-items: baseline;\n          margin-bottom: 2px;\n\n          .report_label {\n            font-size: 14px;\n            color: #999;\n            margin-right: 2px;\n          }\n\n          .report_total {\n            font-size: 15px;\n          }\n\n          .report_adopted {\n            font-size: 15px;\n          }\n        }\n\n        .report_card_label {\n          margin-top: 5px;\n          font-size: 15px;\n          color: #666;\n          font-family: Source Han Serif SC, Source Han Serif SC;\n        }\n      }\n    }\n  }\n\n  .home_meetting_activity {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .meetting_activity_box {\n      display: flex;\n      gap: 16px;\n      padding: 20px 15px;\n\n      .meetting_activity_card {\n        flex: 1;\n        display: flex;\n        align-items: flex-start;\n        box-sizing: border-box;\n        width: 157px;\n        height: 140px;\n        padding: 14px 20px;\n\n        .activity_card_iconimg {\n          width: 32px;\n          height: 32px;\n          margin-right: 15px;\n          margin-top: 4px;\n        }\n\n        .meetting_activity_card_content {\n          display: flex;\n          flex-direction: column;\n          justify-content: center;\n          flex: 1;\n\n          .meetting_activity_card_label {\n            font-size: 14px;\n            color: #999;\n            margin-bottom: 5px;\n          }\n\n          .meetting_activity_card_value {\n            font-size: 20px;\n            color: #308FFF;\n            margin-bottom: 8px;\n          }\n        }\n      }\n\n      .card_meeting {\n        background-image: url('../../../assets/img/largeScreen/icon_meetting_bg.png');\n        background-size: 100% 100%;\n        background-position: center;\n      }\n\n      .card_activity {\n        background-image: url('../../../assets/img/largeScreen/icon_activity_bg.png');\n        background-size: 100% 100%;\n        background-position: center;\n      }\n    }\n  }\n\n  .home_discussions {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .discussions_box {\n      display: flex;\n      gap: 10px;\n      padding: 20px 15px;\n      justify-content: flex-start;\n\n      .discussion_card {\n        flex: 1;\n        width: 103px;\n        height: 77px;\n        background-size: 100% 100%;\n        background-position: center;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        color: #fff;\n\n        .discussion_card_number {\n          font-size: 20px;\n          margin-bottom: 2px;\n\n          .discussion_card_unit {\n            font-size: 12px;\n            font-weight: normal;\n            margin-left: 2px;\n          }\n        }\n\n        .discussion_card_label {\n          font-size: 14px;\n          font-weight: 400;\n          margin-bottom: 2px;\n        }\n      }\n    }\n\n    .hot_topics {\n      padding: 0 15px 15px 15px;\n\n      .hot_topics_title {\n        font-size: 14px;\n        color: #000;\n        margin-bottom: 10px;\n        font-family: Source Han Serif SC, Source Han Serif SC;\n      }\n\n      .hot_topics_list {\n        .hot_topic_item {\n          display: flex;\n          align-items: center;\n          border-bottom: 1px solid #f0f0f0;\n          padding: 12px 0;\n\n          .hot_topic_index {\n            font-size: 14px;\n            margin-right: 10px;\n          }\n\n          .hot_topic_index_1 {\n            color: #FF4D4F;\n          }\n\n          .hot_topic_index_2 {\n            color: #FF9900;\n          }\n\n          .hot_topic_index_3 {\n            color: #FFD600;\n          }\n\n          .hot_topic_text {\n            flex: 1;\n            white-space: nowrap;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            font-size: 14px;\n            color: #666;\n          }\n\n          .hot_topic_tag {\n            font-size: 14px;\n            color: #fff;\n            border-radius: 2px;\n            width: 22px;\n            height: 22px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n          }\n\n          .hot_topic_tag_1 {\n            background: #FB3030;\n          }\n\n          .hot_topic_tag_2 {\n            background: #FF833E;\n          }\n\n          .hot_topic_tag_3 {\n            background: #FFD978;\n          }\n        }\n      }\n    }\n  }\n\n  .home_performance_statistics {\n    background: #FFFFFF;\n    border-radius: 6px;\n    margin: 12px 0;\n\n    .performance_statistics_box {\n      margin-top: 15px;\n\n      .performance_table {\n        width: 100%;\n        background: #fff;\n\n        .performance_table_header,\n        .performance_table_row {\n          display: flex;\n          align-items: center;\n          padding: 8px 0;\n\n          span {\n            flex: 1;\n            text-align: center;\n          }\n        }\n\n        .performance_table_header {\n          background: #F1F8FF;\n          font-weight: bold;\n          color: #222;\n          font-size: 14px;\n        }\n\n        .performance_table_row {\n          background: #fff;\n          color: #222;\n          font-size: 14px;\n\n          &.row-alt {\n            background: #F1F8FF;\n          }\n        }\n\n        .performance_table_footer {\n          display: flex;\n          justify-content: center;\n          padding: 10px 0;\n          background: #fff;\n\n          .view-all-btn {\n            border: 1px solid #0271e3;\n            color: #0271e3;\n            background: #fff;\n            border-radius: 16px;\n            padding: 4px 14px;\n            font-size: 14px;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}