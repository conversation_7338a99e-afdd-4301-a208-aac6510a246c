{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\PieChart.vue?vue&type=template&id=52cf996d&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\PieChart.vue", "mtime": 1753943508732}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogIDxkaXYgOmlkPSJjaGFydElkIiBjbGFzcz0iY2hhcnQiPjwvZGl2Pg0K"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\PieChart.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/zy/xm/h5/qdzx_h5/product-app/src/views/SmartBrainLargeScreen/component/echartsComponent/PieChart.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div :id=\"chartId\" class=\"chart\"></div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, onMounted, onUnmounted, nextTick } from 'vue'\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: 'Pie<PERSON><PERSON>',\r\n  props: {\r\n    id: {\r\n      type: String,\r\n      default: () => ''\r\n    },\r\n    chartData: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    radius: {\r\n      type: Array,\r\n      default: () => ['26%', '42%']\r\n    },\r\n    center: {\r\n      type: Array,\r\n      default: () => ['50%', '35%']\r\n    },\r\n    startAngle: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    showLabel: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    showLegend: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    legendPosition: {\r\n      type: String,\r\n      default: 'bottom'\r\n    }\r\n  },\r\n  setup (props) {\r\n    const chartId = ref(props.id)\r\n    let chartInstance = null\r\n    // 初始化图表\r\n    const initChart = () => {\r\n      nextTick(() => {\r\n        const dom = document.getElementById(chartId.value)\r\n        if (!dom) {\r\n          console.error('Chart DOM element not found:', chartId.value)\r\n          return\r\n        }\r\n        if (!chartInstance) {\r\n          chartInstance = echarts.init(dom)\r\n        }\r\n\r\n        const option = {\r\n          tooltip: {\r\n            trigger: 'item',\r\n            formatter: '{b}: {c} ({d}%)',\r\n            confine: true,\r\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\r\n            borderColor: 'transparent',\r\n            textStyle: {\r\n              color: '#fff',\r\n              fontSize: 12\r\n            },\r\n            extraCssText: 'border-radius: 4px; padding: 8px 12px;'\r\n          },\r\n          legend: {\r\n            show: props.showLegend,\r\n            orient: 'horizontal',\r\n            bottom: 8,\r\n            top: props.legendPosition === 'top' ? 10 : 'auto',\r\n            // left: 30,\r\n            // right: 30,\r\n            textStyle: {\r\n              fontSize: 12,\r\n              color: '#999'\r\n            },\r\n            itemWidth: 18,\r\n            itemHeight: 8,\r\n            itemGap: 22,\r\n            formatter: function (name) {\r\n              const item = props.chartData.find(data => data.name === name)\r\n              if (item) {\r\n                return `${name} ${item.value}人 ${item.percentage}`\r\n              }\r\n              return name\r\n            }\r\n          },\r\n          series: [\r\n            {\r\n              type: 'pie',\r\n              // minAngle: 20,\r\n              radius: props.radius,\r\n              center: props.center,\r\n              startAngle: props.startAngle,\r\n              avoidLabelOverlap: true,\r\n              label: {\r\n                show: props.showLabel,\r\n                position: 'outside',\r\n                formatter: function (params) {\r\n                  return params.name + '\\n' + '{c|' + params.value + '人}'\r\n                },\r\n                rich: {\r\n                  c: {\r\n                    color: '#666',\r\n                    fontSize: 14\r\n                  }\r\n                },\r\n                fontSize: 11,\r\n                color: '#999',\r\n                lineHeight: 18\r\n              },\r\n              labelLine: {\r\n                show: props.showLabel,\r\n                length: 10,\r\n                length2: 20,\r\n                smooth: false\r\n              },\r\n              data: props.chartData.map(item => ({\r\n                value: item.value,\r\n                name: item.name,\r\n                itemStyle: {\r\n                  color: item.color\r\n                }\r\n              }))\r\n            }\r\n          ]\r\n        }\r\n        chartInstance.setOption(option)\r\n      })\r\n    }\r\n\r\n    // 监听窗口大小变化\r\n    const handleResize = () => {\r\n      if (chartInstance) {\r\n        chartInstance.resize()\r\n      }\r\n    }\r\n\r\n    onMounted(() => {\r\n      initChart()\r\n      window.addEventListener('resize', handleResize)\r\n    })\r\n\r\n    onUnmounted(() => {\r\n      if (chartInstance) {\r\n        chartInstance.dispose()\r\n        chartInstance = null\r\n      }\r\n      window.removeEventListener('resize', handleResize)\r\n    })\r\n\r\n    return {\r\n      chartId\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.chart {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n</style>\r\n"]}]}