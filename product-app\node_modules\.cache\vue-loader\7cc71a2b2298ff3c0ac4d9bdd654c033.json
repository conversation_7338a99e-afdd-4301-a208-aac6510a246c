{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue", "mtime": 1753940910433}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\ProposalWork.vue"], "names": [], "mappings": ";AAiMA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,EAA<PERSON>,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7E,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC1F,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAExB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChD,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;;MAEjE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC7B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC/B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC7B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAChC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACP,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACzB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;QACzB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAClC,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACvD,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAChD,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAClD,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACnD,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;MACnD,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACb,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC5B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACzB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QAC3B,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAC5B;IACF,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAClC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACzE,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAC/B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACtE,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAC1C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1F,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MAC5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;QACd,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACxE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACxE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC3E;MACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3B;;IAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ;EACF;AACF", "file": "D:/zy/xm/h5/qdzx_h5/product-app/src/views/SmartBrainLargeScreen/component/ProposalWork.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"ProposalWork\">\r\n    <!-- 提案整体情况 -->\r\n    <div class=\"proposal_overall_situation_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">提案整体情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"proposal_overall_situation\">\r\n        <div class=\"statistics_row\">\r\n          <div class=\"statistics_card\" style=\"background: #E8F7FF;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_total.png\" alt=\"提案总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">提案总件数</div>\r\n            <div class=\"card_value proposal_total_text\">{{ proposalTotal }}</div>\r\n          </div>\r\n          <div class=\"statistics_card\" style=\"background: #F1F5FF;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_register_num.png\" alt=\"立案总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">立案总件数</div>\r\n            <div class=\"card_value register_text\">{{ registerTotal }}</div>\r\n          </div>\r\n          <div class=\"statistics_card\" style=\"background: #DAF6F2;\">\r\n            <img src=\"../../../assets/img/largeScreen/icon_reply_num.png\" alt=\"答复总件数\" class=\"card_icon\" />\r\n            <div class=\"card_label\">答复总件数</div>\r\n            <div class=\"card_value reply_text\">{{ replyTotal }}</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"progress_row\">\r\n          <div class=\"progress_card\">\r\n            <div class=\"progress_content\">\r\n              <div class=\"progress_circle blue_progress\">\r\n                <svg width=\"50\" height=\"50\" viewBox=\"0 0 80 80\">\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"rgba(58,147,255,0.2)\" stroke-width=\"8\" />\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"#3A61CD\" stroke-width=\"8\" stroke-dasharray=\"188.4\"\r\n                    :stroke-dashoffset=\"registerCircleOffset\" stroke-linecap=\"round\" transform=\"rotate(-90 40 40)\" />\r\n                </svg>\r\n              </div>\r\n              <div class=\"progress_info\">\r\n                <div class=\"progress_label\">立案率</div>\r\n                <span class=\"progress_value\" style=\"color: #3A61CD;\">{{ registerRate }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"progress_card\">\r\n            <div class=\"progress_content\">\r\n              <div class=\"progress_circle green_progress\">\r\n                <svg width=\"50\" height=\"50\" viewBox=\"0 0 80 80\">\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"rgba(58,147,255,0.2)\" stroke-width=\"8\" />\r\n                  <circle cx=\"40\" cy=\"40\" r=\"30\" fill=\"none\" stroke=\"#57BCAA\" stroke-width=\"8\" stroke-dasharray=\"188.4\"\r\n                    :stroke-dashoffset=\"replyCircleOffset\" stroke-linecap=\"round\" transform=\"rotate(-90 40 40)\" />\r\n                </svg>\r\n              </div>\r\n              <div class=\"progress_info\">\r\n                <div class=\"progress_label\">答复率</div>\r\n                <span class=\"progress_value\" style=\"color: #57BCAA;\">{{ replyRate }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 提交情况 -->\r\n    <div class=\"submit_status_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">提交情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"submit_status\">\r\n        <div class=\"submit_statistics_row\">\r\n          <div class=\"submit_card\" style=\"background: #E8F4FF;\">\r\n            <div class=\"submit_value committee_text\">{{ committeeProposal }}</div>\r\n            <div class=\"submit_label\">委员提案</div>\r\n          </div>\r\n          <div class=\"submit_card\" style=\"background: #FFF8E1;\">\r\n            <div class=\"submit_value boundary_text\">{{ boundaryProposal }}</div>\r\n            <div class=\"submit_label\">界别提案</div>\r\n          </div>\r\n          <div class=\"submit_card\" style=\"background: #E8F5E8;\">\r\n            <div class=\"submit_value organization_text\">{{ organizationProposal }}</div>\r\n            <div class=\"submit_label\">组织提案</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 类型分布 -->\r\n    <div class=\"proposal_type_analysis_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">类型分布</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"proposal_type_analysis\">\r\n        <pieEchartsLegend id=\"typeAnalysisPie\" :dataList=\"typeAnalysisList\" title=\"类型分析\" />\r\n      </div>\r\n    </div>\r\n    <!-- 答复类型 -->\r\n    <div class=\"reply_type_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">答复类型</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"reply_type\">\r\n        <ProgressBarChart title=\"面复\" :desc=\"`占总件数${replyType.face}%`\" :percent=\"replyType.face\"\r\n          :value=\"replyType.faceNum\" color=\"linear-gradient(90deg, rgba(58,147,255,0.3) 0%, #3A93FF 100%)\" />\r\n        <ProgressBarChart title=\"函复\" :desc=\"`占提交数${replyType.letter}%`\" :percent=\"replyType.letter\"\r\n          :value=\"replyType.letterNum\" color=\"linear-gradient(90deg, rgba(255,115,141,0.3) 0%, #FF738D 100%)\" />\r\n      </div>\r\n    </div>\r\n    <!-- 办理单位统计（前十） -->\r\n    <div class=\"handle_unit_statistics_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">办理单位统计（前十）</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"handle_unit_statistics\">\r\n        <!-- 表头 -->\r\n        <div class=\"ranking_header\">\r\n          <div class=\"header_rank\">排名</div>\r\n          <div class=\"header_unit\">单位名称</div>\r\n          <div class=\"header_count\">办理数量</div>\r\n        </div>\r\n\r\n        <!-- 排名列表 -->\r\n        <div class=\"ranking_list\">\r\n          <div v-for=\"(item, index) in unitRankingList\" :key=\"index\" class=\"ranking_item\"\r\n            :class=\"{ 'top_three': index < 3 }\">\r\n            <div class=\"rank_number\">\r\n              <!-- 前三名显示图片，其他显示数字 -->\r\n              <img v-if=\"index < 3\" :src=\"getRankIcon(index + 1)\" :alt=\"`第${index + 1}名`\" class=\"rank_icon\" />\r\n              <span v-else class=\"rank_text\">{{ index + 1 }}</span>\r\n            </div>\r\n            <div class=\"unit_name\">{{ item.name }}</div>\r\n            <div class=\"handle_count\">{{ item.count }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 各专委会提案数 -->\r\n    <div class=\"committee_proposal_num_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">各专委会提案数</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"committee_proposal_num\">\r\n        <horizontalBarEcharts id=\"committeeProposalNum\" :barList=\"barList\" colorStart=\"#FFFFFF\" colorEnd=\"#EF817C\"\r\n          style=\"height: 260px;\" />\r\n      </div>\r\n    </div>\r\n    <!-- 重点提案 -->\r\n    <div class=\"key_proposal_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">重点提案</span>\r\n        </div>\r\n        <div class=\"header_right\" @click=\"openMore('notice')\">\r\n          <span class=\"header_right_more\">查看全部</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"key_proposal_list\">\r\n        <div class=\"key_proposal_item\" v-for=\"(item, idx) in keyProposalList\" :key=\"idx\">\r\n          <div class=\"key_proposal_item_title\">{{ item.title }}</div>\r\n          <div class=\"key_proposal_item_date\">{{ item.name }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 热词分析 -->\r\n    <div class=\"hot_word_analysis_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">热词分析</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"hot_word_analysis_list\">\r\n        <wordCloudEcharts id=\"wordcloud\" :wordList=\"wordCloudData\"\r\n          :colorList=\"['#1890FF', '#FF6B35', '#52C41A', '#722ED1', '#1890FF', '#FF69B4', '#52C41A', '#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']\"\r\n          :sizeRange=\"[2, 10]\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { toRefs, reactive, computed } from 'vue'\r\nimport pieEchartsLegend from './echartsComponent/pieEchartsLegend.vue'\r\nimport ProgressBarChart from './echartsComponent/ProgressBarChart.vue'\r\nimport horizontalBarEcharts from './echartsComponent/horizontalBarEcharts.vue'\r\nimport wordCloudEcharts from './echartsComponent/wordCloudEcharts.vue'\r\nexport default {\r\n  components: { pieEchartsLegend, ProgressBarChart, horizontalBarEcharts, wordCloudEcharts },\r\n  name: 'ProposalWork',\r\n  setup () {\r\n    const data = reactive({\r\n      // 提案统计数据\r\n      proposalTotal: 1500, // 提案总件数\r\n      registerTotal: 600, // 立案总件数\r\n      replyTotal: 600, // 答复总件数\r\n\r\n      // 提交情况数据\r\n      committeeProposal: 456, // 委员提案\r\n      boundaryProposal: 354, // 界别提案\r\n      organizationProposal: 221, // 组织提案\r\n\r\n      // 计算属性相关数据\r\n      circleRadius: 30, // 圆形进度条半径\r\n      circleStrokeWidth: 8, // 圆形进度条线宽\r\n      typeAnalysisList: [\r\n        { name: '发改财政', value: 22.52, color: '#3DC3F0' },\r\n        { name: '民政市场', value: 18.33, color: '#4AC6A8' },\r\n        { name: '公安司法', value: 12.5, color: '#F9C846' },\r\n        { name: '区市政府', value: 11.34, color: '#6DD3A0' },\r\n        { name: '科技工信', value: 9.56, color: '#7B8DF9' },\r\n        { name: '教育文化', value: 8.09, color: '#F97C9C' },\r\n        { name: '派出机构', value: 4.21, color: '#F9A846' },\r\n        { name: '驻青单位', value: 3.71, color: '#F97C46' },\r\n        { name: '住建交通', value: 3.65, color: '#A97CF9' },\r\n        { name: '农村卫生', value: 3.21, color: '#4A9CF9' },\r\n        { name: '其他机构', value: 1.86, color: '#BFBFBF' },\r\n        { name: '党群其他', value: 1.02, color: '#F9C8C8' }\r\n      ],\r\n      replyType: { face: 60, faceNum: 360, letter: 40, letterNum: 240 },\r\n\r\n      // 办理单位统计数据（前十）\r\n      unitRankingList: [\r\n        { name: '市教育局', count: 89 },\r\n        { name: '市民政局', count: 75 },\r\n        { name: '市劳动局', count: 70 },\r\n        { name: '市农业农村委', count: 63 },\r\n        { name: '市交通运输管理局', count: 60 },\r\n        { name: '市经济信息委', count: 50 },\r\n        { name: '市发改委', count: 46 },\r\n        { name: '市教委', count: 46 },\r\n        { name: '市林业局', count: 44 },\r\n        { name: '市规划自然资源局', count: 41 }\r\n      ],\r\n      barList: [\r\n        { name: '教育界', value: 35 },\r\n        { name: '医药卫生界', value: 15 },\r\n        { name: '经济界', value: 14 },\r\n        { name: '工商联界', value: 21 },\r\n        { name: '民革界', value: 15 },\r\n        { name: '特邀界', value: 21 },\r\n        { name: '妇联界', value: 8 },\r\n        { name: '工会界', value: 8 },\r\n        { name: '社会福利与社会保障界', value: 14 }\r\n      ],\r\n      keyProposalList: [\r\n        { id: '1', title: '关于强化社区快递物流体系基础设施建...', name: '赵国胜' },\r\n        { id: '2', title: '关于预制菜不进学校食堂的提案', name: '李毅' },\r\n        { id: '3', title: '关于上下班高峰期道路拥堵的提案', name: '王洪妮' },\r\n        { id: '4', title: '关于商讨每周工作4天振消费的提案', name: '张万强' },\r\n        { id: '5', title: '关于加强垃圾分类末端处理的提案', name: '王洪妮' }\r\n      ],\r\n      wordCloudData: [\r\n        { name: '乡村振兴', value: 180 },\r\n        { name: '就业优先', value: 165 },\r\n        { name: '科技创新', value: 150 },\r\n        { name: '改革开放', value: 135 },\r\n        { name: '依法治国', value: 120 },\r\n        { name: '教育人才', value: 105 },\r\n        { name: '社会保障', value: 90 },\r\n        { name: '热词', value: 75 },\r\n        { name: '绿色发展', value: 60 },\r\n        { name: '数字中国', value: 45 },\r\n        { name: '共同富裕', value: 40 }\r\n      ]\r\n    })\r\n\r\n    // 计算立案率\r\n    const registerRate = computed(() => {\r\n      if (data.proposalTotal === 0) return '0%'\r\n      return Math.round((data.registerTotal / data.proposalTotal) * 100) + '%'\r\n    })\r\n\r\n    // 计算答复率\r\n    const replyRate = computed(() => {\r\n      if (data.registerTotal === 0) return '0%'\r\n      return Math.round((data.replyTotal / data.registerTotal) * 100) + '%'\r\n    })\r\n\r\n    // 计算立案率圆形进度条偏移量\r\n    const registerCircleOffset = computed(() => {\r\n      const circumference = 2 * Math.PI * data.circleRadius\r\n      const percentage = data.proposalTotal === 0 ? 0 : (data.registerTotal / data.proposalTotal)\r\n      return circumference - (circumference * percentage)\r\n    })\r\n\r\n    // 计算答复率圆形进度条偏移量\r\n    const replyCircleOffset = computed(() => {\r\n      const circumference = 2 * Math.PI * data.circleRadius\r\n      const percentage = data.registerTotal === 0 ? 0 : (data.replyTotal / data.registerTotal)\r\n      return circumference - (circumference * percentage)\r\n    })\r\n\r\n    // 获取排名图标\r\n    const getRankIcon = (rank) => {\r\n      // 这里先返回占位图片路径，您可以后续替换为实际的图片路径\r\n      const iconMap = {\r\n        1: require('../../../assets/img/largeScreen/icon_rank_one.png'), // 第一名图标\r\n        2: require('../../../assets/img/largeScreen/icon_rank_two.png'), // 第二名图标\r\n        3: require('../../../assets/img/largeScreen/icon_rank_three.png') // 第三名图标\r\n      }\r\n      return iconMap[rank] || ''\r\n    }\r\n\r\n    return {\r\n      ...toRefs(data),\r\n      registerRate,\r\n      replyRate,\r\n      registerCircleOffset,\r\n      replyCircleOffset,\r\n      getRankIcon\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.ProposalWork {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .header_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 15px 0 15px;\r\n\r\n    .header_left {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_left_line {\r\n        width: 3px;\r\n        height: 14px;\r\n        background: #007AFF;\r\n      }\r\n\r\n      .header_left_title {\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #222222;\r\n        margin-left: 8px;\r\n        font-family: Source Han Serif SC, Source Han Serif SC;\r\n      }\r\n    }\r\n\r\n    .header_right {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_right_text {\r\n        font-weight: 400;\r\n        font-size: 12px;\r\n        color: #999999;\r\n      }\r\n\r\n      .header_right_more {\r\n        font-size: 14px;\r\n        color: #0271E3;\r\n        border-radius: 14px;\r\n        border: 1px solid #0271E3;\r\n        padding: 3px 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .proposal_overall_situation_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .proposal_overall_situation {\r\n      padding: 12px;\r\n\r\n      .statistics_row {\r\n        display: flex;\r\n        gap: 10px;\r\n        margin-bottom: 10px;\r\n\r\n        .statistics_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 17px 8px;\r\n          text-align: center;\r\n\r\n          .card_icon {\r\n            width: 32px;\r\n            height: 32px;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .card_label {\r\n            font-size: 12px;\r\n            color: #999;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .card_value {\r\n            font-size: 20px;\r\n\r\n            &.proposal_total_text {\r\n              color: #308FFF;\r\n            }\r\n\r\n            &.register_text {\r\n              color: #3A61CD;\r\n            }\r\n\r\n            &.reply_text {\r\n              color: #57BCAA;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .progress_row {\r\n        display: flex;\r\n        gap: 10px;\r\n        justify-content: space-between;\r\n\r\n        .progress_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 10px 16px;\r\n          background: #E8F7FF;\r\n\r\n          .progress_content {\r\n            display: flex;\r\n            align-items: center;\r\n          }\r\n\r\n          .progress_circle {\r\n            margin-right: 15px;\r\n            margin-top: 5px;\r\n          }\r\n\r\n          .progress_info {\r\n\r\n            .progress_label {\r\n              font-size: 12px;\r\n              color: #999999;\r\n              margin-bottom: 5px;\r\n            }\r\n\r\n            .progress_value {\r\n              font-size: 20px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .submit_status_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .submit_status {\r\n      padding: 12px;\r\n\r\n      .submit_statistics_row {\r\n        display: flex;\r\n        gap: 10px;\r\n\r\n        .submit_card {\r\n          flex: 1;\r\n          border-radius: 4px;\r\n          padding: 24px 16px;\r\n          text-align: center;\r\n\r\n          .submit_value {\r\n            font-size: 20px;\r\n            margin-bottom: 10px;\r\n\r\n            &.committee_text {\r\n              color: #3B91FB;\r\n            }\r\n\r\n            &.boundary_text {\r\n              color: #EAB308;\r\n            }\r\n\r\n            &.organization_text {\r\n              color: #43DDBB;\r\n            }\r\n          }\r\n\r\n          .submit_label {\r\n            font-size: 14px;\r\n            color: #666666;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .proposal_type_analysis_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .proposal_type_analysis {\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n    }\r\n  }\r\n\r\n  .reply_type_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .reply_type {\r\n      padding: 5px 18px 18px 18px;\r\n    }\r\n  }\r\n\r\n  .handle_unit_statistics_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .handle_unit_statistics {\r\n      padding: 10px 0;\r\n\r\n      .ranking_header {\r\n        display: flex;\r\n        align-items: center;\r\n        height: 36px;\r\n        padding: 0 18px 0 5px;\r\n        background: #F1F8FF;\r\n        border-radius: 4px 4px 0 0;\r\n\r\n        .header_rank {\r\n          width: 60px;\r\n          font-size: 14px;\r\n          color: #999999;\r\n          text-align: center;\r\n        }\r\n\r\n        .header_unit {\r\n          flex: 1;\r\n          font-size: 14px;\r\n          color: #999999;\r\n          text-align: left;\r\n          padding-left: 10px;\r\n        }\r\n\r\n        .header_count {\r\n          width: 80px;\r\n          font-size: 14px;\r\n          color: #999999;\r\n          text-align: right;\r\n        }\r\n      }\r\n\r\n      .ranking_list {\r\n        .ranking_item {\r\n          display: flex;\r\n          align-items: center;\r\n          height: 36px;\r\n          padding: 0 26px 0 5px;\r\n\r\n          // 斑马纹效果：奇数行白色，偶数行浅蓝色\r\n          &:nth-child(odd) {\r\n            background: #FFFFFF;\r\n          }\r\n\r\n          &:nth-child(even) {\r\n            background: #F1F8FF;\r\n          }\r\n\r\n          .rank_number {\r\n            width: 60px;\r\n            display: flex;\r\n            justify-content: center;\r\n            align-items: center;\r\n            height: 100%;\r\n\r\n            .rank_icon {\r\n              width: 24px;\r\n              height: 25px;\r\n              object-fit: contain;\r\n            }\r\n\r\n            .rank_text {\r\n              font-size: 14px;\r\n              color: #999999;\r\n            }\r\n          }\r\n\r\n          .unit_name {\r\n            flex: 1;\r\n            font-size: 14px;\r\n            color: #333333;\r\n            padding-left: 10px;\r\n            line-height: 1;\r\n          }\r\n\r\n          .handle_count {\r\n            width: 80px;\r\n            font-size: 14px;\r\n            color: #333333;\r\n            text-align: right;\r\n            line-height: 1;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .committee_proposal_num_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .committee_proposal_num {\r\n      padding: 12px;\r\n    }\r\n  }\r\n\r\n  .key_proposal_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .key_proposal_list {\r\n      padding: 8px 15px;\r\n      background: #fff;\r\n\r\n      .key_proposal_item {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        padding: 12px 0;\r\n        border-bottom: 1px solid #f0f0f0;\r\n\r\n        &:last-child {\r\n          border-bottom: none;\r\n        }\r\n\r\n        .key_proposal_item_title {\r\n          flex: 1;\r\n          font-size: 14px;\r\n          color: #666666;\r\n          white-space: nowrap;\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n        }\r\n\r\n        .key_proposal_item_date {\r\n          font-size: 14px;\r\n          color: #666;\r\n          flex-shrink: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .hot_word_analysis_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .hot_word_analysis_list {\r\n      padding: 5px 15px 15px 15px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}