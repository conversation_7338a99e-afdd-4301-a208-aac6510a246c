{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\MemberPerformance.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\MemberPerformance.vue", "mtime": 1753944413991}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgeyB0b1JlZnMsIHJlYWN0aXZlIH0gZnJvbSAndnVlJw0KaW1wb3J0IFBpZUNoYXJ0IGZyb20gJy4vZWNoYXJ0c0NvbXBvbmVudC9QaWVDaGFydC52dWUnDQppbXBvcnQgQWN0aXZpdHlUeXBlQ2hhcnQgZnJvbSAnLi9lY2hhcnRzQ29tcG9uZW50L0FjdGl2aXR5VHlwZUNoYXJ0LnZ1ZScNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ01lbWJlclBlcmZvcm1hbmNlJywNCiAgY29tcG9uZW50czogeyBQaWVDaGFydCwgQWN0aXZpdHlUeXBlQ2hhcnQgfSwNCiAgc2V0dXAgKCkgew0KICAgIGNvbnN0IGRhdGEgPSByZWFjdGl2ZSh7DQogICAgICAvLyDlubTluqblsaXogYzmsYfmgLvmlbDmja4NCiAgICAgIHBlcmZvcm1hbmNlTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICfmj5DkuqTmj5DmoYgnLA0KICAgICAgICAgIHZhbHVlOiAxNTAwLA0KICAgICAgICAgIGljb246IHJlcXVpcmUoJy4uLy4uLy4uL2Fzc2V0cy9pbWcvbGFyZ2VTY3JlZW4vaWNvbl9wcm9wb3NhbC5wbmcnKSwNCiAgICAgICAgICBiZ0NvbG9yOiAnI0YxRjVGRicsDQogICAgICAgICAgdmFsdWVDb2xvcjogJyMzQTYxQ0QnDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDogJ+aPkOS6pOekvuaDheawkeaEjycsDQogICAgICAgICAgdmFsdWU6IDEwNTcsDQogICAgICAgICAgaWNvbjogcmVxdWlyZSgnLi4vLi4vLi4vYXNzZXRzL2ltZy9sYXJnZVNjcmVlbi9pY29uX29waW5pb24ucG5nJyksDQogICAgICAgICAgYmdDb2xvcjogJyNEQUY2RjInLA0KICAgICAgICAgIHZhbHVlQ29sb3I6ICcjNTdCQ0FBJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICfnvZHnu5zorq7mlL8nLA0KICAgICAgICAgIHZhbHVlOiAyMTUsDQogICAgICAgICAgaWNvbjogcmVxdWlyZSgnLi4vLi4vLi4vYXNzZXRzL2ltZy9sYXJnZVNjcmVlbi9pY29uX25ldHdvcmsucG5nJyksDQogICAgICAgICAgYmdDb2xvcjogJyNFOEY3RkYnLA0KICAgICAgICAgIHZhbHVlQ29sb3I6ICcjMzA4RkZGJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICflj4LliqDkvJrorq4nLA0KICAgICAgICAgIHZhbHVlOiAzNjEsDQogICAgICAgICAgaWNvbjogcmVxdWlyZSgnLi4vLi4vLi4vYXNzZXRzL2ltZy9sYXJnZVNjcmVlbi9pY29uX21lZXRpbmcucG5nJyksDQogICAgICAgICAgYmdDb2xvcjogJyNGREY4RjAnLA0KICAgICAgICAgIHZhbHVlQ29sb3I6ICcjRUFCMzA4Jw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICflj4LliqDmtLvliqgnLA0KICAgICAgICAgIHZhbHVlOiAxMDQsDQogICAgICAgICAgaWNvbjogcmVxdWlyZSgnLi4vLi4vLi4vYXNzZXRzL2ltZy9sYXJnZVNjcmVlbi9pY29uX2FjdGl2aXR5LnBuZycpLA0KICAgICAgICAgIGJnQ29sb3I6ICcjRkRFRkVGJywNCiAgICAgICAgICB2YWx1ZUNvbG9yOiAnI0ZENzU3NScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiAn5YW25LuW5bGl6IGMJywNCiAgICAgICAgICB2YWx1ZTogMjQxLA0KICAgICAgICAgIGljb246IHJlcXVpcmUoJy4uLy4uLy4uL2Fzc2V0cy9pbWcvbGFyZ2VTY3JlZW4vaWNvbl9vdGhlci5wbmcnKSwNCiAgICAgICAgICBiZ0NvbG9yOiAnI0U1RjhGRicsDQogICAgICAgICAgdmFsdWVDb2xvcjogJyMxRkM2RkYnDQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICBtZWV0aW5nVHlwZUxpc3Q6IFsNCiAgICAgICAgeyBuYW1lOiAn5YW25LuW5Lya6K6uJywgdmFsdWU6IDI4LCBwZXJjZW50YWdlOiAnJywgY29sb3I6ICcjNDQ4OEVCJyB9LA0KICAgICAgICB7IG5hbWU6ICfkuLvluK3kvJrorq4nLCB2YWx1ZTogMTIsIHBlcmNlbnRhZ2U6ICcnLCBjb2xvcjogJyM0M0REQkInIH0sDQogICAgICAgIHsgbmFtZTogJ+W4uOWnlOS8muiuricsIHZhbHVlOiAxMCwgcGVyY2VudGFnZTogJycsIGNvbG9yOiAnI0ZGNjY2NScgfSwNCiAgICAgICAgeyBuYW1lOiAn5YWo5L2T5Lya6K6uJywgdmFsdWU6IDIsIHBlcmNlbnRhZ2U6ICcnLCBjb2xvcjogJyNFQ0U1MjInIH0NCiAgICAgIF0sDQogICAgICAvLyDmtLvliqjnsbvlnovnu5/orqHmlbDmja4NCiAgICAgIGFjdGl2aXR5VHlwZUxpc3Q6IFsNCiAgICAgICAgeyBuYW1lOiAn6KeG5a+fJywgdmFsdWU6IDMyLCBjb2xvcjogJyNGRjk5OTknIH0sDQogICAgICAgIHsgbmFtZTogJ+iwg+eglCcsIHZhbHVlOiAyMCwgY29sb3I6ICcjNjZCM0ZGJyB9LA0KICAgICAgICB7IG5hbWU6ICfljY/llYYnLCB2YWx1ZTogMTQsIGNvbG9yOiAnI0ZGOTk5OScgfSwNCiAgICAgICAgeyBuYW1lOiAn5a2m5Lmg5Z+56K6tJywgdmFsdWU6IDIyLCBjb2xvcjogJyM2NkIzRkYnIH0sDQogICAgICAgIHsgbmFtZTogJ+iBlOezu+eVjOWIq+e+pOS8lycsIHZhbHVlOiA4LCBjb2xvcjogJyNGRjk5OTknIH0sDQogICAgICAgIHsgbmFtZTogJ+aPkOahiOWuoeafpScsIHZhbHVlOiAyNSwgY29sb3I6ICcjNjZCM0ZGJyB9LA0KICAgICAgICB7IG5hbWU6ICfmj5DmoYjnrZTlip4nLCB2YWx1ZTogMTMsIGNvbG9yOiAnI0ZGOTk5OScgfSwNCiAgICAgICAgeyBuYW1lOiAn5o+Q5qGI6K+E6K6uJywgdmFsdWU6IDMyLCBjb2xvcjogJyM2NkIzRkYnIH0sDQogICAgICAgIHsgbmFtZTogJ+WnlOWRmOiBlOe7nOWwj+e7hCcsIHZhbHVlOiAxNSwgY29sb3I6ICcjRkY5OTk5JyB9LA0KICAgICAgICB7IG5hbWU6ICflp5TlkZjkvJrlrqLljoUnLCB2YWx1ZTogMjUsIGNvbG9yOiAnIzY2QjNGRicgfSwNCiAgICAgICAgeyBuYW1lOiAn6IGU57O756S+5Lya57uE57uHJywgdmFsdWU6IDEwLCBjb2xvcjogJyNGRjk5OTknIH0sDQogICAgICAgIHsgbmFtZTogJ+eVjOWIq+e+pOS8l+mHjeeCueWFs+WIh+mXrumimOaDheWGtemAmuaKpeS8micsIHZhbHVlOiAyMCwgY29sb3I6ICcjNjZCM0ZGJyB9LA0KICAgICAgICB7IG5hbWU6ICfnpL7mg4XmsJHmhI/luqfosIjkvJonLCB2YWx1ZTogMTYsIGNvbG9yOiAnI0ZGOTk5OScgfSwNCiAgICAgICAgeyBuYW1lOiAn5o6l5Y+X5aqS5L2T6YeH6K6/JywgdmFsdWU6IDI4LCBjb2xvcjogJyM2NkIzRkYnIH0sDQogICAgICAgIHsgbmFtZTogJ+e7j+aUv+WNj+aOqOiNkOWPguWKoOacieWFs+S8muiurua0u+WKqCcsIHZhbHVlOiA1LCBjb2xvcjogJyNGRjk5OTknIH0sDQogICAgICAgIHsgbmFtZTogJ+Wuo+iusuWFmueahOaUv+etlicsIHZhbHVlOiA3LCBjb2xvcjogJyM2NkIzRkYnIH0sDQogICAgICAgIHsgbmFtZTogJ+acjeWKoeS4uuawkScsIHZhbHVlOiAzMiwgY29sb3I6ICcjRkY5OTk5JyB9DQogICAgICBdDQogICAgfSkNCiAgICByZXR1cm4gew0KICAgICAgLi4udG9SZWZzKGRhdGEpDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\MemberPerformance.vue"], "names": [], "mappings": ";AAkDA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;EAC3C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC;QACD;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACb,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB;MACF,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7D,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7D,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7D,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC7D,CAAC;MACD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAChB,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC3C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC3C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC3C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC9C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC9C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACxD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACrD,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC9C,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC9C;IACF,CAAC;IACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChB;EACF;AACF", "file": "D:/zy/xm/h5/qdzx_h5/product-app/src/views/SmartBrainLargeScreen/component/MemberPerformance.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"MemberPerformance\">\r\n    <!-- 年度履职汇总 -->\r\n    <div class=\"performanceunit_summary_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">年度履职汇总</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"performanceunit_summary\">\r\n        <div class=\"performance_grid\">\r\n          <div v-for=\"(item, index) in performanceList\" :key=\"index\" class=\"performance_card\"\r\n            :style=\"{ background: item.bgColor }\">\r\n            <img :src=\"item.icon\" :alt=\"item.label\" class=\"icon_img\" />\r\n            <div class=\"card_content\">\r\n              <div class=\"card_label\">{{ item.label }}</div>\r\n              <div class=\"card_value\" :style=\"{ color: item.valueColor }\">{{ item.value }}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 会议类型统计 -->\r\n    <div class=\"meeting_type_statistics_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">会议类型统计</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"meeting_type_statistics\">\r\n        <PieChart id=\"meetingType\" :chart-data=\"meetingTypeList\" :radius=\"['35%', '60%']\" :center=\"['50%', '30%']\" />\r\n      </div>\r\n    </div>\r\n    <!-- 活动类型统计 -->\r\n    <div class=\"activity_type_statistics_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">活动类型统计</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"activity_type_statistics\">\r\n        <ActivityTypeChart :data=\"activityTypeList\" height=\"500px\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { toRefs, reactive } from 'vue'\r\nimport PieChart from './echartsComponent/PieChart.vue'\r\nimport ActivityTypeChart from './echartsComponent/ActivityTypeChart.vue'\r\nexport default {\r\n  name: 'MemberPerformance',\r\n  components: { PieChart, ActivityTypeChart },\r\n  setup () {\r\n    const data = reactive({\r\n      // 年度履职汇总数据\r\n      performanceList: [\r\n        {\r\n          label: '提交提案',\r\n          value: 1500,\r\n          icon: require('../../../assets/img/largeScreen/icon_proposal.png'),\r\n          bgColor: '#F1F5FF',\r\n          valueColor: '#3A61CD'\r\n        },\r\n        {\r\n          label: '提交社情民意',\r\n          value: 1057,\r\n          icon: require('../../../assets/img/largeScreen/icon_opinion.png'),\r\n          bgColor: '#DAF6F2',\r\n          valueColor: '#57BCAA'\r\n        },\r\n        {\r\n          label: '网络议政',\r\n          value: 215,\r\n          icon: require('../../../assets/img/largeScreen/icon_network.png'),\r\n          bgColor: '#E8F7FF',\r\n          valueColor: '#308FFF'\r\n        },\r\n        {\r\n          label: '参加会议',\r\n          value: 361,\r\n          icon: require('../../../assets/img/largeScreen/icon_meeting.png'),\r\n          bgColor: '#FDF8F0',\r\n          valueColor: '#EAB308'\r\n        },\r\n        {\r\n          label: '参加活动',\r\n          value: 104,\r\n          icon: require('../../../assets/img/largeScreen/icon_activity.png'),\r\n          bgColor: '#FDEFEF',\r\n          valueColor: '#FD7575'\r\n        },\r\n        {\r\n          label: '其他履职',\r\n          value: 241,\r\n          icon: require('../../../assets/img/largeScreen/icon_other.png'),\r\n          bgColor: '#E5F8FF',\r\n          valueColor: '#1FC6FF'\r\n        }\r\n      ],\r\n      meetingTypeList: [\r\n        { name: '其他会议', value: 28, percentage: '', color: '#4488EB' },\r\n        { name: '主席会议', value: 12, percentage: '', color: '#43DDBB' },\r\n        { name: '常委会议', value: 10, percentage: '', color: '#FF6665' },\r\n        { name: '全体会议', value: 2, percentage: '', color: '#ECE522' }\r\n      ],\r\n      // 活动类型统计数据\r\n      activityTypeList: [\r\n        { name: '视察', value: 32, color: '#FF9999' },\r\n        { name: '调研', value: 20, color: '#66B3FF' },\r\n        { name: '协商', value: 14, color: '#FF9999' },\r\n        { name: '学习培训', value: 22, color: '#66B3FF' },\r\n        { name: '联系界别群众', value: 8, color: '#FF9999' },\r\n        { name: '提案审查', value: 25, color: '#66B3FF' },\r\n        { name: '提案答办', value: 13, color: '#FF9999' },\r\n        { name: '提案评议', value: 32, color: '#66B3FF' },\r\n        { name: '委员联络小组', value: 15, color: '#FF9999' },\r\n        { name: '委员会客厅', value: 25, color: '#66B3FF' },\r\n        { name: '联系社会组织', value: 10, color: '#FF9999' },\r\n        { name: '界别群众重点关切问题情况通报会', value: 20, color: '#66B3FF' },\r\n        { name: '社情民意座谈会', value: 16, color: '#FF9999' },\r\n        { name: '接受媒体采访', value: 28, color: '#66B3FF' },\r\n        { name: '经政协推荐参加有关会议活动', value: 5, color: '#FF9999' },\r\n        { name: '宣讲党的政策', value: 7, color: '#66B3FF' },\r\n        { name: '服务为民', value: 32, color: '#FF9999' }\r\n      ]\r\n    })\r\n    return {\r\n      ...toRefs(data)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.MemberPerformance {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .header_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 15px 0 15px;\r\n\r\n    .header_left {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_left_line {\r\n        width: 3px;\r\n        height: 14px;\r\n        background: #007AFF;\r\n      }\r\n\r\n      .header_left_title {\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #222222;\r\n        margin-left: 8px;\r\n        font-family: Source Han Serif SC, Source Han Serif SC;\r\n      }\r\n    }\r\n\r\n    .header_right {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_right_text {\r\n        font-weight: 400;\r\n        font-size: 12px;\r\n        color: #999999;\r\n      }\r\n\r\n      .header_right_more {\r\n        font-size: 14px;\r\n        color: #0271E3;\r\n        border-radius: 14px;\r\n        border: 1px solid #0271E3;\r\n        padding: 3px 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .performanceunit_summary_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .performanceunit_summary {\r\n      padding: 12px;\r\n\r\n      .performance_grid {\r\n        display: grid;\r\n        grid-template-columns: 1fr 1fr;\r\n        gap: 10px;\r\n\r\n        .performance_card {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 16px 20px;\r\n          border-radius: 4px;\r\n\r\n          .icon_img {\r\n            width: 32px;\r\n            height: 32px;\r\n            object-fit: contain;\r\n            margin-right: 14px;\r\n          }\r\n\r\n          .card_content {\r\n            flex: 1;\r\n\r\n            .card_label {\r\n              font-size: 12px;\r\n              color: #999;\r\n              margin-bottom: 7px;\r\n            }\r\n\r\n            .card_value {\r\n              font-size: 20px;\r\n              color: #3A61CD;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .meeting_type_statistics_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .meeting_type_statistics {\r\n      width: 100%;\r\n      height: 260px;\r\n      margin-top: 20px;\r\n    }\r\n  }\r\n\r\n  .activity_type_statistics_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .activity_type_statistics {\r\n      padding: 15px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}