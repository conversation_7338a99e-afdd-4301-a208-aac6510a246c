{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue", "mtime": 1753948885976}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["ref", "onMounted", "onUnmounted", "watch", "nextTick", "echarts", "name", "props", "id", "type", "String", "required", "data", "Array", "default", "setup", "chartId", "chartInstance", "initChart", "dom", "document", "getElementById", "value", "console", "error", "init", "option", "tooltip", "trigger", "grid", "left", "right", "top", "bottom", "containLabel", "xAxis", "axisTick", "show", "axisLine", "splitLine", "axisLabel", "yAxis", "inverse", "map", "item", "series", "index", "itemStyle", "color", "x", "y", "x2", "y2", "colorStops", "offset", "<PERSON><PERSON><PERSON><PERSON>", "label", "position", "fontSize", "fontWeight", "formatter", "params", "dataIndex", "length", "substring", "lineHeight", "setOption", "resizeChart", "resize", "window", "addEventListener", "dispose", "removeEventListener", "deep"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue"], "sourcesContent": ["<template>\n  <div class=\"activity-type-chart\" :id=\"chartId\"></div>\n</template>\n\n<script>\nimport { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'ActivityTypeChart',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    data: {\n      type: Array,\n      default: () => []\n    }\n  },\n  setup (props) {\n    const chartId = ref(props.id)\n    let chartInstance = null\n\n    const initChart = () => {\n      nextTick(() => {\n        const dom = document.getElementById(chartId.value)\n        if (!dom) {\n          console.error('Chart DOM element not found:', chartId.value)\n          return\n        }\n        if (!chartInstance) {\n          chartInstance = echarts.init(dom)\n        }\n        const option = {\n          tooltip: {\n            trigger: 'item'\n          },\n          grid: {\n            left: '0%',\n            right: '2%',\n            top: '2%',\n            bottom: '2%',\n            containLabel: true\n          },\n          xAxis: {\n            type: 'value',\n            axisTick: {\n              show: false\n            },\n            axisLine: {\n              show: false\n            },\n            splitLine: {\n              show: false\n            },\n            axisLabel: {\n              show: false\n            }\n          },\n          yAxis: [\n            {\n              type: 'category',\n              inverse: true,\n              axisTick: {\n                show: false\n              },\n              axisLine: {\n                show: false\n              },\n              axisLabel: {\n                show: false // 隐藏Y轴标签，在柱状图上方显示\n              },\n              data: props.data.map(item => item.name)\n            }\n          ],\n          series: [\n            {\n              type: 'bar',\n              data: props.data.map((item, index) => ({\n                value: item.value,\n                itemStyle: {\n                  color: index % 2 === 0\n                    ? {\n                      type: 'linear',\n                      x: 0,\n                      y: 0,\n                      x2: 1,\n                      y2: 0,\n                      colorStops: [\n                        { offset: 0, color: '#FFFFFF' },\n                        { offset: 1, color: '#EF817C' }\n                      ]\n                    }\n                    : {\n                      type: 'linear',\n                      x: 0,\n                      y: 0,\n                      x2: 1,\n                      y2: 0,\n                      colorStops: [\n                        { offset: 0, color: '#FFFFFF' },\n                        { offset: 1, color: '#559FFF' }\n                      ]\n                    }\n                }\n              })),\n              barWidth: 10,\n              label: [\n                // 在柱状图上方显示活动名称\n                {\n                  show: true,\n                  position: 'top',\n                  color: '#666666',\n                  fontSize: 11,\n                  fontWeight: 400,\n                  formatter: function (params) {\n                    const item = props.data[params.dataIndex]\n                    const name = item && item.name ? item.name : ''\n                    // 如果文字太长，进行换行处理\n                    if (name.length > 10) {\n                      return name.substring(0, 10) + '\\n' + name.substring(10)\n                    }\n                    return name\n                  },\n                  offset: [0, -8],\n                  lineHeight: 14\n                },\n                // 在柱状图右侧显示数值\n                {\n                  show: true,\n                  position: 'right',\n                  color: '#333333',\n                  fontSize: 11,\n                  fontWeight: 500,\n                  formatter: '{c}',\n                  offset: [8, 0]\n                }\n              ]\n            }\n          ]\n        }\n        chartInstance.setOption(option)\n      })\n    }\n\n    const resizeChart = () => {\n      if (chartInstance) {\n        chartInstance.resize()\n      }\n    }\n\n    onMounted(() => {\n      initChart()\n      window.addEventListener('resize', resizeChart)\n    })\n\n    onUnmounted(() => {\n      if (chartInstance) {\n        chartInstance.dispose()\n      }\n      window.removeEventListener('resize', resizeChart)\n    })\n\n    watch(() => props.data, () => {\n      if (chartInstance) {\n        initChart()\n      }\n    }, { deep: true })\n\n    return {\n      chartId\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.activity-type-chart {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"], "mappings": "AAKA,SAASA,GAAG,EAAEC,SAAS,EAAEC,WAAW,EAAEC,KAAK,EAAEC,QAAO,QAAS,KAAI;AACjE,OAAO,KAAKC,OAAM,MAAO,SAAQ;AAEjC,eAAe;EACbC,IAAI,EAAE,mBAAmB;EACzBC,KAAK,EAAE;IACLC,EAAE,EAAE;MACFC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,IAAI,EAAE;MACJH,IAAI,EAAEI,KAAK;MACXC,OAAO,EAAEA,CAAA,KAAM;IACjB;EACF,CAAC;EACDC,KAAIA,CAAGR,KAAK,EAAE;IACZ,MAAMS,OAAM,GAAIhB,GAAG,CAACO,KAAK,CAACC,EAAE;IAC5B,IAAIS,aAAY,GAAI,IAAG;IAEvB,MAAMC,SAAQ,GAAIA,CAAA,KAAM;MACtBd,QAAQ,CAAC,MAAM;QACb,MAAMe,GAAE,GAAIC,QAAQ,CAACC,cAAc,CAACL,OAAO,CAACM,KAAK;QACjD,IAAI,CAACH,GAAG,EAAE;UACRI,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAER,OAAO,CAACM,KAAK;UAC3D;QACF;QACA,IAAI,CAACL,aAAa,EAAE;UAClBA,aAAY,GAAIZ,OAAO,CAACoB,IAAI,CAACN,GAAG;QAClC;QACA,MAAMO,MAAK,GAAI;UACbC,OAAO,EAAE;YACPC,OAAO,EAAE;UACX,CAAC;UACDC,IAAI,EAAE;YACJC,IAAI,EAAE,IAAI;YACVC,KAAK,EAAE,IAAI;YACXC,GAAG,EAAE,IAAI;YACTC,MAAM,EAAE,IAAI;YACZC,YAAY,EAAE;UAChB,CAAC;UACDC,KAAK,EAAE;YACL1B,IAAI,EAAE,OAAO;YACb2B,QAAQ,EAAE;cACRC,IAAI,EAAE;YACR,CAAC;YACDC,QAAQ,EAAE;cACRD,IAAI,EAAE;YACR,CAAC;YACDE,SAAS,EAAE;cACTF,IAAI,EAAE;YACR,CAAC;YACDG,SAAS,EAAE;cACTH,IAAI,EAAE;YACR;UACF,CAAC;UACDI,KAAK,EAAE,CACL;YACEhC,IAAI,EAAE,UAAU;YAChBiC,OAAO,EAAE,IAAI;YACbN,QAAQ,EAAE;cACRC,IAAI,EAAE;YACR,CAAC;YACDC,QAAQ,EAAE;cACRD,IAAI,EAAE;YACR,CAAC;YACDG,SAAS,EAAE;cACTH,IAAI,EAAE,KAAI,CAAE;YACd,CAAC;YACDzB,IAAI,EAAEL,KAAK,CAACK,IAAI,CAAC+B,GAAG,CAACC,IAAG,IAAKA,IAAI,CAACtC,IAAI;UACxC,EACD;UACDuC,MAAM,EAAE,CACN;YACEpC,IAAI,EAAE,KAAK;YACXG,IAAI,EAAEL,KAAK,CAACK,IAAI,CAAC+B,GAAG,CAAC,CAACC,IAAI,EAAEE,KAAK,MAAM;cACrCxB,KAAK,EAAEsB,IAAI,CAACtB,KAAK;cACjByB,SAAS,EAAE;gBACTC,KAAK,EAAEF,KAAI,GAAI,MAAM,IACjB;kBACArC,IAAI,EAAE,QAAQ;kBACdwC,CAAC,EAAE,CAAC;kBACJC,CAAC,EAAE,CAAC;kBACJC,EAAE,EAAE,CAAC;kBACLC,EAAE,EAAE,CAAC;kBACLC,UAAU,EAAE,CACV;oBAAEC,MAAM,EAAE,CAAC;oBAAEN,KAAK,EAAE;kBAAU,CAAC,EAC/B;oBAAEM,MAAM,EAAE,CAAC;oBAAEN,KAAK,EAAE;kBAAU;gBAElC,IACE;kBACAvC,IAAI,EAAE,QAAQ;kBACdwC,CAAC,EAAE,CAAC;kBACJC,CAAC,EAAE,CAAC;kBACJC,EAAE,EAAE,CAAC;kBACLC,EAAE,EAAE,CAAC;kBACLC,UAAU,EAAE,CACV;oBAAEC,MAAM,EAAE,CAAC;oBAAEN,KAAK,EAAE;kBAAU,CAAC,EAC/B;oBAAEM,MAAM,EAAE,CAAC;oBAAEN,KAAK,EAAE;kBAAU;gBAElC;cACJ;YACF,CAAC,CAAC,CAAC;YACHO,QAAQ,EAAE,EAAE;YACZC,KAAK,EAAE;YACL;YACA;cACEnB,IAAI,EAAE,IAAI;cACVoB,QAAQ,EAAE,KAAK;cACfT,KAAK,EAAE,SAAS;cAChBU,QAAQ,EAAE,EAAE;cACZC,UAAU,EAAE,GAAG;cACfC,SAAS,EAAE,SAAAA,CAAUC,MAAM,EAAE;gBAC3B,MAAMjB,IAAG,GAAIrC,KAAK,CAACK,IAAI,CAACiD,MAAM,CAACC,SAAS;gBACxC,MAAMxD,IAAG,GAAIsC,IAAG,IAAKA,IAAI,CAACtC,IAAG,GAAIsC,IAAI,CAACtC,IAAG,GAAI,EAAC;gBAC9C;gBACA,IAAIA,IAAI,CAACyD,MAAK,GAAI,EAAE,EAAE;kBACpB,OAAOzD,IAAI,CAAC0D,SAAS,CAAC,CAAC,EAAE,EAAE,IAAI,IAAG,GAAI1D,IAAI,CAAC0D,SAAS,CAAC,EAAE;gBACzD;gBACA,OAAO1D,IAAG;cACZ,CAAC;cACDgD,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;cACfW,UAAU,EAAE;YACd,CAAC;YACD;YACA;cACE5B,IAAI,EAAE,IAAI;cACVoB,QAAQ,EAAE,OAAO;cACjBT,KAAK,EAAE,SAAS;cAChBU,QAAQ,EAAE,EAAE;cACZC,UAAU,EAAE,GAAG;cACfC,SAAS,EAAE,KAAK;cAChBN,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;YACf;UAEJ;QAEJ;QACArC,aAAa,CAACiD,SAAS,CAACxC,MAAM;MAChC,CAAC;IACH;IAEA,MAAMyC,WAAU,GAAIA,CAAA,KAAM;MACxB,IAAIlD,aAAa,EAAE;QACjBA,aAAa,CAACmD,MAAM,CAAC;MACvB;IACF;IAEAnE,SAAS,CAAC,MAAM;MACdiB,SAAS,CAAC;MACVmD,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEH,WAAW;IAC/C,CAAC;IAEDjE,WAAW,CAAC,MAAM;MAChB,IAAIe,aAAa,EAAE;QACjBA,aAAa,CAACsD,OAAO,CAAC;MACxB;MACAF,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEL,WAAW;IAClD,CAAC;IAEDhE,KAAK,CAAC,MAAMI,KAAK,CAACK,IAAI,EAAE,MAAM;MAC5B,IAAIK,aAAa,EAAE;QACjBC,SAAS,CAAC;MACZ;IACF,CAAC,EAAE;MAAEuD,IAAI,EAAE;IAAK,CAAC;IAEjB,OAAO;MACLzD;IACF;EACF;AACF", "ignoreList": []}]}