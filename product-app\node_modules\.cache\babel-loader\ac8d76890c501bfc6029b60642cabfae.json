{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue", "mtime": 1753945303915}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["ref", "onMounted", "onUnmounted", "watch", "echarts", "__default__", "name", "props", "data", "type", "Array", "default", "height", "String", "setup", "chartContainer", "chartInstance", "initChart", "value", "init", "option", "grid", "left", "right", "top", "bottom", "containLabel", "xAxis", "show", "max", "Math", "ceil", "yAxis", "map", "item", "axisLine", "axisTick", "axisLabel", "inverse", "series", "index", "itemStyle", "color", "barHeight", "label", "position", "fontSize", "fontWeight", "formatter", "params", "dataIndex", "length", "substring", "offset", "lineHeight", "borderRadius", "animation", "animationDuration", "animationEasing", "setOption", "resizeChart", "resize", "window", "addEventListener", "dispose", "removeEventListener", "deep"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue"], "sourcesContent": ["<template>\n  <div class=\"activity-type-chart\" ref=\"chartContainer\"></div>\n</template>\n\n<script>\nimport { ref, onMounted, onUnmounted, watch } from 'vue'\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'ActivityTypeChart',\n  props: {\n    data: {\n      type: Array,\n      default: () => []\n    },\n    height: {\n      type: String,\n      default: '400px'\n    }\n  },\n  setup (props) {\n    const chartContainer = ref(null)\n    let chartInstance = null\n\n    const initChart = () => {\n      if (!chartContainer.value) return\n\n      chartInstance = echarts.init(chartContainer.value)\n\n      const option = {\n        grid: {\n          left: '5%',\n          right: '10%',\n          top: '8%',\n          bottom: '2%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'value',\n          show: false,\n          max: function (value) {\n            return Math.ceil(value.max * 1.1)\n          }\n        },\n        yAxis: {\n          type: 'category',\n          data: props.data.map(item => item.name),\n          axisLine: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            show: false // 隐藏Y轴标签，因为我们要在柱状图上方显示\n          },\n          inverse: true // 反转Y轴，让第一项在顶部\n        },\n        series: [\n          {\n            type: 'bar',\n            data: props.data.map((item, index) => ({\n              value: item.value,\n              itemStyle: {\n                color: item.color || (index % 2 === 0 ? '#FF9999' : '#66B3FF')\n              }\n            })),\n            barHeight: 14,\n            label: [\n              // 在柱状图上方显示活动名称\n              {\n                show: true,\n                position: 'top',\n                color: '#666666',\n                fontSize: 11,\n                fontWeight: 400,\n                formatter: function (params) {\n                  const item = props.data[params.dataIndex]\n                  const name = item && item.name ? item.name : ''\n                  // 如果文字太长，进行换行处理\n                  if (name.length > 10) {\n                    return name.substring(0, 10) + '\\n' + name.substring(10)\n                  }\n                  return name\n                },\n                offset: [0, -8],\n                lineHeight: 14\n              },\n              // 在柱状图右侧显示数值\n              {\n                show: true,\n                position: 'right',\n                color: '#333333',\n                fontSize: 11,\n                fontWeight: 500,\n                formatter: '{c}',\n                offset: [8, 0]\n              }\n            ],\n            itemStyle: {\n              borderRadius: [0, 4, 4, 0]\n            }\n          }\n        ],\n        animation: true,\n        animationDuration: 1000,\n        animationEasing: 'cubicOut'\n      }\n\n      chartInstance.setOption(option)\n    }\n\n    const resizeChart = () => {\n      if (chartInstance) {\n        chartInstance.resize()\n      }\n    }\n\n    onMounted(() => {\n      initChart()\n      window.addEventListener('resize', resizeChart)\n    })\n\n    onUnmounted(() => {\n      if (chartInstance) {\n        chartInstance.dispose()\n      }\n      window.removeEventListener('resize', resizeChart)\n    })\n\n    watch(() => props.data, () => {\n      if (chartInstance) {\n        initChart()\n      }\n    }, { deep: true })\n\n    return {\n      chartContainer\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.activity-type-chart {\n  width: 100%;\n  height: v-bind(height);\n}\n</style>\n"], "mappings": "AAKA,SAASA,GAAG,EAAEC,SAAS,EAAEC,WAAW,EAAEC,KAAI,QAAS,KAAI;AACvD,OAAO,KAAKC,OAAM,MAAO,SAAQ;AAEjC,MAAKC,WAAU;EACbC,IAAI,EAAE,mBAAmB;EACzBC,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC,KAAK;MACXC,OAAO,EAAEA,CAAA,KAAM;IACjB,CAAC;IACDC,MAAM,EAAE;MACNH,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX;EACF,CAAC;EACDG,KAAIA,CAAGP,KAAK,EAAE;IACZ,MAAMQ,cAAa,GAAIf,GAAG,CAAC,IAAI;IAC/B,IAAIgB,aAAY,GAAI,IAAG;IAEvB,MAAMC,SAAQ,GAAIA,CAAA,KAAM;MACtB,IAAI,CAACF,cAAc,CAACG,KAAK,EAAE;MAE3BF,aAAY,GAAIZ,OAAO,CAACe,IAAI,CAACJ,cAAc,CAACG,KAAK;MAEjD,MAAME,MAAK,GAAI;QACbC,IAAI,EAAE;UACJC,IAAI,EAAE,IAAI;UACVC,KAAK,EAAE,KAAK;UACZC,GAAG,EAAE,IAAI;UACTC,MAAM,EAAE,IAAI;UACZC,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE;UACLlB,IAAI,EAAE,OAAO;UACbmB,IAAI,EAAE,KAAK;UACXC,GAAG,EAAE,SAAAA,CAAUX,KAAK,EAAE;YACpB,OAAOY,IAAI,CAACC,IAAI,CAACb,KAAK,CAACW,GAAE,GAAI,GAAG;UAClC;QACF,CAAC;QACDG,KAAK,EAAE;UACLvB,IAAI,EAAE,UAAU;UAChBD,IAAI,EAAED,KAAK,CAACC,IAAI,CAACyB,GAAG,CAACC,IAAG,IAAKA,IAAI,CAAC5B,IAAI,CAAC;UACvC6B,QAAQ,EAAE;YACRP,IAAI,EAAE;UACR,CAAC;UACDQ,QAAQ,EAAE;YACRR,IAAI,EAAE;UACR,CAAC;UACDS,SAAS,EAAE;YACTT,IAAI,EAAE,KAAI,CAAE;UACd,CAAC;UACDU,OAAO,EAAE,IAAG,CAAE;QAChB,CAAC;QACDC,MAAM,EAAE,CACN;UACE9B,IAAI,EAAE,KAAK;UACXD,IAAI,EAAED,KAAK,CAACC,IAAI,CAACyB,GAAG,CAAC,CAACC,IAAI,EAAEM,KAAK,MAAM;YACrCtB,KAAK,EAAEgB,IAAI,CAAChB,KAAK;YACjBuB,SAAS,EAAE;cACTC,KAAK,EAAER,IAAI,CAACQ,KAAI,KAAMF,KAAI,GAAI,MAAM,IAAI,SAAQ,GAAI,SAAS;YAC/D;UACF,CAAC,CAAC,CAAC;UACHG,SAAS,EAAE,EAAE;UACbC,KAAK,EAAE;UACL;UACA;YACEhB,IAAI,EAAE,IAAI;YACViB,QAAQ,EAAE,KAAK;YACfH,KAAK,EAAE,SAAS;YAChBI,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,GAAG;YACfC,SAAS,EAAE,SAAAA,CAAUC,MAAM,EAAE;cAC3B,MAAMf,IAAG,GAAI3B,KAAK,CAACC,IAAI,CAACyC,MAAM,CAACC,SAAS;cACxC,MAAM5C,IAAG,GAAI4B,IAAG,IAAKA,IAAI,CAAC5B,IAAG,GAAI4B,IAAI,CAAC5B,IAAG,GAAI,EAAC;cAC9C;cACA,IAAIA,IAAI,CAAC6C,MAAK,GAAI,EAAE,EAAE;gBACpB,OAAO7C,IAAI,CAAC8C,SAAS,CAAC,CAAC,EAAE,EAAE,IAAI,IAAG,GAAI9C,IAAI,CAAC8C,SAAS,CAAC,EAAE;cACzD;cACA,OAAO9C,IAAG;YACZ,CAAC;YACD+C,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACfC,UAAU,EAAE;UACd,CAAC;UACD;UACA;YACE1B,IAAI,EAAE,IAAI;YACViB,QAAQ,EAAE,OAAO;YACjBH,KAAK,EAAE,SAAS;YAChBI,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,GAAG;YACfC,SAAS,EAAE,KAAK;YAChBK,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;UACf,EACD;UACDZ,SAAS,EAAE;YACTc,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;UAC3B;QACF,EACD;QACDC,SAAS,EAAE,IAAI;QACfC,iBAAiB,EAAE,IAAI;QACvBC,eAAe,EAAE;MACnB;MAEA1C,aAAa,CAAC2C,SAAS,CAACvC,MAAM;IAChC;IAEA,MAAMwC,WAAU,GAAIA,CAAA,KAAM;MACxB,IAAI5C,aAAa,EAAE;QACjBA,aAAa,CAAC6C,MAAM,CAAC;MACvB;IACF;IAEA5D,SAAS,CAAC,MAAM;MACdgB,SAAS,CAAC;MACV6C,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEH,WAAW;IAC/C,CAAC;IAED1D,WAAW,CAAC,MAAM;MAChB,IAAIc,aAAa,EAAE;QACjBA,aAAa,CAACgD,OAAO,CAAC;MACxB;MACAF,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEL,WAAW;IAClD,CAAC;IAEDzD,KAAK,CAAC,MAAMI,KAAK,CAACC,IAAI,EAAE,MAAM;MAC5B,IAAIQ,aAAa,EAAE;QACjBC,SAAS,CAAC;MACZ;IACF,CAAC,EAAE;MAAEiD,IAAI,EAAE;IAAK,CAAC;IAEjB,OAAO;MACLnD;IACF;EACF;AACF", "ignoreList": []}]}