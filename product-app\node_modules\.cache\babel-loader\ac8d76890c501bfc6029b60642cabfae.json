{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue", "mtime": 1753945158738}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["ref", "onMounted", "onUnmounted", "watch", "echarts", "__default__", "name", "props", "data", "type", "Array", "default", "height", "String", "setup", "chartContainer", "chartInstance", "initChart", "value", "init", "option", "grid", "left", "right", "top", "bottom", "containLabel", "xAxis", "show", "max", "Math", "ceil", "yAxis", "map", "item", "axisLine", "axisTick", "axisLabel", "color", "fontSize", "interval", "lineHeight", "formatter", "length", "substring", "inverse", "series", "index", "itemStyle", "barHeight", "label", "position", "fontWeight", "offset", "params", "dataIndex", "borderRadius", "animation", "animationDuration", "animationEasing", "setOption", "resizeChart", "resize", "window", "addEventListener", "dispose", "removeEventListener", "deep"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue"], "sourcesContent": ["<template>\n  <div class=\"activity-type-chart\" ref=\"chartContainer\"></div>\n</template>\n\n<script>\nimport { ref, onMounted, onUnmounted, watch } from 'vue'\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'ActivityTypeChart',\n  props: {\n    data: {\n      type: Array,\n      default: () => []\n    },\n    height: {\n      type: String,\n      default: '400px'\n    }\n  },\n  setup (props) {\n    const chartContainer = ref(null)\n    let chartInstance = null\n\n    const initChart = () => {\n      if (!chartContainer.value) return\n\n      chartInstance = echarts.init(chartContainer.value)\n\n      const option = {\n        grid: {\n          left: '35%',\n          right: '10%',\n          top: '2%',\n          bottom: '2%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'value',\n          show: false,\n          max: function (value) {\n            return Math.ceil(value.max * 1.1)\n          }\n        },\n        yAxis: {\n          type: 'category',\n          data: props.data.map(item => item.name),\n          axisLine: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            show: true,\n            color: '#666666',\n            fontSize: 11,\n            interval: 0,\n            lineHeight: 16,\n            formatter: function (value) {\n              // 如果文字太长，进行换行处理\n              if (value.length > 12) {\n                return value.substring(0, 12) + '\\n' + value.substring(12)\n              }\n              return value\n            }\n          },\n          inverse: true // 反转Y轴，让第一项在顶部\n        },\n        series: [\n          {\n            type: 'bar',\n            data: props.data.map((item, index) => ({\n              value: item.value,\n              itemStyle: {\n                color: item.color || (index % 2 === 0 ? '#FF9999' : '#66B3FF')\n              }\n            })),\n            barHeight: 14,\n            label: [\n              // 在柱状图右侧显示数值\n              {\n                show: true,\n                position: 'right',\n                color: '#333333',\n                fontSize: 11,\n                fontWeight: 500,\n                formatter: '{c}',\n                offset: [8, 0]\n              },\n              {\n                show: true,\n                position: 'top',\n                color: '#666666',\n                fontSize: 11,\n                fontWeight: 400,\n                formatter: function (params) {\n                  const item = props.data[params.dataIndex]\n                  const name = item && item.name ? item.name : ''\n                  // 如果文字太长，进行换行处理\n                  if (name.length > 10) {\n                    return name.substring(0, 10) + '\\n' + name.substring(10)\n                  }\n                  return name\n                },\n                offset: [0, -5],\n                lineHeight: 14\n              }\n            ],\n            itemStyle: {\n              borderRadius: [0, 4, 4, 0]\n            }\n          }\n        ],\n        animation: true,\n        animationDuration: 1000,\n        animationEasing: 'cubicOut'\n      }\n\n      chartInstance.setOption(option)\n    }\n\n    const resizeChart = () => {\n      if (chartInstance) {\n        chartInstance.resize()\n      }\n    }\n\n    onMounted(() => {\n      initChart()\n      window.addEventListener('resize', resizeChart)\n    })\n\n    onUnmounted(() => {\n      if (chartInstance) {\n        chartInstance.dispose()\n      }\n      window.removeEventListener('resize', resizeChart)\n    })\n\n    watch(() => props.data, () => {\n      if (chartInstance) {\n        initChart()\n      }\n    }, { deep: true })\n\n    return {\n      chartContainer\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.activity-type-chart {\n  width: 100%;\n  height: v-bind(height);\n}\n</style>\n"], "mappings": "AAKA,SAASA,GAAG,EAAEC,SAAS,EAAEC,WAAW,EAAEC,KAAI,QAAS,KAAI;AACvD,OAAO,KAAKC,OAAM,MAAO,SAAQ;AAEjC,MAAKC,WAAU;EACbC,IAAI,EAAE,mBAAmB;EACzBC,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC,KAAK;MACXC,OAAO,EAAEA,CAAA,KAAM;IACjB,CAAC;IACDC,MAAM,EAAE;MACNH,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX;EACF,CAAC;EACDG,KAAIA,CAAGP,KAAK,EAAE;IACZ,MAAMQ,cAAa,GAAIf,GAAG,CAAC,IAAI;IAC/B,IAAIgB,aAAY,GAAI,IAAG;IAEvB,MAAMC,SAAQ,GAAIA,CAAA,KAAM;MACtB,IAAI,CAACF,cAAc,CAACG,KAAK,EAAE;MAE3BF,aAAY,GAAIZ,OAAO,CAACe,IAAI,CAACJ,cAAc,CAACG,KAAK;MAEjD,MAAME,MAAK,GAAI;QACbC,IAAI,EAAE;UACJC,IAAI,EAAE,KAAK;UACXC,KAAK,EAAE,KAAK;UACZC,GAAG,EAAE,IAAI;UACTC,MAAM,EAAE,IAAI;UACZC,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE;UACLlB,IAAI,EAAE,OAAO;UACbmB,IAAI,EAAE,KAAK;UACXC,GAAG,EAAE,SAAAA,CAAUX,KAAK,EAAE;YACpB,OAAOY,IAAI,CAACC,IAAI,CAACb,KAAK,CAACW,GAAE,GAAI,GAAG;UAClC;QACF,CAAC;QACDG,KAAK,EAAE;UACLvB,IAAI,EAAE,UAAU;UAChBD,IAAI,EAAED,KAAK,CAACC,IAAI,CAACyB,GAAG,CAACC,IAAG,IAAKA,IAAI,CAAC5B,IAAI,CAAC;UACvC6B,QAAQ,EAAE;YACRP,IAAI,EAAE;UACR,CAAC;UACDQ,QAAQ,EAAE;YACRR,IAAI,EAAE;UACR,CAAC;UACDS,SAAS,EAAE;YACTT,IAAI,EAAE,IAAI;YACVU,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,EAAE;YACZC,QAAQ,EAAE,CAAC;YACXC,UAAU,EAAE,EAAE;YACdC,SAAS,EAAE,SAAAA,CAAUxB,KAAK,EAAE;cAC1B;cACA,IAAIA,KAAK,CAACyB,MAAK,GAAI,EAAE,EAAE;gBACrB,OAAOzB,KAAK,CAAC0B,SAAS,CAAC,CAAC,EAAE,EAAE,IAAI,IAAG,GAAI1B,KAAK,CAAC0B,SAAS,CAAC,EAAE;cAC3D;cACA,OAAO1B,KAAI;YACb;UACF,CAAC;UACD2B,OAAO,EAAE,IAAG,CAAE;QAChB,CAAC;QACDC,MAAM,EAAE,CACN;UACErC,IAAI,EAAE,KAAK;UACXD,IAAI,EAAED,KAAK,CAACC,IAAI,CAACyB,GAAG,CAAC,CAACC,IAAI,EAAEa,KAAK,MAAM;YACrC7B,KAAK,EAAEgB,IAAI,CAAChB,KAAK;YACjB8B,SAAS,EAAE;cACTV,KAAK,EAAEJ,IAAI,CAACI,KAAI,KAAMS,KAAI,GAAI,MAAM,IAAI,SAAQ,GAAI,SAAS;YAC/D;UACF,CAAC,CAAC,CAAC;UACHE,SAAS,EAAE,EAAE;UACbC,KAAK,EAAE;UACL;UACA;YACEtB,IAAI,EAAE,IAAI;YACVuB,QAAQ,EAAE,OAAO;YACjBb,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,EAAE;YACZa,UAAU,EAAE,GAAG;YACfV,SAAS,EAAE,KAAK;YAChBW,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;UACf,CAAC,EACD;YACEzB,IAAI,EAAE,IAAI;YACVuB,QAAQ,EAAE,KAAK;YACfb,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,EAAE;YACZa,UAAU,EAAE,GAAG;YACfV,SAAS,EAAE,SAAAA,CAAUY,MAAM,EAAE;cAC3B,MAAMpB,IAAG,GAAI3B,KAAK,CAACC,IAAI,CAAC8C,MAAM,CAACC,SAAS;cACxC,MAAMjD,IAAG,GAAI4B,IAAG,IAAKA,IAAI,CAAC5B,IAAG,GAAI4B,IAAI,CAAC5B,IAAG,GAAI,EAAC;cAC9C;cACA,IAAIA,IAAI,CAACqC,MAAK,GAAI,EAAE,EAAE;gBACpB,OAAOrC,IAAI,CAACsC,SAAS,CAAC,CAAC,EAAE,EAAE,IAAI,IAAG,GAAItC,IAAI,CAACsC,SAAS,CAAC,EAAE;cACzD;cACA,OAAOtC,IAAG;YACZ,CAAC;YACD+C,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACfZ,UAAU,EAAE;UACd,EACD;UACDO,SAAS,EAAE;YACTQ,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;UAC3B;QACF,EACD;QACDC,SAAS,EAAE,IAAI;QACfC,iBAAiB,EAAE,IAAI;QACvBC,eAAe,EAAE;MACnB;MAEA3C,aAAa,CAAC4C,SAAS,CAACxC,MAAM;IAChC;IAEA,MAAMyC,WAAU,GAAIA,CAAA,KAAM;MACxB,IAAI7C,aAAa,EAAE;QACjBA,aAAa,CAAC8C,MAAM,CAAC;MACvB;IACF;IAEA7D,SAAS,CAAC,MAAM;MACdgB,SAAS,CAAC;MACV8C,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEH,WAAW;IAC/C,CAAC;IAED3D,WAAW,CAAC,MAAM;MAChB,IAAIc,aAAa,EAAE;QACjBA,aAAa,CAACiD,OAAO,CAAC;MACxB;MACAF,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEL,WAAW;IAClD,CAAC;IAED1D,KAAK,CAAC,MAAMI,KAAK,CAACC,IAAI,EAAE,MAAM;MAC5B,IAAIQ,aAAa,EAAE;QACjBC,SAAS,CAAC;MACZ;IACF,CAAC,EAAE;MAAEkD,IAAI,EAAE;IAAK,CAAC;IAEjB,OAAO;MACLpD;IACF;EACF;AACF", "ignoreList": []}]}