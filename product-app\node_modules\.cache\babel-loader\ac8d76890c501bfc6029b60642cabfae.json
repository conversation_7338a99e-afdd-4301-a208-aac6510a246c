{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue", "mtime": 1753949462545}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["ref", "onMounted", "onUnmounted", "watch", "nextTick", "echarts", "name", "props", "id", "type", "String", "required", "data", "Array", "default", "setup", "chartId", "chartInstance", "initChart", "dom", "document", "getElementById", "value", "console", "error", "init", "option", "tooltip", "trigger", "grid", "left", "right", "top", "bottom", "containLabel", "xAxis", "axisTick", "show", "axisLine", "splitLine", "axisLabel", "yAxis", "inverse", "map", "item", "series", "index", "itemStyle", "color", "x", "y", "x2", "y2", "colorStops", "offset", "<PERSON><PERSON><PERSON><PERSON>", "label", "position", "fontSize", "fontWeight", "formatter", "params", "dataIndex", "lineHeight", "setOption", "resizeChart", "resize", "window", "addEventListener", "dispose", "removeEventListener", "deep"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue"], "sourcesContent": ["<template>\n  <div class=\"activity-type-chart\" :id=\"chartId\"></div>\n</template>\n\n<script>\nimport { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'ActivityTypeChart',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    data: {\n      type: Array,\n      default: () => []\n    }\n  },\n  setup (props) {\n    const chartId = ref(props.id)\n    let chartInstance = null\n\n    const initChart = () => {\n      nextTick(() => {\n        const dom = document.getElementById(chartId.value)\n        if (!dom) {\n          console.error('Chart DOM element not found:', chartId.value)\n          return\n        }\n        if (!chartInstance) {\n          chartInstance = echarts.init(dom)\n        }\n        const option = {\n          tooltip: {\n            trigger: 'item'\n          },\n          grid: {\n            left: '5%',\n            right: '15%',\n            top: '12%',\n            bottom: '2%',\n            containLabel: true\n          },\n          xAxis: {\n            type: 'value',\n            axisTick: {\n              show: false\n            },\n            axisLine: {\n              show: false\n            },\n            splitLine: {\n              show: false\n            },\n            axisLabel: {\n              show: false\n            }\n          },\n          yAxis: [\n            {\n              type: 'category',\n              inverse: true,\n              axisTick: {\n                show: false\n              },\n              axisLine: {\n                show: false\n              },\n              axisLabel: {\n                show: false // 隐藏Y轴标签，改用柱状图标签显示\n              },\n              data: props.data.map(item => item.name)\n            }\n          ],\n          series: [\n            {\n              type: 'bar',\n              data: props.data.map((item, index) => ({\n                value: item.value,\n                itemStyle: {\n                  color: index % 2 === 0\n                    ? {\n                      type: 'linear',\n                      x: 0,\n                      y: 0,\n                      x2: 1,\n                      y2: 0,\n                      colorStops: [\n                        { offset: 0, color: '#FFFFFF' },\n                        { offset: 1, color: '#EF817C' }\n                      ]\n                    }\n                    : {\n                      type: 'linear',\n                      x: 0,\n                      y: 0,\n                      x2: 1,\n                      y2: 0,\n                      colorStops: [\n                        { offset: 0, color: '#FFFFFF' },\n                        { offset: 1, color: '#559FFF' }\n                      ]\n                    }\n                }\n              })),\n              barWidth: 10,\n              label: [\n                // 在柱状图上方显示活动名称\n                {\n                  show: true,\n                  position: 'top',\n                  color: '#666666',\n                  fontSize: 11,\n                  fontWeight: 400,\n                  formatter: function (params) {\n                    const item = props.data[params.dataIndex]\n                    return item ? item.name : ''\n                  },\n                  offset: [0, -5],\n                  lineHeight: 14\n                },\n                // 在柱状图右侧显示数值\n                {\n                  show: true,\n                  position: 'right',\n                  color: '#999',\n                  fontSize: 12,\n                  formatter: '{c}',\n                  offset: [8, 0]\n                }\n              ]\n            }\n          ]\n        }\n        chartInstance.setOption(option)\n      })\n    }\n\n    const resizeChart = () => {\n      if (chartInstance) {\n        chartInstance.resize()\n      }\n    }\n\n    onMounted(() => {\n      initChart()\n      window.addEventListener('resize', resizeChart)\n    })\n\n    onUnmounted(() => {\n      if (chartInstance) {\n        chartInstance.dispose()\n      }\n      window.removeEventListener('resize', resizeChart)\n    })\n\n    watch(() => props.data, () => {\n      if (chartInstance) {\n        initChart()\n      }\n    }, { deep: true })\n\n    return {\n      chartId\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.activity-type-chart {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"], "mappings": "AAKA,SAASA,GAAG,EAAEC,SAAS,EAAEC,WAAW,EAAEC,KAAK,EAAEC,QAAO,QAAS,KAAI;AACjE,OAAO,KAAKC,OAAM,MAAO,SAAQ;AAEjC,eAAe;EACbC,IAAI,EAAE,mBAAmB;EACzBC,KAAK,EAAE;IACLC,EAAE,EAAE;MACFC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,IAAI,EAAE;MACJH,IAAI,EAAEI,KAAK;MACXC,OAAO,EAAEA,CAAA,KAAM;IACjB;EACF,CAAC;EACDC,KAAIA,CAAGR,KAAK,EAAE;IACZ,MAAMS,OAAM,GAAIhB,GAAG,CAACO,KAAK,CAACC,EAAE;IAC5B,IAAIS,aAAY,GAAI,IAAG;IAEvB,MAAMC,SAAQ,GAAIA,CAAA,KAAM;MACtBd,QAAQ,CAAC,MAAM;QACb,MAAMe,GAAE,GAAIC,QAAQ,CAACC,cAAc,CAACL,OAAO,CAACM,KAAK;QACjD,IAAI,CAACH,GAAG,EAAE;UACRI,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAER,OAAO,CAACM,KAAK;UAC3D;QACF;QACA,IAAI,CAACL,aAAa,EAAE;UAClBA,aAAY,GAAIZ,OAAO,CAACoB,IAAI,CAACN,GAAG;QAClC;QACA,MAAMO,MAAK,GAAI;UACbC,OAAO,EAAE;YACPC,OAAO,EAAE;UACX,CAAC;UACDC,IAAI,EAAE;YACJC,IAAI,EAAE,IAAI;YACVC,KAAK,EAAE,KAAK;YACZC,GAAG,EAAE,KAAK;YACVC,MAAM,EAAE,IAAI;YACZC,YAAY,EAAE;UAChB,CAAC;UACDC,KAAK,EAAE;YACL1B,IAAI,EAAE,OAAO;YACb2B,QAAQ,EAAE;cACRC,IAAI,EAAE;YACR,CAAC;YACDC,QAAQ,EAAE;cACRD,IAAI,EAAE;YACR,CAAC;YACDE,SAAS,EAAE;cACTF,IAAI,EAAE;YACR,CAAC;YACDG,SAAS,EAAE;cACTH,IAAI,EAAE;YACR;UACF,CAAC;UACDI,KAAK,EAAE,CACL;YACEhC,IAAI,EAAE,UAAU;YAChBiC,OAAO,EAAE,IAAI;YACbN,QAAQ,EAAE;cACRC,IAAI,EAAE;YACR,CAAC;YACDC,QAAQ,EAAE;cACRD,IAAI,EAAE;YACR,CAAC;YACDG,SAAS,EAAE;cACTH,IAAI,EAAE,KAAI,CAAE;YACd,CAAC;YACDzB,IAAI,EAAEL,KAAK,CAACK,IAAI,CAAC+B,GAAG,CAACC,IAAG,IAAKA,IAAI,CAACtC,IAAI;UACxC,EACD;UACDuC,MAAM,EAAE,CACN;YACEpC,IAAI,EAAE,KAAK;YACXG,IAAI,EAAEL,KAAK,CAACK,IAAI,CAAC+B,GAAG,CAAC,CAACC,IAAI,EAAEE,KAAK,MAAM;cACrCxB,KAAK,EAAEsB,IAAI,CAACtB,KAAK;cACjByB,SAAS,EAAE;gBACTC,KAAK,EAAEF,KAAI,GAAI,MAAM,IACjB;kBACArC,IAAI,EAAE,QAAQ;kBACdwC,CAAC,EAAE,CAAC;kBACJC,CAAC,EAAE,CAAC;kBACJC,EAAE,EAAE,CAAC;kBACLC,EAAE,EAAE,CAAC;kBACLC,UAAU,EAAE,CACV;oBAAEC,MAAM,EAAE,CAAC;oBAAEN,KAAK,EAAE;kBAAU,CAAC,EAC/B;oBAAEM,MAAM,EAAE,CAAC;oBAAEN,KAAK,EAAE;kBAAU;gBAElC,IACE;kBACAvC,IAAI,EAAE,QAAQ;kBACdwC,CAAC,EAAE,CAAC;kBACJC,CAAC,EAAE,CAAC;kBACJC,EAAE,EAAE,CAAC;kBACLC,EAAE,EAAE,CAAC;kBACLC,UAAU,EAAE,CACV;oBAAEC,MAAM,EAAE,CAAC;oBAAEN,KAAK,EAAE;kBAAU,CAAC,EAC/B;oBAAEM,MAAM,EAAE,CAAC;oBAAEN,KAAK,EAAE;kBAAU;gBAElC;cACJ;YACF,CAAC,CAAC,CAAC;YACHO,QAAQ,EAAE,EAAE;YACZC,KAAK,EAAE;YACL;YACA;cACEnB,IAAI,EAAE,IAAI;cACVoB,QAAQ,EAAE,KAAK;cACfT,KAAK,EAAE,SAAS;cAChBU,QAAQ,EAAE,EAAE;cACZC,UAAU,EAAE,GAAG;cACfC,SAAS,EAAE,SAAAA,CAAUC,MAAM,EAAE;gBAC3B,MAAMjB,IAAG,GAAIrC,KAAK,CAACK,IAAI,CAACiD,MAAM,CAACC,SAAS;gBACxC,OAAOlB,IAAG,GAAIA,IAAI,CAACtC,IAAG,GAAI,EAAC;cAC7B,CAAC;cACDgD,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;cACfS,UAAU,EAAE;YACd,CAAC;YACD;YACA;cACE1B,IAAI,EAAE,IAAI;cACVoB,QAAQ,EAAE,OAAO;cACjBT,KAAK,EAAE,MAAM;cACbU,QAAQ,EAAE,EAAE;cACZE,SAAS,EAAE,KAAK;cAChBN,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;YACf;UAEJ;QAEJ;QACArC,aAAa,CAAC+C,SAAS,CAACtC,MAAM;MAChC,CAAC;IACH;IAEA,MAAMuC,WAAU,GAAIA,CAAA,KAAM;MACxB,IAAIhD,aAAa,EAAE;QACjBA,aAAa,CAACiD,MAAM,CAAC;MACvB;IACF;IAEAjE,SAAS,CAAC,MAAM;MACdiB,SAAS,CAAC;MACViD,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEH,WAAW;IAC/C,CAAC;IAED/D,WAAW,CAAC,MAAM;MAChB,IAAIe,aAAa,EAAE;QACjBA,aAAa,CAACoD,OAAO,CAAC;MACxB;MACAF,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEL,WAAW;IAClD,CAAC;IAED9D,KAAK,CAAC,MAAMI,KAAK,CAACK,IAAI,EAAE,MAAM;MAC5B,IAAIK,aAAa,EAAE;QACjBC,SAAS,CAAC;MACZ;IACF,CAAC,EAAE;MAAEqD,IAAI,EAAE;IAAK,CAAC;IAEjB,OAAO;MACLvD;IACF;EACF;AACF", "ignoreList": []}]}