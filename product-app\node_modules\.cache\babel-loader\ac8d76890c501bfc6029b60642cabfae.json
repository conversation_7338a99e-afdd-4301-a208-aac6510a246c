{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue", "mtime": 1753944306570}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["ref", "onMounted", "onUnmounted", "watch", "echarts", "__default__", "name", "props", "data", "type", "Array", "default", "height", "String", "setup", "chartContainer", "chartInstance", "initChart", "value", "init", "option", "grid", "left", "right", "top", "bottom", "containLabel", "xAxis", "show", "max", "Math", "ceil", "yAxis", "map", "item", "axisLine", "axisTick", "axisLabel", "color", "fontSize", "interval", "formatter", "length", "substring", "inverse", "series", "index", "itemStyle", "barHeight", "label", "position", "fontWeight", "borderRadius", "animation", "animationDuration", "animationEasing", "setOption", "resizeChart", "resize", "window", "addEventListener", "dispose", "removeEventListener", "deep"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue"], "sourcesContent": ["<template>\n  <div class=\"activity-type-chart\" ref=\"chartContainer\"></div>\n</template>\n\n<script>\nimport { ref, onMounted, onUnmounted, watch } from 'vue'\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'ActivityTypeChart',\n  props: {\n    data: {\n      type: Array,\n      default: () => []\n    },\n    height: {\n      type: String,\n      default: '400px'\n    }\n  },\n  setup(props) {\n    const chartContainer = ref(null)\n    let chartInstance = null\n\n    const initChart = () => {\n      if (!chartContainer.value) return\n\n      chartInstance = echarts.init(chartContainer.value)\n      \n      const option = {\n        grid: {\n          left: '15%',\n          right: '10%',\n          top: '5%',\n          bottom: '5%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'value',\n          show: false,\n          max: function(value) {\n            return Math.ceil(value.max * 1.1)\n          }\n        },\n        yAxis: {\n          type: 'category',\n          data: props.data.map(item => item.name),\n          axisLine: {\n            show: false\n          },\n          axisTick: {\n            show: false\n          },\n          axisLabel: {\n            color: '#666666',\n            fontSize: 12,\n            interval: 0,\n            formatter: function(value) {\n              // 如果文字太长，进行换行处理\n              if (value.length > 8) {\n                return value.substring(0, 8) + '\\n' + value.substring(8)\n              }\n              return value\n            }\n          },\n          inverse: true // 反转Y轴，让第一项在顶部\n        },\n        series: [\n          {\n            type: 'bar',\n            data: props.data.map((item, index) => ({\n              value: item.value,\n              itemStyle: {\n                color: item.color || (index % 2 === 0 ? '#FF9999' : '#66B3FF')\n              }\n            })),\n            barHeight: 16,\n            label: {\n              show: true,\n              position: 'right',\n              color: '#333333',\n              fontSize: 12,\n              fontWeight: 500,\n              formatter: '{c}'\n            },\n            itemStyle: {\n              borderRadius: [0, 8, 8, 0]\n            }\n          }\n        ],\n        animation: true,\n        animationDuration: 1000,\n        animationEasing: 'cubicOut'\n      }\n\n      chartInstance.setOption(option)\n    }\n\n    const resizeChart = () => {\n      if (chartInstance) {\n        chartInstance.resize()\n      }\n    }\n\n    onMounted(() => {\n      initChart()\n      window.addEventListener('resize', resizeChart)\n    })\n\n    onUnmounted(() => {\n      if (chartInstance) {\n        chartInstance.dispose()\n      }\n      window.removeEventListener('resize', resizeChart)\n    })\n\n    watch(() => props.data, () => {\n      if (chartInstance) {\n        initChart()\n      }\n    }, { deep: true })\n\n    return {\n      chartContainer\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.activity-type-chart {\n  width: 100%;\n  height: v-bind(height);\n}\n</style>\n"], "mappings": "AAKA,SAASA,GAAG,EAAEC,SAAS,EAAEC,WAAW,EAAEC,KAAI,QAAS,KAAI;AACvD,OAAO,KAAKC,OAAM,MAAO,SAAQ;AAEjC,MAAKC,WAAU;EACbC,IAAI,EAAE,mBAAmB;EACzBC,KAAK,EAAE;IACLC,IAAI,EAAE;MACJC,IAAI,EAAEC,KAAK;MACXC,OAAO,EAAEA,CAAA,KAAM;IACjB,CAAC;IACDC,MAAM,EAAE;MACNH,IAAI,EAAEI,MAAM;MACZF,OAAO,EAAE;IACX;EACF,CAAC;EACDG,KAAKA,CAACP,KAAK,EAAE;IACX,MAAMQ,cAAa,GAAIf,GAAG,CAAC,IAAI;IAC/B,IAAIgB,aAAY,GAAI,IAAG;IAEvB,MAAMC,SAAQ,GAAIA,CAAA,KAAM;MACtB,IAAI,CAACF,cAAc,CAACG,KAAK,EAAE;MAE3BF,aAAY,GAAIZ,OAAO,CAACe,IAAI,CAACJ,cAAc,CAACG,KAAK;MAEjD,MAAME,MAAK,GAAI;QACbC,IAAI,EAAE;UACJC,IAAI,EAAE,KAAK;UACXC,KAAK,EAAE,KAAK;UACZC,GAAG,EAAE,IAAI;UACTC,MAAM,EAAE,IAAI;UACZC,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE;UACLlB,IAAI,EAAE,OAAO;UACbmB,IAAI,EAAE,KAAK;UACXC,GAAG,EAAE,SAAAA,CAASX,KAAK,EAAE;YACnB,OAAOY,IAAI,CAACC,IAAI,CAACb,KAAK,CAACW,GAAE,GAAI,GAAG;UAClC;QACF,CAAC;QACDG,KAAK,EAAE;UACLvB,IAAI,EAAE,UAAU;UAChBD,IAAI,EAAED,KAAK,CAACC,IAAI,CAACyB,GAAG,CAACC,IAAG,IAAKA,IAAI,CAAC5B,IAAI,CAAC;UACvC6B,QAAQ,EAAE;YACRP,IAAI,EAAE;UACR,CAAC;UACDQ,QAAQ,EAAE;YACRR,IAAI,EAAE;UACR,CAAC;UACDS,SAAS,EAAE;YACTC,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,EAAE;YACZC,QAAQ,EAAE,CAAC;YACXC,SAAS,EAAE,SAAAA,CAASvB,KAAK,EAAE;cACzB;cACA,IAAIA,KAAK,CAACwB,MAAK,GAAI,CAAC,EAAE;gBACpB,OAAOxB,KAAK,CAACyB,SAAS,CAAC,CAAC,EAAE,CAAC,IAAI,IAAG,GAAIzB,KAAK,CAACyB,SAAS,CAAC,CAAC;cACzD;cACA,OAAOzB,KAAI;YACb;UACF,CAAC;UACD0B,OAAO,EAAE,IAAG,CAAE;QAChB,CAAC;QACDC,MAAM,EAAE,CACN;UACEpC,IAAI,EAAE,KAAK;UACXD,IAAI,EAAED,KAAK,CAACC,IAAI,CAACyB,GAAG,CAAC,CAACC,IAAI,EAAEY,KAAK,MAAM;YACrC5B,KAAK,EAAEgB,IAAI,CAAChB,KAAK;YACjB6B,SAAS,EAAE;cACTT,KAAK,EAAEJ,IAAI,CAACI,KAAI,KAAMQ,KAAI,GAAI,MAAM,IAAI,SAAQ,GAAI,SAAS;YAC/D;UACF,CAAC,CAAC,CAAC;UACHE,SAAS,EAAE,EAAE;UACbC,KAAK,EAAE;YACLrB,IAAI,EAAE,IAAI;YACVsB,QAAQ,EAAE,OAAO;YACjBZ,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE,EAAE;YACZY,UAAU,EAAE,GAAG;YACfV,SAAS,EAAE;UACb,CAAC;UACDM,SAAS,EAAE;YACTK,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;UAC3B;QACF,EACD;QACDC,SAAS,EAAE,IAAI;QACfC,iBAAiB,EAAE,IAAI;QACvBC,eAAe,EAAE;MACnB;MAEAvC,aAAa,CAACwC,SAAS,CAACpC,MAAM;IAChC;IAEA,MAAMqC,WAAU,GAAIA,CAAA,KAAM;MACxB,IAAIzC,aAAa,EAAE;QACjBA,aAAa,CAAC0C,MAAM,CAAC;MACvB;IACF;IAEAzD,SAAS,CAAC,MAAM;MACdgB,SAAS,CAAC;MACV0C,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEH,WAAW;IAC/C,CAAC;IAEDvD,WAAW,CAAC,MAAM;MAChB,IAAIc,aAAa,EAAE;QACjBA,aAAa,CAAC6C,OAAO,CAAC;MACxB;MACAF,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEL,WAAW;IAClD,CAAC;IAEDtD,KAAK,CAAC,MAAMI,KAAK,CAACC,IAAI,EAAE,MAAM;MAC5B,IAAIQ,aAAa,EAAE;QACjBC,SAAS,CAAC;MACZ;IACF,CAAC,EAAE;MAAE8C,IAAI,EAAE;IAAK,CAAC;IAEjB,OAAO;MACLhD;IACF;EACF;AACF", "ignoreList": []}]}