{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue", "mtime": 1753951703798}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcmVmLCBvbk1vdW50ZWQsIG9uVW5tb3VudGVkLCB3YXRjaCwgbmV4dFRpY2sgfSBmcm9tICd2dWUnOwppbXBvcnQgKiBhcyBlY2hhcnRzIGZyb20gJ2VjaGFydHMnOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0FjdGl2aXR5VHlwZUNoYXJ0JywKICBwcm9wczogewogICAgaWQ6IHsKICAgICAgdHlwZTogU3RyaW5nLAogICAgICByZXF1aXJlZDogdHJ1ZQogICAgfSwKICAgIGRhdGE6IHsKICAgICAgdHlwZTogQXJyYXksCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdCiAgICB9CiAgfSwKICBzZXR1cChwcm9wcykgewogICAgY29uc3QgY2hhcnRJZCA9IHJlZihwcm9wcy5pZCk7CiAgICBsZXQgY2hhcnRJbnN0YW5jZSA9IG51bGw7CiAgICBjb25zdCBpbml0Q2hhcnQgPSAoKSA9PiB7CiAgICAgIHZhciBjaGFydHMgPSB7CiAgICAgICAgY2l0eUxpc3Q6IHByb3BzLmRhdGEubWFwKHYgPT4gdi5uYW1lKSwKICAgICAgICBjaXR5RGF0YTogcHJvcHMuZGF0YS5tYXAodiA9PiB2LnZhbHVlKQogICAgICB9OwogICAgICB2YXIgdG9wMTBDaXR5TGlzdCA9IGNoYXJ0cy5jaXR5TGlzdDsKICAgICAgdmFyIHRvcDEwQ2l0eURhdGEgPSBjaGFydHMuY2l0eURhdGE7CiAgICAgIGNvbnN0IGxpbmVZID0gW107CiAgICAgIGNvbnN0IGxpbmVUID0gW107CiAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgY2hhcnRzLmNpdHlMaXN0Lmxlbmd0aDsgaSsrKSB7CiAgICAgICAgdmFyIHggPSBpOwogICAgICAgIGlmICh4ID4gMSkgewogICAgICAgICAgeCA9IDI7CiAgICAgICAgfQogICAgICAgIHZhciBkYXRhID0gewogICAgICAgICAgbmFtZTogY2hhcnRzLmNpdHlMaXN0W2ldLAogICAgICAgICAgdmFsdWU6IHRvcDEwQ2l0eURhdGFbaV0sCiAgICAgICAgICBiYXJHYXA6ICctMTAwJScsCiAgICAgICAgICBpdGVtU3R5bGU6IHsKICAgICAgICAgICAgY29sb3I6IHsKICAgICAgICAgICAgICB0eXBlOiAnbGluZWFyJywKICAgICAgICAgICAgICB4OiAwLAogICAgICAgICAgICAgIHk6IDAsCiAgICAgICAgICAgICAgeDI6IDEsCiAgICAgICAgICAgICAgeTI6IDAsCiAgICAgICAgICAgICAgY29sb3JTdG9wczogW3sKICAgICAgICAgICAgICAgIG9mZnNldDogMCwKICAgICAgICAgICAgICAgIGNvbG9yOiAnI0ZGRkZGRicKICAgICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgICBvZmZzZXQ6IDEsCiAgICAgICAgICAgICAgICBjb2xvcjogJyM1NTlGRkYnCiAgICAgICAgICAgICAgfV0KICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH07CiAgICAgICAgdmFyIGRhdGExID0gewogICAgICAgICAgdmFsdWU6IHRvcDEwQ2l0eURhdGFbaV0sCiAgICAgICAgICBsYWJlbDogewogICAgICAgICAgICBzaG93OiB0cnVlLAogICAgICAgICAgICBwb3NpdGlvbjogJ3JpZ2h0JywKICAgICAgICAgICAgY29sb3I6ICcjOTk5JywKICAgICAgICAgICAgZm9udFNpemU6IDE0LAogICAgICAgICAgICBkaXN0YW5jZTogMTAKICAgICAgICAgIH0sCiAgICAgICAgICBpdGVtU3R5bGU6IHsKICAgICAgICAgICAgY29sb3I6IHsKICAgICAgICAgICAgICB0eXBlOiAnbGluZWFyJywKICAgICAgICAgICAgICB4OiAwLAogICAgICAgICAgICAgIHk6IDAsCiAgICAgICAgICAgICAgeDI6IDEsCiAgICAgICAgICAgICAgeTI6IDAsCiAgICAgICAgICAgICAgY29sb3JTdG9wczogW3sKICAgICAgICAgICAgICAgIG9mZnNldDogMCwKICAgICAgICAgICAgICAgIGNvbG9yOiAnI0ZGRkZGRicKICAgICAgICAgICAgICB9LCB7CiAgICAgICAgICAgICAgICBvZmZzZXQ6IDEsCiAgICAgICAgICAgICAgICBjb2xvcjogJyNFRjgxN0MnCiAgICAgICAgICAgICAgfV0KICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIH07CiAgICAgICAgbGluZVkucHVzaChkYXRhKTsKICAgICAgICBsaW5lVC5wdXNoKGRhdGExKTsKICAgICAgICBjb25zb2xlLmxvZygnbGluZVQ9PT0+JywgbGluZVQpOwogICAgICAgIGNvbnNvbGUubG9nKCdsaW5lVD09PT4nLCBsaW5lVCk7CiAgICAgIH0KICAgICAgbmV4dFRpY2soKCkgPT4gewogICAgICAgIGNvbnN0IGRvbSA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKGNoYXJ0SWQudmFsdWUpOwogICAgICAgIGlmICghZG9tKSB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCdDaGFydCBET00gZWxlbWVudCBub3QgZm91bmQ6JywgY2hhcnRJZC52YWx1ZSk7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICAgIGlmICghY2hhcnRJbnN0YW5jZSkgewogICAgICAgICAgY2hhcnRJbnN0YW5jZSA9IGVjaGFydHMuaW5pdChkb20pOwogICAgICAgIH0KICAgICAgICBjb25zdCBvcHRpb24gPSB7CiAgICAgICAgICB0b29sdGlwOiB7CiAgICAgICAgICAgIHRyaWdnZXI6ICdpdGVtJywKICAgICAgICAgICAgZm9ybWF0dGVyOiBwID0+IHsKICAgICAgICAgICAgICBpZiAocC5zZXJpZXNOYW1lID09PSAndG90YWwnKSB7CiAgICAgICAgICAgICAgICByZXR1cm4gJyc7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIHJldHVybiBgJHtwLm5hbWV9PGJyLz4ke3AudmFsdWV9YDsKICAgICAgICAgICAgfQogICAgICAgICAgfSwKICAgICAgICAgIGdyaWQ6IHsKICAgICAgICAgICAgbGVmdDogJzAlJywKICAgICAgICAgICAgcmlnaHQ6ICcyMCUnLAogICAgICAgICAgICB0b3A6ICcyJScsCiAgICAgICAgICAgIGJvdHRvbTogJzIlJywKICAgICAgICAgICAgY29udGFpbkxhYmVsOiB0cnVlCiAgICAgICAgICB9LAogICAgICAgICAgeUF4aXM6IHsKICAgICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywKICAgICAgICAgICAgaW52ZXJzZTogdHJ1ZSwKICAgICAgICAgICAgYXhpc1RpY2s6IHsKICAgICAgICAgICAgICBzaG93OiBmYWxzZQogICAgICAgICAgICB9LAogICAgICAgICAgICBheGlzTGluZTogewogICAgICAgICAgICAgIHNob3c6IGZhbHNlCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGF4aXNMYWJlbDogewogICAgICAgICAgICAgIHNob3c6IGZhbHNlLAogICAgICAgICAgICAgIGluc2lkZTogZmFsc2UKICAgICAgICAgICAgfSwKICAgICAgICAgICAgZGF0YTogdG9wMTBDaXR5TGlzdAogICAgICAgICAgfSwKICAgICAgICAgIHhBeGlzOiB7CiAgICAgICAgICAgIHR5cGU6ICd2YWx1ZScsCiAgICAgICAgICAgIGF4aXNUaWNrOiB7CiAgICAgICAgICAgICAgc2hvdzogZmFsc2UKICAgICAgICAgICAgfSwKICAgICAgICAgICAgYXhpc0xpbmU6IHsKICAgICAgICAgICAgICBzaG93OiBmYWxzZQogICAgICAgICAgICB9LAogICAgICAgICAgICBzcGxpdExpbmU6IHsKICAgICAgICAgICAgICBzaG93OiBmYWxzZQogICAgICAgICAgICB9LAogICAgICAgICAgICBheGlzTGFiZWw6IHsKICAgICAgICAgICAgICBzaG93OiBmYWxzZQogICAgICAgICAgICB9CiAgICAgICAgICB9LAogICAgICAgICAgc2VyaWVzOiBbewogICAgICAgICAgICBuYW1lOiAndG90YWwnLAogICAgICAgICAgICB0eXBlOiAnYmFyJywKICAgICAgICAgICAgYmFyR2FwOiAnLTEwMCUnLAogICAgICAgICAgICBiYXJXaWR0aDogMTAsCiAgICAgICAgICAgIGRhdGE6IGxpbmVULAogICAgICAgICAgICBsZWdlbmRIb3Zlckxpbms6IGZhbHNlCiAgICAgICAgICB9LCB7CiAgICAgICAgICAgIG5hbWU6ICdiYXInLAogICAgICAgICAgICB0eXBlOiAnYmFyJywKICAgICAgICAgICAgYmFyV2lkdGg6IDEwLAogICAgICAgICAgICBkYXRhOiBsaW5lWSwKICAgICAgICAgICAgbGFiZWw6IHsKICAgICAgICAgICAgICBub3JtYWw6IHsKICAgICAgICAgICAgICAgIHNob3c6IHRydWUsCiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzEycHgnLAogICAgICAgICAgICAgICAgY29sb3I6ICcjOTk5JywKICAgICAgICAgICAgICAgIHBvc2l0aW9uOiBbMCwgJy0yMHB4J10sCiAgICAgICAgICAgICAgICBmb3JtYXR0ZXI6ICd7Yn0nCiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9XQogICAgICAgIH07CiAgICAgICAgLy8gICBjb25zdCBvcHRpb24gPSB7CgogICAgICAgIC8vICAgICB4QXhpczogewogICAgICAgIC8vICAgICAgIHR5cGU6ICd2YWx1ZScsCiAgICAgICAgLy8gICAgICAgYXhpc1RpY2s6IHsKICAgICAgICAvLyAgICAgICAgIHNob3c6IGZhbHNlCiAgICAgICAgLy8gICAgICAgfSwKICAgICAgICAvLyAgICAgICBheGlzTGluZTogewogICAgICAgIC8vICAgICAgICAgc2hvdzogZmFsc2UKICAgICAgICAvLyAgICAgICB9LAogICAgICAgIC8vICAgICAgIHNwbGl0TGluZTogewogICAgICAgIC8vICAgICAgICAgc2hvdzogZmFsc2UKICAgICAgICAvLyAgICAgICB9LAogICAgICAgIC8vICAgICAgIGF4aXNMYWJlbDogewogICAgICAgIC8vICAgICAgICAgc2hvdzogZmFsc2UKICAgICAgICAvLyAgICAgICB9CiAgICAgICAgLy8gICAgIH0sCiAgICAgICAgLy8gICAgIHlBeGlzOiBbCiAgICAgICAgLy8gICAgICAgewogICAgICAgIC8vICAgICAgICAgdHlwZTogJ2NhdGVnb3J5JywKICAgICAgICAvLyAgICAgICAgIGludmVyc2U6IHRydWUsCiAgICAgICAgLy8gICAgICAgICBheGlzVGljazogewogICAgICAgIC8vICAgICAgICAgICBzaG93OiBmYWxzZQogICAgICAgIC8vICAgICAgICAgfSwKICAgICAgICAvLyAgICAgICAgIGF4aXNMaW5lOiB7CiAgICAgICAgLy8gICAgICAgICAgIHNob3c6IGZhbHNlCiAgICAgICAgLy8gICAgICAgICB9LAogICAgICAgIC8vICAgICAgICAgYXhpc0xhYmVsOiB7CiAgICAgICAgLy8gICAgICAgICAgIHNob3c6IGZhbHNlIC8vIOmakOiXj1novbTmoIfnrb7vvIzlkI3np7DlsIbmmL7npLrlnKjmn7Hnirblm77kuIrmlrkKICAgICAgICAvLyAgICAgICAgIH0sCiAgICAgICAgLy8gICAgICAgICBkYXRhOiBwcm9wcy5kYXRhLm1hcChpdGVtID0+IGl0ZW0ubmFtZSkKICAgICAgICAvLyAgICAgICB9CiAgICAgICAgLy8gICAgIF0sCiAgICAgICAgLy8gICAgIHNlcmllczogWwogICAgICAgIC8vICAgICAgIHsKICAgICAgICAvLyAgICAgICAgIHR5cGU6ICdiYXInLAogICAgICAgIC8vICAgICAgICAgZGF0YTogcHJvcHMuZGF0YS5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoewogICAgICAgIC8vICAgICAgICAgICB2YWx1ZTogaXRlbS52YWx1ZSwKICAgICAgICAvLyBpdGVtU3R5bGU6IHsKICAgICAgICAvLyAgIGNvbG9yOiBpbmRleCAlIDIgPT09IDAKICAgICAgICAvLyAgICAgPyB7CiAgICAgICAgLy8gICAgICAgdHlwZTogJ2xpbmVhcicsCiAgICAgICAgLy8gICAgICAgeDogMCwKICAgICAgICAvLyAgICAgICB5OiAwLAogICAgICAgIC8vICAgICAgIHgyOiAxLAogICAgICAgIC8vICAgICAgIHkyOiAwLAogICAgICAgIC8vICAgICAgIGNvbG9yU3RvcHM6IFsKICAgICAgICAvLyAgICAgICAgIHsgb2Zmc2V0OiAwLCBjb2xvcjogJyNGRkZGRkYnIH0sCiAgICAgICAgLy8gICAgICAgICB7IG9mZnNldDogMSwgY29sb3I6ICcjRUY4MTdDJyB9CiAgICAgICAgLy8gICAgICAgXQogICAgICAgIC8vICAgICB9CiAgICAgICAgLy8gICAgIDogewogICAgICAgIC8vICAgICAgIHR5cGU6ICdsaW5lYXInLAogICAgICAgIC8vICAgICAgIHg6IDAsCiAgICAgICAgLy8gICAgICAgeTogMCwKICAgICAgICAvLyAgICAgICB4MjogMSwKICAgICAgICAvLyAgICAgICB5MjogMCwKICAgICAgICAvLyAgICAgICBjb2xvclN0b3BzOiBbCiAgICAgICAgLy8gICAgICAgICB7IG9mZnNldDogMCwgY29sb3I6ICcjRkZGRkZGJyB9LAogICAgICAgIC8vICAgICAgICAgeyBvZmZzZXQ6IDEsIGNvbG9yOiAnIzU1OUZGRicgfQogICAgICAgIC8vICAgICAgIF0KICAgICAgICAvLyAgICAgfQogICAgICAgIC8vIH0KICAgICAgICAvLyAgICAgICAgIH0pKSwKICAgICAgICAvLyAgICAgICAgIGJhcldpZHRoOiAxMCwKICAgICAgICAvLyAgICAgICAgIGxhYmVsOiBbCiAgICAgICAgLy8gICAgICAgICAgIC8vIOWcqOafseeKtuWbvuS4iuaWueaYvuekuua0u+WKqOWQjeensAogICAgICAgIC8vICAgICAgICAgICB7CiAgICAgICAgLy8gICAgICAgICAgICAgc2hvdzogdHJ1ZSwKICAgICAgICAvLyAgICAgICAgICAgICBwb3NpdGlvbjogJ3RvcCcsCiAgICAgICAgLy8gICAgICAgICAgICAgY29sb3I6ICcjNjY2NjY2JywKICAgICAgICAvLyAgICAgICAgICAgICBmb250U2l6ZTogMTIsCiAgICAgICAgLy8gICAgICAgICAgICAgZm9udFdlaWdodDogJ25vcm1hbCcsCiAgICAgICAgLy8gICAgICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbiAocGFyYW1zKSB7CiAgICAgICAgLy8gICAgICAgICAgICAgICAvLyDnm7TmjqXku45Z6L205pWw5o2u5Lit6I635Y+W5ZCN56ewCiAgICAgICAgLy8gICAgICAgICAgICAgICByZXR1cm4gcHJvcHMuZGF0YVtwYXJhbXMuZGF0YUluZGV4XS5uYW1lIHx8ICcnCiAgICAgICAgLy8gICAgICAgICAgICAgfSwKICAgICAgICAvLyAgICAgICAgICAgICBvZmZzZXQ6IFswLCAtOF0KICAgICAgICAvLyAgICAgICAgICAgfSwKICAgICAgICAvLyAgICAgICAgICAgLy8g5Zyo5p+x54q25Zu+5Y+z5L6n5pi+56S65pWw5YC8CiAgICAgICAgLy8gICAgICAgICAgIHsKICAgICAgICAvLyAgICAgICAgICAgICBzaG93OiB0cnVlLAogICAgICAgIC8vICAgICAgICAgICAgIHBvc2l0aW9uOiAncmlnaHQnLAogICAgICAgIC8vICAgICAgICAgICAgIGNvbG9yOiAnIzY2NjY2NicsCiAgICAgICAgLy8gICAgICAgICAgICAgZm9udFNpemU6IDEyLAogICAgICAgIC8vICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdub3JtYWwnLAogICAgICAgIC8vICAgICAgICAgICAgIGZvcm1hdHRlcjogJ3tjfScsCiAgICAgICAgLy8gICAgICAgICAgICAgb2Zmc2V0OiBbOCwgMF0KICAgICAgICAvLyAgICAgICAgICAgfQogICAgICAgIC8vICAgICAgICAgXQogICAgICAgIC8vICAgICAgIH0KICAgICAgICAvLyAgICAgXQogICAgICAgIC8vICAgfQogICAgICAgIGNoYXJ0SW5zdGFuY2Uuc2V0T3B0aW9uKG9wdGlvbik7CiAgICAgIH0pOwogICAgfTsKICAgIGNvbnN0IHJlc2l6ZUNoYXJ0ID0gKCkgPT4gewogICAgICBpZiAoY2hhcnRJbnN0YW5jZSkgewogICAgICAgIGNoYXJ0SW5zdGFuY2UucmVzaXplKCk7CiAgICAgIH0KICAgIH07CiAgICBvbk1vdW50ZWQoKCkgPT4gewogICAgICBpbml0Q2hhcnQoKTsKICAgICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHJlc2l6ZUNoYXJ0KTsKICAgIH0pOwogICAgb25Vbm1vdW50ZWQoKCkgPT4gewogICAgICBpZiAoY2hhcnRJbnN0YW5jZSkgewogICAgICAgIGNoYXJ0SW5zdGFuY2UuZGlzcG9zZSgpOwogICAgICB9CiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNpemUnLCByZXNpemVDaGFydCk7CiAgICB9KTsKICAgIHdhdGNoKCgpID0+IHByb3BzLmRhdGEsICgpID0+IHsKICAgICAgaWYgKGNoYXJ0SW5zdGFuY2UpIHsKICAgICAgICBpbml0Q2hhcnQoKTsKICAgICAgfQogICAgfSwgewogICAgICBkZWVwOiB0cnVlCiAgICB9KTsKICAgIHJldHVybiB7CiAgICAgIGNoYXJ0SWQKICAgIH07CiAgfQp9Ow=="}, {"version": 3, "names": ["ref", "onMounted", "onUnmounted", "watch", "nextTick", "echarts", "name", "props", "id", "type", "String", "required", "data", "Array", "default", "setup", "chartId", "chartInstance", "initChart", "charts", "cityList", "map", "v", "cityData", "value", "top10CityList", "top10CityData", "lineY", "lineT", "i", "length", "x", "barGap", "itemStyle", "color", "y", "x2", "y2", "colorStops", "offset", "data1", "label", "show", "position", "fontSize", "distance", "push", "console", "log", "dom", "document", "getElementById", "error", "init", "option", "tooltip", "trigger", "formatter", "p", "seriesName", "grid", "left", "right", "top", "bottom", "containLabel", "yAxis", "inverse", "axisTick", "axisLine", "axisLabel", "inside", "xAxis", "splitLine", "series", "<PERSON><PERSON><PERSON><PERSON>", "legendHoverLink", "normal", "setOption", "resizeChart", "resize", "window", "addEventListener", "dispose", "removeEventListener", "deep"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\echartsComponent\\ActivityTypeChart.vue"], "sourcesContent": ["<template>\n  <div class=\"activity-type-chart\" :id=\"chartId\"></div>\n</template>\n\n<script>\nimport { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'ActivityTypeChart',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    data: {\n      type: Array,\n      default: () => []\n    }\n  },\n  setup (props) {\n    const chartId = ref(props.id)\n    let chartInstance = null\n\n    const initChart = () => {\n      var charts = {\n        cityList: props.data.map(v => v.name),\n        cityData: props.data.map(v => v.value)\n      }\n      var top10CityList = charts.cityList\n      var top10CityData = charts.cityData\n      const lineY = []\n      const lineT = []\n      for (var i = 0; i < charts.cityList.length; i++) {\n        var x = i\n        if (x > 1) {\n          x = 2\n        }\n        var data = {\n          name: charts.cityList[i],\n          value: top10CityData[i],\n          barGap: '-100%',\n          itemStyle: {\n            color: {\n              type: 'linear',\n              x: 0,\n              y: 0,\n              x2: 1,\n              y2: 0,\n              colorStops: [\n                { offset: 0, color: '#FFFFFF' },\n                { offset: 1, color: '#559FFF' }\n              ]\n            }\n          }\n        }\n        var data1 = {\n          value: top10CityData[i],\n          label: {\n            show: true,\n            position: 'right',\n            color: '#999',\n            fontSize: 14,\n            distance: 10\n          },\n          itemStyle: {\n            color: {\n              type: 'linear',\n              x: 0,\n              y: 0,\n              x2: 1,\n              y2: 0,\n              colorStops: [\n                { offset: 0, color: '#FFFFFF' },\n                { offset: 1, color: '#EF817C' }\n              ]\n            }\n          }\n        }\n        lineY.push(data)\n        lineT.push(data1)\n        console.log('lineT===>', lineT)\n        console.log('lineT===>', lineT)\n      }\n      nextTick(() => {\n        const dom = document.getElementById(chartId.value)\n        if (!dom) {\n          console.error('Chart DOM element not found:', chartId.value)\n          return\n        }\n        if (!chartInstance) {\n          chartInstance = echarts.init(dom)\n        }\n        const option = {\n          tooltip: {\n            trigger: 'item',\n            formatter: (p) => {\n              if (p.seriesName === 'total') {\n                return ''\n              }\n              return `${p.name}<br/>${p.value}`\n            }\n          },\n          grid: {\n            left: '0%',\n            right: '20%',\n            top: '2%',\n            bottom: '2%',\n            containLabel: true\n          },\n          yAxis: {\n            type: 'category',\n            inverse: true,\n            axisTick: {\n              show: false\n            },\n            axisLine: {\n              show: false\n            },\n            axisLabel: {\n              show: false,\n              inside: false\n            },\n            data: top10CityList\n          },\n          xAxis: {\n            type: 'value',\n            axisTick: {\n              show: false\n            },\n            axisLine: {\n              show: false\n            },\n            splitLine: {\n              show: false\n            },\n            axisLabel: {\n              show: false\n            }\n          },\n          series: [\n            {\n              name: 'total',\n              type: 'bar',\n              barGap: '-100%',\n              barWidth: 10,\n              data: lineT,\n              legendHoverLink: false\n            },\n            {\n              name: 'bar',\n              type: 'bar',\n              barWidth: 10,\n              data: lineY,\n              label: {\n                normal: {\n                  show: true,\n                  fontSize: '12px',\n                  color: '#999',\n                  position: [0, '-20px'],\n                  formatter: '{b}'\n                }\n              }\n            }\n          ]\n        }\n        //   const option = {\n\n        //     xAxis: {\n        //       type: 'value',\n        //       axisTick: {\n        //         show: false\n        //       },\n        //       axisLine: {\n        //         show: false\n        //       },\n        //       splitLine: {\n        //         show: false\n        //       },\n        //       axisLabel: {\n        //         show: false\n        //       }\n        //     },\n        //     yAxis: [\n        //       {\n        //         type: 'category',\n        //         inverse: true,\n        //         axisTick: {\n        //           show: false\n        //         },\n        //         axisLine: {\n        //           show: false\n        //         },\n        //         axisLabel: {\n        //           show: false // 隐藏Y轴标签，名称将显示在柱状图上方\n        //         },\n        //         data: props.data.map(item => item.name)\n        //       }\n        //     ],\n        //     series: [\n        //       {\n        //         type: 'bar',\n        //         data: props.data.map((item, index) => ({\n        //           value: item.value,\n        // itemStyle: {\n        //   color: index % 2 === 0\n        //     ? {\n        //       type: 'linear',\n        //       x: 0,\n        //       y: 0,\n        //       x2: 1,\n        //       y2: 0,\n        //       colorStops: [\n        //         { offset: 0, color: '#FFFFFF' },\n        //         { offset: 1, color: '#EF817C' }\n        //       ]\n        //     }\n        //     : {\n        //       type: 'linear',\n        //       x: 0,\n        //       y: 0,\n        //       x2: 1,\n        //       y2: 0,\n        //       colorStops: [\n        //         { offset: 0, color: '#FFFFFF' },\n        //         { offset: 1, color: '#559FFF' }\n        //       ]\n        //     }\n        // }\n        //         })),\n        //         barWidth: 10,\n        //         label: [\n        //           // 在柱状图上方显示活动名称\n        //           {\n        //             show: true,\n        //             position: 'top',\n        //             color: '#666666',\n        //             fontSize: 12,\n        //             fontWeight: 'normal',\n        //             formatter: function (params) {\n        //               // 直接从Y轴数据中获取名称\n        //               return props.data[params.dataIndex].name || ''\n        //             },\n        //             offset: [0, -8]\n        //           },\n        //           // 在柱状图右侧显示数值\n        //           {\n        //             show: true,\n        //             position: 'right',\n        //             color: '#666666',\n        //             fontSize: 12,\n        //             fontWeight: 'normal',\n        //             formatter: '{c}',\n        //             offset: [8, 0]\n        //           }\n        //         ]\n        //       }\n        //     ]\n        //   }\n        chartInstance.setOption(option)\n      })\n    }\n\n    const resizeChart = () => {\n      if (chartInstance) {\n        chartInstance.resize()\n      }\n    }\n\n    onMounted(() => {\n      initChart()\n      window.addEventListener('resize', resizeChart)\n    })\n\n    onUnmounted(() => {\n      if (chartInstance) {\n        chartInstance.dispose()\n      }\n      window.removeEventListener('resize', resizeChart)\n    })\n\n    watch(() => props.data, () => {\n      if (chartInstance) {\n        initChart()\n      }\n    }, { deep: true })\n\n    return {\n      chartId\n    }\n  }\n}\n</script>\n\n<style lang=\"less\" scoped>\n.activity-type-chart {\n  width: 100%;\n  height: 100%;\n}\n</style>\n"], "mappings": "AAKA,SAASA,GAAG,EAAEC,SAAS,EAAEC,WAAW,EAAEC,KAAK,EAAEC,QAAO,QAAS,KAAI;AACjE,OAAO,KAAKC,OAAM,MAAO,SAAQ;AAEjC,eAAe;EACbC,IAAI,EAAE,mBAAmB;EACzBC,KAAK,EAAE;IACLC,EAAE,EAAE;MACFC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,IAAI,EAAE;MACJH,IAAI,EAAEI,KAAK;MACXC,OAAO,EAAEA,CAAA,KAAM;IACjB;EACF,CAAC;EACDC,KAAIA,CAAGR,KAAK,EAAE;IACZ,MAAMS,OAAM,GAAIhB,GAAG,CAACO,KAAK,CAACC,EAAE;IAC5B,IAAIS,aAAY,GAAI,IAAG;IAEvB,MAAMC,SAAQ,GAAIA,CAAA,KAAM;MACtB,IAAIC,MAAK,GAAI;QACXC,QAAQ,EAAEb,KAAK,CAACK,IAAI,CAACS,GAAG,CAACC,CAAA,IAAKA,CAAC,CAAChB,IAAI,CAAC;QACrCiB,QAAQ,EAAEhB,KAAK,CAACK,IAAI,CAACS,GAAG,CAACC,CAAA,IAAKA,CAAC,CAACE,KAAK;MACvC;MACA,IAAIC,aAAY,GAAIN,MAAM,CAACC,QAAO;MAClC,IAAIM,aAAY,GAAIP,MAAM,CAACI,QAAO;MAClC,MAAMI,KAAI,GAAI,EAAC;MACf,MAAMC,KAAI,GAAI,EAAC;MACf,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIV,MAAM,CAACC,QAAQ,CAACU,MAAM,EAAED,CAAC,EAAE,EAAE;QAC/C,IAAIE,CAAA,GAAIF,CAAA;QACR,IAAIE,CAAA,GAAI,CAAC,EAAE;UACTA,CAAA,GAAI;QACN;QACA,IAAInB,IAAG,GAAI;UACTN,IAAI,EAAEa,MAAM,CAACC,QAAQ,CAACS,CAAC,CAAC;UACxBL,KAAK,EAAEE,aAAa,CAACG,CAAC,CAAC;UACvBG,MAAM,EAAE,OAAO;UACfC,SAAS,EAAE;YACTC,KAAK,EAAE;cACLzB,IAAI,EAAE,QAAQ;cACdsB,CAAC,EAAE,CAAC;cACJI,CAAC,EAAE,CAAC;cACJC,EAAE,EAAE,CAAC;cACLC,EAAE,EAAE,CAAC;cACLC,UAAU,EAAE,CACV;gBAAEC,MAAM,EAAE,CAAC;gBAAEL,KAAK,EAAE;cAAU,CAAC,EAC/B;gBAAEK,MAAM,EAAE,CAAC;gBAAEL,KAAK,EAAE;cAAU;YAElC;UACF;QACF;QACA,IAAIM,KAAI,GAAI;UACVhB,KAAK,EAAEE,aAAa,CAACG,CAAC,CAAC;UACvBY,KAAK,EAAE;YACLC,IAAI,EAAE,IAAI;YACVC,QAAQ,EAAE,OAAO;YACjBT,KAAK,EAAE,MAAM;YACbU,QAAQ,EAAE,EAAE;YACZC,QAAQ,EAAE;UACZ,CAAC;UACDZ,SAAS,EAAE;YACTC,KAAK,EAAE;cACLzB,IAAI,EAAE,QAAQ;cACdsB,CAAC,EAAE,CAAC;cACJI,CAAC,EAAE,CAAC;cACJC,EAAE,EAAE,CAAC;cACLC,EAAE,EAAE,CAAC;cACLC,UAAU,EAAE,CACV;gBAAEC,MAAM,EAAE,CAAC;gBAAEL,KAAK,EAAE;cAAU,CAAC,EAC/B;gBAAEK,MAAM,EAAE,CAAC;gBAAEL,KAAK,EAAE;cAAU;YAElC;UACF;QACF;QACAP,KAAK,CAACmB,IAAI,CAAClC,IAAI;QACfgB,KAAK,CAACkB,IAAI,CAACN,KAAK;QAChBO,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEpB,KAAK;QAC9BmB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEpB,KAAK;MAChC;MACAxB,QAAQ,CAAC,MAAM;QACb,MAAM6C,GAAE,GAAIC,QAAQ,CAACC,cAAc,CAACnC,OAAO,CAACQ,KAAK;QACjD,IAAI,CAACyB,GAAG,EAAE;UACRF,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAEpC,OAAO,CAACQ,KAAK;UAC3D;QACF;QACA,IAAI,CAACP,aAAa,EAAE;UAClBA,aAAY,GAAIZ,OAAO,CAACgD,IAAI,CAACJ,GAAG;QAClC;QACA,MAAMK,MAAK,GAAI;UACbC,OAAO,EAAE;YACPC,OAAO,EAAE,MAAM;YACfC,SAAS,EAAGC,CAAC,IAAK;cAChB,IAAIA,CAAC,CAACC,UAAS,KAAM,OAAO,EAAE;gBAC5B,OAAO,EAAC;cACV;cACA,OAAO,GAAGD,CAAC,CAACpD,IAAI,QAAQoD,CAAC,CAAClC,KAAK,EAAC;YAClC;UACF,CAAC;UACDoC,IAAI,EAAE;YACJC,IAAI,EAAE,IAAI;YACVC,KAAK,EAAE,KAAK;YACZC,GAAG,EAAE,IAAI;YACTC,MAAM,EAAE,IAAI;YACZC,YAAY,EAAE;UAChB,CAAC;UACDC,KAAK,EAAE;YACLzD,IAAI,EAAE,UAAU;YAChB0D,OAAO,EAAE,IAAI;YACbC,QAAQ,EAAE;cACR1B,IAAI,EAAE;YACR,CAAC;YACD2B,QAAQ,EAAE;cACR3B,IAAI,EAAE;YACR,CAAC;YACD4B,SAAS,EAAE;cACT5B,IAAI,EAAE,KAAK;cACX6B,MAAM,EAAE;YACV,CAAC;YACD3D,IAAI,EAAEa;UACR,CAAC;UACD+C,KAAK,EAAE;YACL/D,IAAI,EAAE,OAAO;YACb2D,QAAQ,EAAE;cACR1B,IAAI,EAAE;YACR,CAAC;YACD2B,QAAQ,EAAE;cACR3B,IAAI,EAAE;YACR,CAAC;YACD+B,SAAS,EAAE;cACT/B,IAAI,EAAE;YACR,CAAC;YACD4B,SAAS,EAAE;cACT5B,IAAI,EAAE;YACR;UACF,CAAC;UACDgC,MAAM,EAAE,CACN;YACEpE,IAAI,EAAE,OAAO;YACbG,IAAI,EAAE,KAAK;YACXuB,MAAM,EAAE,OAAO;YACf2C,QAAQ,EAAE,EAAE;YACZ/D,IAAI,EAAEgB,KAAK;YACXgD,eAAe,EAAE;UACnB,CAAC,EACD;YACEtE,IAAI,EAAE,KAAK;YACXG,IAAI,EAAE,KAAK;YACXkE,QAAQ,EAAE,EAAE;YACZ/D,IAAI,EAAEe,KAAK;YACXc,KAAK,EAAE;cACLoC,MAAM,EAAE;gBACNnC,IAAI,EAAE,IAAI;gBACVE,QAAQ,EAAE,MAAM;gBAChBV,KAAK,EAAE,MAAM;gBACbS,QAAQ,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC;gBACtBc,SAAS,EAAE;cACb;YACF;UACF;QAEJ;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAxC,aAAa,CAAC6D,SAAS,CAACxB,MAAM;MAChC,CAAC;IACH;IAEA,MAAMyB,WAAU,GAAIA,CAAA,KAAM;MACxB,IAAI9D,aAAa,EAAE;QACjBA,aAAa,CAAC+D,MAAM,CAAC;MACvB;IACF;IAEA/E,SAAS,CAAC,MAAM;MACdiB,SAAS,CAAC;MACV+D,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEH,WAAW;IAC/C,CAAC;IAED7E,WAAW,CAAC,MAAM;MAChB,IAAIe,aAAa,EAAE;QACjBA,aAAa,CAACkE,OAAO,CAAC;MACxB;MACAF,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEL,WAAW;IAClD,CAAC;IAED5E,KAAK,CAAC,MAAMI,KAAK,CAACK,IAAI,EAAE,MAAM;MAC5B,IAAIK,aAAa,EAAE;QACjBC,SAAS,CAAC;MACZ;IACF,CAAC,EAAE;MAAEmE,IAAI,EAAE;IAAK,CAAC;IAEjB,OAAO;MACLrE;IACF;EACF;AACF", "ignoreList": []}]}