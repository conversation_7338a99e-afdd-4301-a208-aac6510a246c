{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\PublicOpinion.vue?vue&type=template&id=e9c37430&scoped=true", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\PublicOpinion.vue", "mtime": 1753943363546}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\PublicOpinion.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAChB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5G,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACzE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC5G,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1D,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC/G,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1F,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC1F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,CAAC,CAAC,CAAC,CAAC;UACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAClC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;cACxC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;MAC1B,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/zy/xm/h5/qdzx_h5/product-app/src/views/SmartBrainLargeScreen/component/PublicOpinion.vue", "sourceRoot": "", "sourcesContent": ["<template>\r\n  <div class=\"PublicOpinion\">\r\n    <!-- 社情民意整体情况 -->\r\n    <div class=\"overall_situation_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">社情民意整体情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"overall_situation_list\">\r\n        <PublicOpinionOverallSituationChart id=\"publicOpinionOverall\" :total-count=\"totalCount\"\r\n          :adopted-count=\"adoptedCount\" />\r\n      </div>\r\n    </div>\r\n    <!-- 采用情况 -->\r\n    <div class=\"adopt_situation_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">采用情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"adopt_situation_list\">\r\n        <ProgressBarChart title=\"委员提交\" desc=\"占总件数60%\" :percent=\"committeeMember.submitPercent\"\r\n          :value=\"committeeMember.submitNum\" color=\"linear-gradient(90deg, rgba(58,147,255,0.3) 0%, #3A93FF 100%)\" />\r\n        <ProgressBarChart title=\"采用情况\" desc=\"占提交数42%\" :percent=\"committeeMember.adoptSituationPercent\"\r\n          :value=\"committeeMember.adoptSituationNum\"\r\n          color=\"linear-gradient(90deg, rgba(255,115,141,0.3) 0%, #FF738D 100%)\" />\r\n        <ProgressBarChart title=\"单位提交\" desc=\"占总件数60%\" :percent=\"unit.submitPercent\" :value=\"unit.submitNum\"\r\n          color=\"linear-gradient(90deg, rgba(58,147,255,0.3) 0%, #3A93FF 100%)\" />\r\n        <ProgressBarChart title=\"采用情况\" desc=\"占提交数42%\" :percent=\"unit.adoptSituationPercent\"\r\n          :value=\"unit.adoptSituationNum\" color=\"linear-gradient(90deg, rgba(255,115,141,0.3) 0%, #FF738D 100%)\" />\r\n      </div>\r\n      <div class=\"adopt_situation_distribution_text\">采用情况分布</div>\r\n      <div class=\"adopt_situation_distribution_charts\">\r\n        <PieChart id=\"adoptSituationDistribution\" :chart-data=\"adoptSituationDistribution\" :radius=\"['35%', '60%']\" />\r\n      </div>\r\n    </div>\r\n    <!-- 批示情况 -->\r\n    <div class=\"instructions_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">批示情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"instructions_list\">\r\n        <div class=\"instructions_item\" v-for=\"(item, index) in instructions\" :key=\"index\"\r\n          :style=\"`background: ${item.bg}`\">\r\n          <div class=\"instructions_item_value\" :style=\"`color: ${item.color}`\">{{ item.value }}</div>\r\n          <div class=\"instructions_item_label\">{{ item.label }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 各单位上报与采用情况 -->\r\n    <div class=\"report_adopt_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">各单位上报与采用情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"report_adopt_list\">\r\n        <DoubleBarChart id=\"party_double_line\" :data=\"partyData\" :legend=\"['报送篇数', '采用篇数']\" :color=\"[\r\n          ['#56A0FF', 'rgba(86,160,255,0.1)'],\r\n          ['#FF738D', 'rgba(255,115,141,0.1)']\r\n        ]\" title=\"八大党派及民主工商联\" style=\"height: 260px;\" />\r\n        <DoubleBarChart id=\"district_double_line\" :data=\"districtData\" :legend=\"['报送篇数', '采用篇数']\" :color=\"[\r\n          ['#56A0FF', 'rgba(86,160,255,0.1)'],\r\n          ['#FF738D', 'rgba(255,115,141,0.1)']\r\n        ]\" title=\"各区市\" style=\"height: 260px;\" />\r\n        <DoubleBarChart id=\"office_double_line\" :data=\"officeData\" :legend=\"['报送篇数', '采用篇数']\" :color=\"[\r\n          ['#56A0FF', 'rgba(86,160,255,0.1)'],\r\n          ['#FF738D', 'rgba(255,115,141,0.1)']\r\n        ]\" title=\"各单位办公室\" style=\"height: 260px;\" />\r\n      </div>\r\n    </div>\r\n    <!-- 个人报送与采用情况 -->\r\n    <div class=\"submit_adopt_situation_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">个人报送与采用情况</span>\r\n        </div>\r\n        <div class=\"header_right\" @click=\"openMore('notice')\">\r\n          <span class=\"header_right_more\">查看全部</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"submit_adopt_situation_list\">\r\n        <div class=\"submit_adopt_table\">\r\n          <div class=\"submit_adopt_table_header\">\r\n            <span class=\"party-header-column\">姓名</span>\r\n            <span class=\"count-header-column\">报送件数</span>\r\n            <span class=\"count-header-column\">采用件数</span>\r\n          </div>\r\n          <div class=\"submit_adopt_table_row\" v-for=\"(item, idx) in submitAdoptSituationData\" :key=\"item.name\"\r\n            :class=\"{ 'row-alt': idx % 2 === 1 }\">\r\n            <span class=\"party-column name-cell\">\r\n              <img :src=\"item.avatar\" class=\"avatar\" />\r\n              <span class=\"name\">{{ item.name }}</span>\r\n            </span>\r\n            <span class=\"count-column\">{{ item.submit }}</span>\r\n            <span class=\"count-column\">{{ item.adopt }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 类别分析 -->\r\n    <div class=\"category_analysis_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">类别分析</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"category_analysis_list\">\r\n        <barChart id=\"categoryAnalysis\" :data=\"categoryData\" :color=\"['#559FFF', 'rgba(85,159,255,0.3)']\"\r\n          style=\"height:200px\" />\r\n      </div>\r\n    </div>\r\n    <!-- 热词分析 -->\r\n    <div class=\"hot_words_analysis_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">热词分析</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"hot_words_analysis_list\">\r\n        <wordCloudEcharts id=\"wordcloud\" :wordList=\"wordCloudData\"\r\n          :colorList=\"['#1890FF', '#FF6B35', '#52C41A', '#722ED1', '#1890FF', '#FF69B4', '#52C41A', '#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']\"\r\n          :sizeRange=\"[2, 10]\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { toRefs, reactive } from 'vue'\r\nimport PublicOpinionOverallSituationChart from './echartsComponent/PublicOpinionOverallSituationChart.vue'\r\nimport ProgressBarChart from './echartsComponent/ProgressBarChart.vue'\r\nimport PieChart from './echartsComponent/PieChart.vue'\r\nimport DoubleBarChart from './echartsComponent/DoubleBarChart.vue'\r\nimport barChart from './echartsComponent/barChart.vue'\r\nimport wordCloudEcharts from './echartsComponent/wordCloudEcharts.vue'\r\nexport default {\r\n  name: 'NetworkPolitics',\r\n  components: { PublicOpinionOverallSituationChart, ProgressBarChart, PieChart, DoubleBarChart, barChart, wordCloudEcharts },\r\n  setup () {\r\n    const data = reactive({\r\n      totalCount: 50,\r\n      adoptedCount: 20,\r\n      committeeMember: { submitPercent: 60, submitNum: 345, adoptSituationPercent: 42, adoptSituationNum: 102 },\r\n      unit: { submitPercent: 60, submitNum: 345, adoptSituationPercent: 42, adoptSituationNum: 102 },\r\n      adoptSituationDistribution: [\r\n        { name: '市政协采用', value: 135, percentage: '40%', color: '#4A90E2' },\r\n        { name: '省政协采用', value: 131, percentage: '30%', color: '#4CD9C0' },\r\n        { name: '全国政协采用', value: 126, percentage: '20%', color: '#F56A6A' }\r\n      ],\r\n      instructions: [\r\n        { label: '市政协批示', value: '135', bg: '#EFF6FF', color: '#3B91FB' },\r\n        { label: '省政协批示', value: '131', bg: '#FDF8F0', color: '#EAB308' },\r\n        { label: '全国政协批示', value: '126', bg: '#F0FDF4', color: '#43DDBB' }\r\n      ],\r\n      partyData: [\r\n        { name: '民革', value1: 102, value2: 80 },\r\n        { name: '民盟', value1: 56, value2: 30 },\r\n        { name: '民建', value1: 120, value2: 75 },\r\n        { name: '民进', value1: 34, value2: 20 },\r\n        { name: '农工', value1: 89, value2: 60 },\r\n        { name: '致公', value1: 95, value2: 70 },\r\n        { name: '九三', value1: 80, value2: 55 },\r\n        { name: '工商联', value1: 45, value2: 25 }\r\n      ],\r\n      districtData: [\r\n        { name: '市南', value1: 55, value2: 13 },\r\n        { name: '市北', value1: 20, value2: 6 },\r\n        { name: '李沧', value1: 35, value2: 10 },\r\n        { name: '崂山', value1: 18, value2: 4 },\r\n        { name: '黄岛', value1: 52, value2: 12 },\r\n        { name: '城阳', value1: 10, value2: 2 },\r\n        { name: '即墨', value1: 25, value2: 5 },\r\n        { name: '胶州', value1: 30, value2: 7 },\r\n        { name: '平度', value1: 12, value2: 3 },\r\n        { name: '莱西', value1: 8, value2: 1 }\r\n      ],\r\n      officeData: [\r\n        { name: '委员', value1: 8, value2: 2 },\r\n        { name: '提案', value1: 2, value2: 1 },\r\n        { name: '经济', value1: 5, value2: 1 },\r\n        { name: '农业', value1: 18, value2: 6 },\r\n        { name: '人口环', value1: 50, value2: 15 },\r\n        { name: '教科', value1: 3, value2: 1 },\r\n        { name: '社法', value1: 2, value2: 1 },\r\n        { name: '港澳', value1: 1, value2: 0 },\r\n        { name: '文史', value1: 2, value2: 1 },\r\n        { name: '民宗', value1: 1, value2: 0 },\r\n        { name: '财经', value1: 50, value2: 15 }\r\n      ],\r\n      submitAdoptSituationData: [\r\n        { avatar: require('@/assets/img/largeScreen/man.png'), name: '张伟', submit: 12, adopt: 3 },\r\n        { avatar: require('@/assets/img/largeScreen/woman.png'), name: '刘芳', submit: 11, adopt: 2 },\r\n        { avatar: require('@/assets/img/largeScreen/man.png'), name: '陈明', submit: 11, adopt: 2 },\r\n        { avatar: require('@/assets/img/largeScreen/man.png'), name: '林小华', submit: 11, adopt: 2 },\r\n        { avatar: require('@/assets/img/largeScreen/man.png'), name: '赵天宇', submit: 10, adopt: 1 },\r\n        { avatar: require('@/assets/img/largeScreen/woman.png'), name: '吴静怡', submit: 10, adopt: 1 },\r\n        { avatar: require('@/assets/img/largeScreen/man.png'), name: '黄浩然', submit: 8, adopt: 1 },\r\n        { avatar: require('@/assets/img/largeScreen/woman.png'), name: '周梦琪', submit: 7, adopt: 1 }\r\n      ],\r\n      categoryData: [\r\n        { name: '社会', value: 115 },\r\n        { name: '政治', value: 140 },\r\n        { name: '经济', value: 60 },\r\n        { name: '文化', value: 115 },\r\n        { name: '生态文明', value: 125 }\r\n      ],\r\n      wordCloudData: [\r\n        { name: '乡村振兴', value: 180 },\r\n        { name: '就业优先', value: 165 },\r\n        { name: '科技创新', value: 150 },\r\n        { name: '改革开放', value: 135 },\r\n        { name: '依法治国', value: 120 },\r\n        { name: '教育人才', value: 105 },\r\n        { name: '社会保障', value: 90 },\r\n        { name: '热词', value: 75 },\r\n        { name: '绿色发展', value: 60 },\r\n        { name: '数字中国', value: 45 },\r\n        { name: '共同富裕', value: 40 },\r\n        { name: '文化自信', value: 35 },\r\n        { name: '国家安全', value: 30 },\r\n        { name: '人民至上', value: 25 },\r\n        { name: '中国式现代化', value: 20 }\r\n      ]\r\n    })\r\n\r\n    return { ...toRefs(data) }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.PublicOpinion {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .header_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 15px 0 15px;\r\n\r\n    .header_left {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_left_line {\r\n        width: 3px;\r\n        height: 14px;\r\n        background: #007AFF;\r\n      }\r\n\r\n      .header_left_title {\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #222222;\r\n        margin-left: 8px;\r\n        font-family: Source Han Serif SC, Source Han Serif SC;\r\n      }\r\n    }\r\n\r\n    .header_right {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_right_text {\r\n        font-weight: 400;\r\n        font-size: 12px;\r\n        color: #999999;\r\n      }\r\n\r\n      .header_right_more {\r\n        font-size: 14px;\r\n        color: #0271E3;\r\n        border-radius: 14px;\r\n        border: 1px solid #0271E3;\r\n        padding: 3px 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .overall_situation_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .overall_situation_list {\r\n      height: 160px;\r\n    }\r\n  }\r\n\r\n  .adopt_situation_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .adopt_situation_list {\r\n      padding: 5px 18px 18px 18px;\r\n    }\r\n\r\n    .adopt_situation_distribution_text {\r\n      font-size: 15px;\r\n      color: #222222;\r\n      font-family: Source Han Serif SC, Source Han Serif SC;\r\n      padding-left: 18px;\r\n    }\r\n\r\n    .adopt_situation_distribution_charts {\r\n      width: 100%;\r\n      height: 260px;\r\n      margin-top: 10px;\r\n    }\r\n  }\r\n\r\n  .instructions_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .instructions_list {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 18px;\r\n\r\n      .instructions_item {\r\n        width: 93px;\r\n        height: 90px;\r\n        border-radius: 4px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        .instructions_item_value {\r\n          font-size: 19px;\r\n        }\r\n\r\n        .instructions_item_label {\r\n          font-size: 13px;\r\n          color: #666666;\r\n          margin-top: 14px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .report_adopt_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n  }\r\n\r\n  .submit_adopt_situation_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .submit_adopt_situation_list {\r\n      margin-top: 15px;\r\n\r\n      .submit_adopt_table {\r\n        width: 100%;\r\n        background: #fff;\r\n\r\n        .submit_adopt_table_header,\r\n        .submit_adopt_table_row {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 12px 15px;\r\n          border-bottom: 1px solid #f0f0f0;\r\n\r\n          &:last-child {\r\n            border-bottom: none;\r\n          }\r\n\r\n          .party-header-column {\r\n            flex: 2;\r\n            text-align: left;\r\n            font-size: 14px;\r\n            color: #999;\r\n          }\r\n\r\n          .count-header-column {\r\n            flex: 1;\r\n            text-align: center;\r\n            font-size: 14px;\r\n            color: #999;\r\n          }\r\n\r\n          .party-column {\r\n            flex: 2;\r\n            text-align: left;\r\n            font-size: 14px;\r\n            color: #333;\r\n          }\r\n\r\n          .count-column {\r\n            flex: 1;\r\n            text-align: center;\r\n            font-size: 14px;\r\n            color: #333;\r\n          }\r\n\r\n          .name-cell {\r\n            display: flex;\r\n            align-items: center;\r\n\r\n            .avatar {\r\n              width: 28px;\r\n              height: 28px;\r\n              border-radius: 50%;\r\n              margin-right: 8px;\r\n              object-fit: cover;\r\n              border: 1px solid #e0e0e0;\r\n            }\r\n\r\n            .name {\r\n              font-size: 14px;\r\n              color: #333;\r\n            }\r\n          }\r\n        }\r\n\r\n        .submit_adopt_table_header {\r\n          background: #F1F8FF;\r\n          font-weight: 600;\r\n          color: #222;\r\n          font-size: 14px;\r\n        }\r\n\r\n        .submit_adopt_table_row {\r\n          background: #fff;\r\n          color: #333;\r\n          font-size: 14px;\r\n\r\n          &.row-alt {\r\n            background: #F1F8FF;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .category_analysis_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .category_analysis_list {\r\n      padding: 18px;\r\n    }\r\n  }\r\n\r\n  .hot_words_analysis_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .hot_words_analysis_list {}\r\n  }\r\n}\r\n</style>\r\n"]}]}