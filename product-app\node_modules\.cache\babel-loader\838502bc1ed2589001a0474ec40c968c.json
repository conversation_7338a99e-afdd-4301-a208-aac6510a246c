{"remainingRequest": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\PublicOpinion.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\PublicOpinion.vue", "mtime": 1753943363546}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\babel.config.js", "mtime": 1731635626828}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["toRefs", "reactive", "PublicOpinionOverallSituationChart", "ProgressBarChart", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "wordCloudEcharts", "name", "components", "setup", "data", "totalCount", "adoptedCount", "committeeMember", "submitPercent", "submitNum", "adoptSituationPercent", "adoptSituationNum", "unit", "adoptSituationDistribution", "value", "percentage", "color", "instructions", "label", "bg", "partyData", "value1", "value2", "districtData", "officeData", "submitAdoptSituationData", "avatar", "require", "submit", "adopt", "categoryData", "wordCloudData"], "sources": ["D:\\zy\\xm\\h5\\qdzx_h5\\product-app\\src\\views\\SmartBrainLargeScreen\\component\\PublicOpinion.vue"], "sourcesContent": ["<template>\r\n  <div class=\"PublicOpinion\">\r\n    <!-- 社情民意整体情况 -->\r\n    <div class=\"overall_situation_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">社情民意整体情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"overall_situation_list\">\r\n        <PublicOpinionOverallSituationChart id=\"publicOpinionOverall\" :total-count=\"totalCount\"\r\n          :adopted-count=\"adoptedCount\" />\r\n      </div>\r\n    </div>\r\n    <!-- 采用情况 -->\r\n    <div class=\"adopt_situation_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">采用情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"adopt_situation_list\">\r\n        <ProgressBarChart title=\"委员提交\" desc=\"占总件数60%\" :percent=\"committeeMember.submitPercent\"\r\n          :value=\"committeeMember.submitNum\" color=\"linear-gradient(90deg, rgba(58,147,255,0.3) 0%, #3A93FF 100%)\" />\r\n        <ProgressBarChart title=\"采用情况\" desc=\"占提交数42%\" :percent=\"committeeMember.adoptSituationPercent\"\r\n          :value=\"committeeMember.adoptSituationNum\"\r\n          color=\"linear-gradient(90deg, rgba(255,115,141,0.3) 0%, #FF738D 100%)\" />\r\n        <ProgressBarChart title=\"单位提交\" desc=\"占总件数60%\" :percent=\"unit.submitPercent\" :value=\"unit.submitNum\"\r\n          color=\"linear-gradient(90deg, rgba(58,147,255,0.3) 0%, #3A93FF 100%)\" />\r\n        <ProgressBarChart title=\"采用情况\" desc=\"占提交数42%\" :percent=\"unit.adoptSituationPercent\"\r\n          :value=\"unit.adoptSituationNum\" color=\"linear-gradient(90deg, rgba(255,115,141,0.3) 0%, #FF738D 100%)\" />\r\n      </div>\r\n      <div class=\"adopt_situation_distribution_text\">采用情况分布</div>\r\n      <div class=\"adopt_situation_distribution_charts\">\r\n        <PieChart id=\"adoptSituationDistribution\" :chart-data=\"adoptSituationDistribution\" :radius=\"['35%', '60%']\" />\r\n      </div>\r\n    </div>\r\n    <!-- 批示情况 -->\r\n    <div class=\"instructions_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">批示情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"instructions_list\">\r\n        <div class=\"instructions_item\" v-for=\"(item, index) in instructions\" :key=\"index\"\r\n          :style=\"`background: ${item.bg}`\">\r\n          <div class=\"instructions_item_value\" :style=\"`color: ${item.color}`\">{{ item.value }}</div>\r\n          <div class=\"instructions_item_label\">{{ item.label }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 各单位上报与采用情况 -->\r\n    <div class=\"report_adopt_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">各单位上报与采用情况</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"report_adopt_list\">\r\n        <DoubleBarChart id=\"party_double_line\" :data=\"partyData\" :legend=\"['报送篇数', '采用篇数']\" :color=\"[\r\n          ['#56A0FF', 'rgba(86,160,255,0.1)'],\r\n          ['#FF738D', 'rgba(255,115,141,0.1)']\r\n        ]\" title=\"八大党派及民主工商联\" style=\"height: 260px;\" />\r\n        <DoubleBarChart id=\"district_double_line\" :data=\"districtData\" :legend=\"['报送篇数', '采用篇数']\" :color=\"[\r\n          ['#56A0FF', 'rgba(86,160,255,0.1)'],\r\n          ['#FF738D', 'rgba(255,115,141,0.1)']\r\n        ]\" title=\"各区市\" style=\"height: 260px;\" />\r\n        <DoubleBarChart id=\"office_double_line\" :data=\"officeData\" :legend=\"['报送篇数', '采用篇数']\" :color=\"[\r\n          ['#56A0FF', 'rgba(86,160,255,0.1)'],\r\n          ['#FF738D', 'rgba(255,115,141,0.1)']\r\n        ]\" title=\"各单位办公室\" style=\"height: 260px;\" />\r\n      </div>\r\n    </div>\r\n    <!-- 个人报送与采用情况 -->\r\n    <div class=\"submit_adopt_situation_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">个人报送与采用情况</span>\r\n        </div>\r\n        <div class=\"header_right\" @click=\"openMore('notice')\">\r\n          <span class=\"header_right_more\">查看全部</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"submit_adopt_situation_list\">\r\n        <div class=\"submit_adopt_table\">\r\n          <div class=\"submit_adopt_table_header\">\r\n            <span class=\"party-header-column\">姓名</span>\r\n            <span class=\"count-header-column\">报送件数</span>\r\n            <span class=\"count-header-column\">采用件数</span>\r\n          </div>\r\n          <div class=\"submit_adopt_table_row\" v-for=\"(item, idx) in submitAdoptSituationData\" :key=\"item.name\"\r\n            :class=\"{ 'row-alt': idx % 2 === 1 }\">\r\n            <span class=\"party-column name-cell\">\r\n              <img :src=\"item.avatar\" class=\"avatar\" />\r\n              <span class=\"name\">{{ item.name }}</span>\r\n            </span>\r\n            <span class=\"count-column\">{{ item.submit }}</span>\r\n            <span class=\"count-column\">{{ item.adopt }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 类别分析 -->\r\n    <div class=\"category_analysis_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">类别分析</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"category_analysis_list\">\r\n        <barChart id=\"categoryAnalysis\" :data=\"categoryData\" :color=\"['#559FFF', 'rgba(85,159,255,0.3)']\"\r\n          style=\"height:200px\" />\r\n      </div>\r\n    </div>\r\n    <!-- 热词分析 -->\r\n    <div class=\"hot_words_analysis_box\">\r\n      <div class=\"header_box\">\r\n        <div class=\"header_left\">\r\n          <span class=\"header_left_line\"></span>\r\n          <span class=\"header_left_title\">热词分析</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"hot_words_analysis_list\">\r\n        <wordCloudEcharts id=\"wordcloud\" :wordList=\"wordCloudData\"\r\n          :colorList=\"['#1890FF', '#FF6B35', '#52C41A', '#722ED1', '#1890FF', '#FF69B4', '#52C41A', '#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']\"\r\n          :sizeRange=\"[2, 10]\" />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { toRefs, reactive } from 'vue'\r\nimport PublicOpinionOverallSituationChart from './echartsComponent/PublicOpinionOverallSituationChart.vue'\r\nimport ProgressBarChart from './echartsComponent/ProgressBarChart.vue'\r\nimport PieChart from './echartsComponent/PieChart.vue'\r\nimport DoubleBarChart from './echartsComponent/DoubleBarChart.vue'\r\nimport barChart from './echartsComponent/barChart.vue'\r\nimport wordCloudEcharts from './echartsComponent/wordCloudEcharts.vue'\r\nexport default {\r\n  name: 'NetworkPolitics',\r\n  components: { PublicOpinionOverallSituationChart, ProgressBarChart, PieChart, DoubleBarChart, barChart, wordCloudEcharts },\r\n  setup () {\r\n    const data = reactive({\r\n      totalCount: 50,\r\n      adoptedCount: 20,\r\n      committeeMember: { submitPercent: 60, submitNum: 345, adoptSituationPercent: 42, adoptSituationNum: 102 },\r\n      unit: { submitPercent: 60, submitNum: 345, adoptSituationPercent: 42, adoptSituationNum: 102 },\r\n      adoptSituationDistribution: [\r\n        { name: '市政协采用', value: 135, percentage: '40%', color: '#4A90E2' },\r\n        { name: '省政协采用', value: 131, percentage: '30%', color: '#4CD9C0' },\r\n        { name: '全国政协采用', value: 126, percentage: '20%', color: '#F56A6A' }\r\n      ],\r\n      instructions: [\r\n        { label: '市政协批示', value: '135', bg: '#EFF6FF', color: '#3B91FB' },\r\n        { label: '省政协批示', value: '131', bg: '#FDF8F0', color: '#EAB308' },\r\n        { label: '全国政协批示', value: '126', bg: '#F0FDF4', color: '#43DDBB' }\r\n      ],\r\n      partyData: [\r\n        { name: '民革', value1: 102, value2: 80 },\r\n        { name: '民盟', value1: 56, value2: 30 },\r\n        { name: '民建', value1: 120, value2: 75 },\r\n        { name: '民进', value1: 34, value2: 20 },\r\n        { name: '农工', value1: 89, value2: 60 },\r\n        { name: '致公', value1: 95, value2: 70 },\r\n        { name: '九三', value1: 80, value2: 55 },\r\n        { name: '工商联', value1: 45, value2: 25 }\r\n      ],\r\n      districtData: [\r\n        { name: '市南', value1: 55, value2: 13 },\r\n        { name: '市北', value1: 20, value2: 6 },\r\n        { name: '李沧', value1: 35, value2: 10 },\r\n        { name: '崂山', value1: 18, value2: 4 },\r\n        { name: '黄岛', value1: 52, value2: 12 },\r\n        { name: '城阳', value1: 10, value2: 2 },\r\n        { name: '即墨', value1: 25, value2: 5 },\r\n        { name: '胶州', value1: 30, value2: 7 },\r\n        { name: '平度', value1: 12, value2: 3 },\r\n        { name: '莱西', value1: 8, value2: 1 }\r\n      ],\r\n      officeData: [\r\n        { name: '委员', value1: 8, value2: 2 },\r\n        { name: '提案', value1: 2, value2: 1 },\r\n        { name: '经济', value1: 5, value2: 1 },\r\n        { name: '农业', value1: 18, value2: 6 },\r\n        { name: '人口环', value1: 50, value2: 15 },\r\n        { name: '教科', value1: 3, value2: 1 },\r\n        { name: '社法', value1: 2, value2: 1 },\r\n        { name: '港澳', value1: 1, value2: 0 },\r\n        { name: '文史', value1: 2, value2: 1 },\r\n        { name: '民宗', value1: 1, value2: 0 },\r\n        { name: '财经', value1: 50, value2: 15 }\r\n      ],\r\n      submitAdoptSituationData: [\r\n        { avatar: require('@/assets/img/largeScreen/man.png'), name: '张伟', submit: 12, adopt: 3 },\r\n        { avatar: require('@/assets/img/largeScreen/woman.png'), name: '刘芳', submit: 11, adopt: 2 },\r\n        { avatar: require('@/assets/img/largeScreen/man.png'), name: '陈明', submit: 11, adopt: 2 },\r\n        { avatar: require('@/assets/img/largeScreen/man.png'), name: '林小华', submit: 11, adopt: 2 },\r\n        { avatar: require('@/assets/img/largeScreen/man.png'), name: '赵天宇', submit: 10, adopt: 1 },\r\n        { avatar: require('@/assets/img/largeScreen/woman.png'), name: '吴静怡', submit: 10, adopt: 1 },\r\n        { avatar: require('@/assets/img/largeScreen/man.png'), name: '黄浩然', submit: 8, adopt: 1 },\r\n        { avatar: require('@/assets/img/largeScreen/woman.png'), name: '周梦琪', submit: 7, adopt: 1 }\r\n      ],\r\n      categoryData: [\r\n        { name: '社会', value: 115 },\r\n        { name: '政治', value: 140 },\r\n        { name: '经济', value: 60 },\r\n        { name: '文化', value: 115 },\r\n        { name: '生态文明', value: 125 }\r\n      ],\r\n      wordCloudData: [\r\n        { name: '乡村振兴', value: 180 },\r\n        { name: '就业优先', value: 165 },\r\n        { name: '科技创新', value: 150 },\r\n        { name: '改革开放', value: 135 },\r\n        { name: '依法治国', value: 120 },\r\n        { name: '教育人才', value: 105 },\r\n        { name: '社会保障', value: 90 },\r\n        { name: '热词', value: 75 },\r\n        { name: '绿色发展', value: 60 },\r\n        { name: '数字中国', value: 45 },\r\n        { name: '共同富裕', value: 40 },\r\n        { name: '文化自信', value: 35 },\r\n        { name: '国家安全', value: 30 },\r\n        { name: '人民至上', value: 25 },\r\n        { name: '中国式现代化', value: 20 }\r\n      ]\r\n    })\r\n\r\n    return { ...toRefs(data) }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.PublicOpinion {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .header_box {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 15px 15px 0 15px;\r\n\r\n    .header_left {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_left_line {\r\n        width: 3px;\r\n        height: 14px;\r\n        background: #007AFF;\r\n      }\r\n\r\n      .header_left_title {\r\n        font-weight: bold;\r\n        font-size: 15px;\r\n        color: #222222;\r\n        margin-left: 8px;\r\n        font-family: Source Han Serif SC, Source Han Serif SC;\r\n      }\r\n    }\r\n\r\n    .header_right {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .header_right_text {\r\n        font-weight: 400;\r\n        font-size: 12px;\r\n        color: #999999;\r\n      }\r\n\r\n      .header_right_more {\r\n        font-size: 14px;\r\n        color: #0271E3;\r\n        border-radius: 14px;\r\n        border: 1px solid #0271E3;\r\n        padding: 3px 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .overall_situation_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .overall_situation_list {\r\n      height: 160px;\r\n    }\r\n  }\r\n\r\n  .adopt_situation_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .adopt_situation_list {\r\n      padding: 5px 18px 18px 18px;\r\n    }\r\n\r\n    .adopt_situation_distribution_text {\r\n      font-size: 15px;\r\n      color: #222222;\r\n      font-family: Source Han Serif SC, Source Han Serif SC;\r\n      padding-left: 18px;\r\n    }\r\n\r\n    .adopt_situation_distribution_charts {\r\n      width: 100%;\r\n      height: 260px;\r\n      margin-top: 10px;\r\n    }\r\n  }\r\n\r\n  .instructions_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .instructions_list {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 18px;\r\n\r\n      .instructions_item {\r\n        width: 93px;\r\n        height: 90px;\r\n        border-radius: 4px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        .instructions_item_value {\r\n          font-size: 19px;\r\n        }\r\n\r\n        .instructions_item_label {\r\n          font-size: 13px;\r\n          color: #666666;\r\n          margin-top: 14px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .report_adopt_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n  }\r\n\r\n  .submit_adopt_situation_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .submit_adopt_situation_list {\r\n      margin-top: 15px;\r\n\r\n      .submit_adopt_table {\r\n        width: 100%;\r\n        background: #fff;\r\n\r\n        .submit_adopt_table_header,\r\n        .submit_adopt_table_row {\r\n          display: flex;\r\n          align-items: center;\r\n          padding: 12px 15px;\r\n          border-bottom: 1px solid #f0f0f0;\r\n\r\n          &:last-child {\r\n            border-bottom: none;\r\n          }\r\n\r\n          .party-header-column {\r\n            flex: 2;\r\n            text-align: left;\r\n            font-size: 14px;\r\n            color: #999;\r\n          }\r\n\r\n          .count-header-column {\r\n            flex: 1;\r\n            text-align: center;\r\n            font-size: 14px;\r\n            color: #999;\r\n          }\r\n\r\n          .party-column {\r\n            flex: 2;\r\n            text-align: left;\r\n            font-size: 14px;\r\n            color: #333;\r\n          }\r\n\r\n          .count-column {\r\n            flex: 1;\r\n            text-align: center;\r\n            font-size: 14px;\r\n            color: #333;\r\n          }\r\n\r\n          .name-cell {\r\n            display: flex;\r\n            align-items: center;\r\n\r\n            .avatar {\r\n              width: 28px;\r\n              height: 28px;\r\n              border-radius: 50%;\r\n              margin-right: 8px;\r\n              object-fit: cover;\r\n              border: 1px solid #e0e0e0;\r\n            }\r\n\r\n            .name {\r\n              font-size: 14px;\r\n              color: #333;\r\n            }\r\n          }\r\n        }\r\n\r\n        .submit_adopt_table_header {\r\n          background: #F1F8FF;\r\n          font-weight: 600;\r\n          color: #222;\r\n          font-size: 14px;\r\n        }\r\n\r\n        .submit_adopt_table_row {\r\n          background: #fff;\r\n          color: #333;\r\n          font-size: 14px;\r\n\r\n          &.row-alt {\r\n            background: #F1F8FF;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .category_analysis_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .category_analysis_list {\r\n      padding: 18px;\r\n    }\r\n  }\r\n\r\n  .hot_words_analysis_box {\r\n    background: #FFFFFF;\r\n    border-radius: 6px;\r\n    margin: 12px 0;\r\n\r\n    .hot_words_analysis_list {}\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AA0IA,SAASA,MAAM,EAAEC,QAAO,QAAS,KAAI;AACrC,OAAOC,kCAAiC,MAAO,2DAA0D;AACzG,OAAOC,gBAAe,MAAO,yCAAwC;AACrE,OAAOC,QAAO,MAAO,iCAAgC;AACrD,OAAOC,cAAa,MAAO,uCAAsC;AACjE,OAAOC,QAAO,MAAO,iCAAgC;AACrD,OAAOC,gBAAe,MAAO,yCAAwC;AACrE,eAAe;EACbC,IAAI,EAAE,iBAAiB;EACvBC,UAAU,EAAE;IAAEP,kCAAkC;IAAEC,gBAAgB;IAAEC,QAAQ;IAAEC,cAAc;IAAEC,QAAQ;IAAEC;EAAiB,CAAC;EAC1HG,KAAIA,CAAA,EAAK;IACP,MAAMC,IAAG,GAAIV,QAAQ,CAAC;MACpBW,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE,EAAE;MAChBC,eAAe,EAAE;QAAEC,aAAa,EAAE,EAAE;QAAEC,SAAS,EAAE,GAAG;QAAEC,qBAAqB,EAAE,EAAE;QAAEC,iBAAiB,EAAE;MAAI,CAAC;MACzGC,IAAI,EAAE;QAAEJ,aAAa,EAAE,EAAE;QAAEC,SAAS,EAAE,GAAG;QAAEC,qBAAqB,EAAE,EAAE;QAAEC,iBAAiB,EAAE;MAAI,CAAC;MAC9FE,0BAA0B,EAAE,CAC1B;QAAEZ,IAAI,EAAE,OAAO;QAAEa,KAAK,EAAE,GAAG;QAAEC,UAAU,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAU,CAAC,EAClE;QAAEf,IAAI,EAAE,OAAO;QAAEa,KAAK,EAAE,GAAG;QAAEC,UAAU,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAU,CAAC,EAClE;QAAEf,IAAI,EAAE,QAAQ;QAAEa,KAAK,EAAE,GAAG;QAAEC,UAAU,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAU,EACnE;MACDC,YAAY,EAAE,CACZ;QAAEC,KAAK,EAAE,OAAO;QAAEJ,KAAK,EAAE,KAAK;QAAEK,EAAE,EAAE,SAAS;QAAEH,KAAK,EAAE;MAAU,CAAC,EACjE;QAAEE,KAAK,EAAE,OAAO;QAAEJ,KAAK,EAAE,KAAK;QAAEK,EAAE,EAAE,SAAS;QAAEH,KAAK,EAAE;MAAU,CAAC,EACjE;QAAEE,KAAK,EAAE,QAAQ;QAAEJ,KAAK,EAAE,KAAK;QAAEK,EAAE,EAAE,SAAS;QAAEH,KAAK,EAAE;MAAU,EAClE;MACDI,SAAS,EAAE,CACT;QAAEnB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,GAAG;QAAEC,MAAM,EAAE;MAAG,CAAC,EACvC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAG,CAAC,EACtC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,GAAG;QAAEC,MAAM,EAAE;MAAG,CAAC,EACvC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAG,CAAC,EACtC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAG,CAAC,EACtC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAG,CAAC,EACtC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAG,CAAC,EACtC;QAAErB,IAAI,EAAE,KAAK;QAAEoB,MAAM,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAG,EACvC;MACDC,YAAY,EAAE,CACZ;QAAEtB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAG,CAAC,EACtC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAE,CAAC,EACrC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAG,CAAC,EACtC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAE,CAAC,EACrC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAG,CAAC,EACtC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAE,CAAC,EACrC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAE,CAAC,EACrC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAE,CAAC,EACrC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAE,CAAC,EACrC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,EACpC;MACDE,UAAU,EAAE,CACV;QAAEvB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC,EACpC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC,EACpC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC,EACpC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAE,CAAC,EACrC;QAAErB,IAAI,EAAE,KAAK;QAAEoB,MAAM,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAG,CAAC,EACvC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC,EACpC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC,EACpC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC,EACpC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC,EACpC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC,EACpC;QAAErB,IAAI,EAAE,IAAI;QAAEoB,MAAM,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAG,EACtC;MACDG,wBAAwB,EAAE,CACxB;QAAEC,MAAM,EAAEC,OAAO,CAAC,kCAAkC,CAAC;QAAE1B,IAAI,EAAE,IAAI;QAAE2B,MAAM,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAE,CAAC,EACzF;QAAEH,MAAM,EAAEC,OAAO,CAAC,oCAAoC,CAAC;QAAE1B,IAAI,EAAE,IAAI;QAAE2B,MAAM,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAE,CAAC,EAC3F;QAAEH,MAAM,EAAEC,OAAO,CAAC,kCAAkC,CAAC;QAAE1B,IAAI,EAAE,IAAI;QAAE2B,MAAM,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAE,CAAC,EACzF;QAAEH,MAAM,EAAEC,OAAO,CAAC,kCAAkC,CAAC;QAAE1B,IAAI,EAAE,KAAK;QAAE2B,MAAM,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAE,CAAC,EAC1F;QAAEH,MAAM,EAAEC,OAAO,CAAC,kCAAkC,CAAC;QAAE1B,IAAI,EAAE,KAAK;QAAE2B,MAAM,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAE,CAAC,EAC1F;QAAEH,MAAM,EAAEC,OAAO,CAAC,oCAAoC,CAAC;QAAE1B,IAAI,EAAE,KAAK;QAAE2B,MAAM,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAE,CAAC,EAC5F;QAAEH,MAAM,EAAEC,OAAO,CAAC,kCAAkC,CAAC;QAAE1B,IAAI,EAAE,KAAK;QAAE2B,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAE,CAAC,EACzF;QAAEH,MAAM,EAAEC,OAAO,CAAC,oCAAoC,CAAC;QAAE1B,IAAI,EAAE,KAAK;QAAE2B,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAE,EAC3F;MACDC,YAAY,EAAE,CACZ;QAAE7B,IAAI,EAAE,IAAI;QAAEa,KAAK,EAAE;MAAI,CAAC,EAC1B;QAAEb,IAAI,EAAE,IAAI;QAAEa,KAAK,EAAE;MAAI,CAAC,EAC1B;QAAEb,IAAI,EAAE,IAAI;QAAEa,KAAK,EAAE;MAAG,CAAC,EACzB;QAAEb,IAAI,EAAE,IAAI;QAAEa,KAAK,EAAE;MAAI,CAAC,EAC1B;QAAEb,IAAI,EAAE,MAAM;QAAEa,KAAK,EAAE;MAAI,EAC5B;MACDiB,aAAa,EAAE,CACb;QAAE9B,IAAI,EAAE,MAAM;QAAEa,KAAK,EAAE;MAAI,CAAC,EAC5B;QAAEb,IAAI,EAAE,MAAM;QAAEa,KAAK,EAAE;MAAI,CAAC,EAC5B;QAAEb,IAAI,EAAE,MAAM;QAAEa,KAAK,EAAE;MAAI,CAAC,EAC5B;QAAEb,IAAI,EAAE,MAAM;QAAEa,KAAK,EAAE;MAAI,CAAC,EAC5B;QAAEb,IAAI,EAAE,MAAM;QAAEa,KAAK,EAAE;MAAI,CAAC,EAC5B;QAAEb,IAAI,EAAE,MAAM;QAAEa,KAAK,EAAE;MAAI,CAAC,EAC5B;QAAEb,IAAI,EAAE,MAAM;QAAEa,KAAK,EAAE;MAAG,CAAC,EAC3B;QAAEb,IAAI,EAAE,IAAI;QAAEa,KAAK,EAAE;MAAG,CAAC,EACzB;QAAEb,IAAI,EAAE,MAAM;QAAEa,KAAK,EAAE;MAAG,CAAC,EAC3B;QAAEb,IAAI,EAAE,MAAM;QAAEa,KAAK,EAAE;MAAG,CAAC,EAC3B;QAAEb,IAAI,EAAE,MAAM;QAAEa,KAAK,EAAE;MAAG,CAAC,EAC3B;QAAEb,IAAI,EAAE,MAAM;QAAEa,KAAK,EAAE;MAAG,CAAC,EAC3B;QAAEb,IAAI,EAAE,MAAM;QAAEa,KAAK,EAAE;MAAG,CAAC,EAC3B;QAAEb,IAAI,EAAE,MAAM;QAAEa,KAAK,EAAE;MAAG,CAAC,EAC3B;QAAEb,IAAI,EAAE,QAAQ;QAAEa,KAAK,EAAE;MAAG;IAEhC,CAAC;IAED,OAAO;MAAE,GAAGrB,MAAM,CAACW,IAAI;IAAE;EAC3B;AACF", "ignoreList": []}]}