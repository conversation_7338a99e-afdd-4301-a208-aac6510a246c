<template>
  <div class="contactServiceExpertsList">
    <van-sticky>
      <van-nav-bar v-if="hasApi" title="联系服务专家" left-text="" left-arrow @click-left="onClickLeft" />
    </van-sticky>
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" offset="52" @load="onLoad">
        <ul class="vue_newslist_box">
          <div class="expert_item" v-for="(item, index) in dataList" :key="index" @click="openDetails(item)">
            <img class="expert_avatar" src="@/assets/img/icon_user_experts_headimg.png" alt="avatar" />
            <div class="expert_info">
              <div class="expert_name">{{ item.title }}</div>
              <!-- <div class="expert_title" v-html="item.content"></div> -->
            </div>
          </div>
        </ul>
      </van-list>
    </van-pull-refresh>
  </div>
</template>
<script>
import { useRouter, useRoute } from 'vue-router'
import { inject, reactive, toRefs } from 'vue'
import { NavBar, Sticky, Tag } from 'vant'
export default {
  name: 'contactServiceExpertsList',
  components: {
    [Tag.name]: Tag,
    [NavBar.name]: NavBar,
    [Sticky.name]: Sticky
  },
  setup () {
    const router = useRouter()
    const route = useRoute()
    const $api = inject('$api')
    const $appTheme = inject('$appTheme')
    const $general = inject('$general')
    const dayjs = require('dayjs')
    const $isShowHead = inject('$isShowHead')
    const data = reactive({
      hasApi: false,
      appTheme: $appTheme,
      isShowHead: $isShowHead,
      title: route.query.title || '',
      user: JSON.parse(sessionStorage.getItem('user')),
      loading: false,
      finished: false,
      refreshing: false,
      pageNo: 0,
      pageSize: 10,
      total: 0,
      dataList: []
    })
    if (data.title) {
      document.title = data.title
    }
    if (typeof (api) === 'undefined') {
      data.hasApi = false
    } else {
      data.hasApi = true
    }
    const search = () => {
      data.pageNo = 1
      data.dataList = []
      data.loading = true
      data.finished = false
      noticeList()
    }
    const onRefresh = () => {
      setTimeout(() => {
        data.pageNo = 1
        data.dataList = []
        data.loading = true
        data.finished = false
        noticeList()
      }, 520)
    }
    const onLoad = () => {
      data.pageNo = data.pageNo + 1
      noticeList()
    }
    // 列表请求
    const noticeList = async () => {
      const res = await $api.ImportantWork.list({
        pageNo: data.pageNo,
        pageSize: 10,
        columnId: route.query.columnId
      })
      var { data: list, total } = res
      data.dataList = data.dataList.concat(list)
      data.loading = false
      data.refreshing = false
      // 数据全部加载完成
      if (data.dataList.length >= total) {
        data.finished = true
      }
    }
    const openDetails = (row) => {
      router.push({ name: 'contactServiceExpertsDetails', query: { id: row.id } })
    }
    const onClickLeft = () => {
      history.back()
    }
    return { ...toRefs(data), dayjs, search, onRefresh, onLoad, openDetails, $general, onClickLeft }
  }
}
</script>
<style lang="less">
.contactServiceExpertsList {
  width: 100%;
  min-height: 100vh;
  background: #fff;

  .vue_newslist_box {
    padding: 5px 15px;

    .expert_item {
      display: flex;
      align-items: flex-start;
      position: relative;
      padding: 15px 0 15px 0;
      border-bottom: 1px solid #f0f0f0;

      .expert_avatar {
        width: 19px;
        height: 17px;
        margin-right: 12px;
        object-fit: cover;
        margin-top: 5px;
      }

      .expert_info {
        flex: 1;

        .expert_name {
          font-family: Source Han Serif SC, Source Han Serif SC;
          font-weight: bold;
          font-size: 16px;
          color: #333333;
        }

        .expert_title {
          font-family: Source Han Serif SC, Source Han Serif SC;
          font-weight: 500;
          font-size: 14px;
          color: #666666;
          margin-top: 6px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

.van-nav-bar {
  padding-top: 35px;
  background: rgb(2, 113, 227);

  .van-icon {
    color: #fff;
  }

  .van-nav-bar__title {
    font-size: 17px;
    color: #fff;
    font-family: simplified;
  }
}
</style>
