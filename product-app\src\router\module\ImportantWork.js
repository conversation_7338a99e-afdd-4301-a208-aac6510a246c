const ImportantWork = () => import('@/views/ImportantWork/ImportantWork')
const WorkNoticeList = () => import('@/views/ImportantWork/WorkNotice/WorkNoticeList')
const WorkNoticeDetails = () => import('@/views/ImportantWork/WorkNotice/WorkNoticeDetails')
const WorkTeamList = () => import('@/views/ImportantWork/WorkTeam/WorkTeamList')
const WorkTeamColumnList = () => import('@/views/ImportantWork/WorkTeam/WorkTeamColumnList')
const WorkTeamDetails = () => import('@/views/ImportantWork/WorkTeam/WorkTeamDetails')
const MajorProjectsList = () => import('@/views/ImportantWork/MajorProjects/MajorProjectsList')
const MajorProjectsColumnList = () => import('@/views/ImportantWork/MajorProjects/MajorProjectsColumnList')
const MajorProjectsDetails = () => import('@/views/ImportantWork/MajorProjects/MajorProjectsDetails')
const contactCompanyList = () => import('@/views/ImportantWork/contactCompany/contactCompanyList')
const contactCompanyColumnList = () => import('@/views/ImportantWork/contactCompany/contactCompanyColumnList')
const contactCompanyDetails = () => import('@/views/ImportantWork/contactCompany/contactCompanyDetails')
const villageContactPointList = () => import('@/views/ImportantWork/villageContactPoint/villageContactPointList')
const villageContactPointDetails = () => import('@/views/ImportantWork/villageContactPoint/villageContactPointDetails')
const contactServiceExpertsList = () => import('@/views/ImportantWork/contactServiceExperts/contactServiceExpertsList')
const contactServiceExpertsDetails = () => import('@/views/ImportantWork/contactServiceExperts/contactServiceExpertsDetails')
const attractInvestmentList = () => import('@/views/ImportantWork/attractInvestment/attractInvestmentList')
const attractInvestmentColumnList = () => import('@/views/ImportantWork/attractInvestment/attractInvestmentColumnList')
const attractInvestmentDetails = () => import('@/views/ImportantWork/attractInvestment/attractInvestmentDetails')
const pdfFilePreview = () => import('@/views/pdfFilePreview/pdfFilePreview')
const Networkpolitics = [
  {
    path: '/ImportantWork',
    name: 'ImportantWork',
    component: ImportantWork,
    meta: {
      title: '重点工作首页',
      keepAlive: true
    }
  },
  {
    path: '/WorkNoticeList',
    name: 'WorkNoticeList',
    component: WorkNoticeList,
    meta: {
      title: '重点工作的工作通知列表',
      keepAlive: true
    }
  },
  {
    path: '/WorkNoticeDetails',
    name: 'WorkNoticeDetails',
    component: WorkNoticeDetails,
    meta: {
      title: '重点工作的工作通知详情',
      keepAlive: true
    }
  },
  {
    path: '/WorkTeamList',
    name: 'WorkTeamList',
    component: WorkTeamList,
    meta: {
      title: '重点工作的工作专班列表',
      keepAlive: true
    }
  },
  {
    path: '/WorkTeamColumnList',
    name: 'WorkTeamColumnList',
    component: WorkTeamColumnList,
    meta: {
      title: '重点工作的工作专班栏目列表',
      keepAlive: true
    }
  },
  {
    path: '/WorkTeamDetails',
    name: 'WorkTeamDetails',
    component: WorkTeamDetails,
    meta: {
      title: '重点工作的工作专班详情',
      keepAlive: true
    }
  },
  {
    path: '/MajorProjectsList',
    name: 'MajorProjectsList',
    component: MajorProjectsList,
    meta: {
      title: '重点工作的顶格推进重大项目列表',
      keepAlive: true
    }
  },
  {
    path: '/MajorProjectsColumnList',
    name: 'MajorProjectsColumnList',
    component: MajorProjectsColumnList,
    meta: {
      title: '重点工作的顶格推进重大项目栏目列表',
      keepAlive: true
    }
  },
  {
    path: '/MajorProjectsDetails',
    name: 'MajorProjectsDetails',
    component: MajorProjectsDetails,
    meta: {
      title: '重点工作的顶格推进重大项目详情',
      keepAlive: true
    }
  },
  {
    path: '/contactCompanyList',
    name: 'contactCompanyList',
    component: contactCompanyList,
    meta: {
      title: '重点工作的联系企业列表',
      keepAlive: true
    }
  },
  {
    path: '/contactCompanyColumnList',
    name: 'contactCompanyColumnList',
    component: contactCompanyColumnList,
    meta: {
      title: '重点工作的联系企业栏目列表',
      keepAlive: true
    }
  },
  {
    path: '/contactCompanyDetails',
    name: 'contactCompanyDetails',
    component: contactCompanyDetails,
    meta: {
      title: '重点工作的联系企业详情',
      keepAlive: true
    }
  },
  {
    path: '/villageContactPointList',
    name: 'villageContactPointList',
    component: villageContactPointList,
    meta: {
      title: '重点工作的乡村振兴联系点列表',
      keepAlive: true
    }
  },
  {
    path: '/villageContactPointDetails',
    name: 'villageContactPointDetails',
    component: villageContactPointDetails,
    meta: {
      title: '重点工作的乡村振兴联系点详情',
      keepAlive: true
    }
  },
  {
    path: '/contactServiceExpertsList',
    name: 'contactServiceExpertsList',
    component: contactServiceExpertsList,
    meta: {
      title: '重点工作的联系服务专家列表',
      keepAlive: true
    }
  },
  {
    path: '/contactServiceExpertsDetails',
    name: 'contactServiceExpertsDetails',
    component: contactServiceExpertsDetails,
    meta: {
      title: '重点工作的联系服务专家详情',
      keepAlive: true
    }
  },
  {
    path: '/attractInvestmentList',
    name: 'attractInvestmentList',
    component: attractInvestmentList,
    meta: {
      title: '重点工作的招商引资情况统计列表',
      keepAlive: true
    }
  },
  {
    path: '/attractInvestmentColumnList',
    name: 'attractInvestmentColumnList',
    component: attractInvestmentColumnList,
    meta: {
      title: '重点工作的招商引资情况统计栏目列表',
      keepAlive: true
    }
  },
  {
    path: '/attractInvestmentDetails',
    name: 'attractInvestmentDetails',
    component: attractInvestmentDetails,
    meta: {
      title: '重点工作的招商引资情况统计详情',
      keepAlive: true
    }
  },
  {
    path: '/pdfFilePreview',
    name: 'pdfFilePreview',
    component: pdfFilePreview,
    meta: {
      title: '附件预览',
      keepAlive: true
    }
  }
]
export default Networkpolitics
